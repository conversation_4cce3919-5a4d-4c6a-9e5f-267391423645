NODE_ENV=production
VUE_APP_ENV=uat
VUE_APP_QIANKUN=true
VUE_APP_API_BASE_URL=https://api-uat.isolareye.com/solareye-system
VUE_APP_CAS_BASE_URL=http://localhost:8888/cas
VUE_APP_API_ERP_URL=https://api-uat.isolareye.com/isolar-erp
VUE_APP_API_MSG_URL=https://api-uat.isolareye.com/solareye-msg
VUE_APP_API_CODE_URL=https://app.isolareye.com/uat/app.png
VUE_APP_API_SEC_URL=https://api-uat.isolareye.com/solareyetwo
VUE_APP_API_BI_URL=https://api-uat.isolareye.com/isolarerpbi
VUE_APP_AIGC_URL=https://aigc-uat.isolareye.com/#/index
VUE_APP_AIGC_API_URL=https://api-uat.isolareye.com/solareye-aigc
VUE_APP_Health_BASE_URL=https://api-uat.isolareye.com/isolar-health
VUE_APP_BI_BASE_URL=https://api-uat.isolareye.com/isolarerpbi
VUE_APP_DING_BASE_URL=https://api-uat.isolareye.com/dingding

VUE_APP_API_SHOP_URL = https://api-uat.isolareye.com/solareye-shop
VUE_APP_TANGO_BASE_URL=https://api-uat.isolareye.com/solareyecare
VUE_APP_OSS_FILE_URL=https://api-uat.isolareye.com/solareyemc/oss/v1
VUE_APP_DM_BASE_URL=https://api-uat.isolareye.com/solareyedm
VUE_APP_RDP_URL=https://api-uat.isolareye.com/RDP-SERVER
# 微应用列表必须VUE_APP_SUB_开头,solarCare为子应用的项目名称,也是子应用的路由父路径
VUE_APP_SUB_solarCare = '/solarCare/'