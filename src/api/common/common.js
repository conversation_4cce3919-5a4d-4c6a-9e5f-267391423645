import { axios } from '@/utils/erp-request';
import { getAction } from '@api/manage';
const url = process.env.VUE_APP_API_ERP_URL;

// 图片预览
export function getComFilePreview (data) {
  return axios({
    url: '/equipment/v1/archives/filePreview',
    method: 'post',
    data
  });
}

// 设备树-最新
export const getComDeviceTypeTree = (params) => getAction(url + '/device-type/tree', params);
// 设备类型 => 设备名称
export const getListCompleteDevice = (params) => getAction(url + '/device/listCompleteDevice', params);
// 跳转户用大厅获取最新token
export const getSolareyeToken = (params) => getAction(url + '/system/user/getSolareyeToken', params);
