import {
  axios
} from '@/utils/request';
const url = process.env.VUE_APP_API_BI_URL;

// 查询列表
export function getAuditReduceInfo (data) {
  return axios({
    url: url + '/reduce/workflow/reduceList',
    method: 'post',
    data
  });
}
// 查询头部参数
export function getReduceSum (data) {
  return axios({
    url: url + '/reduce/workflow/reduceSum',
    method: 'post',
    data
  });
}
// 查询详情
export function getReduceDetail (data) {
  return axios({
    url: url + '/reduce/workflow/reduceDetail',
    method: 'post',
    data
  });
}
// 提交
export function reduceSubmit (data) {
  return axios({
    url: url + '/reduce/workflow/reduceSubmit',
    method: 'post',
    data
  });
}
// 审批
export function reduceAduit (data) {
  return axios({
    url: url + '/reduce/workflow/reduceAduit',
    method: 'post',
    data
  });
}
// 删除
export function deleteReduceAduit (data) {
  return axios({
    url: url + '/reduce/workflow/deleteWorkFlow',
    method: 'post',
    data
  });
}

// 内部核减电量删除
export function manageDeleteApi (data) {
  return axios({
    url: url + '/reduce/workflow/manageDelete',
    method: 'post',
    data
  });
}
