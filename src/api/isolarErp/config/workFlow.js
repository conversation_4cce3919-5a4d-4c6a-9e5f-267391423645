import { axios } from '@/utils/erp-request';

// 查询列表数据
export function queryFlow (data) {
  return axios({
    url: '/workflow/v1/listModels',
    method: 'POST',
    data
  });
}

// 创建流程模板
export function createFlowModel (data) {
  return axios({
    url: '/workflow/v1/model/createModel',
    method: 'POST',
    data
  });
}

// 部署
export function deployProcess (data) {
  return axios({
    url: '/workflow/v1/deployProcess',
    method: 'POST',
    data
  });
}

// 显示流程图
export function showProcessDiagram (data) {
  return axios({
    url: '/workflow/v1/showProcessDiagram',
    method: 'POST',
    data
  });
}
