import { axios } from '@/utils/erp-request';

export function allStationData (data) {
  return axios({
    url: 'runanalysis/v1/poweranalysis/allStationData',
    method: 'post',
    data
  });
}
export function allStationComment (data) {
  return axios({
    url: 'runanalysis/v1/poweranalysis/allStationComment',
    method: 'post',
    data
  });
}
export function elecAnalysisData (data) {
  return axios({
    url: 'runanalysis/v1/poweranalysis/elecAnalysisData',
    method: 'post',
    data
  });
}
export function resourceAnalysisData (data) {
  return axios({
    url: 'runanalysis/v1/poweranalysis/resourceAnalysisData',
    method: 'post',
    data
  });
}
export function scoreQuery (data) {
  return axios({
    url: 'runanalysis/v1/poweranalysis/scoreQuery',
    method: 'post',
    data
  });
}
export function scoreTable (data) {
  return axios({
    url: 'runanalysis/v1/poweranalysis/scoreTable',
    method: 'post',
    data
  });
}
export function scoreLine (data) {
  return axios({
    url: 'runanalysis/v1/poweranalysis/scoreLine',
    method: 'post',
    data
  });
}
// 导出表格
export function exportTable (data) {
  return axios({
    url: 'runanalysis/v1/poweranalysis/exportTable',
    method: 'post',
    data
  });
}

/*
  获取区域下拉选(没有层级关系)
 */
export function allArea (data) {
  return axios({
    url: 'runanalysis/v1/newpoweranalysis/allArea',
    method: 'post',
    data
  });
}
/*
  全区域数据
 */
export function dataAllArea (data) {
  return axios({
    url: 'runanalysis/v1/newpoweranalysis/dataAllArea',
    method: 'post',
    data
  });
}
/*
  单区域数据
 */
export function dataSingleArea (data) {
  return axios({
    url: 'runanalysis/v1/newpoweranalysis/dataSingleArea',
    method: 'post',
    data
  });
}
/*
  单区域-各电站评分列表数据
 */
export function dataSingleAreaScope (data) {
  return axios({
    url: 'runanalysis/v1/newpoweranalysis/dataSingleAreaScope',
    method: 'post',
    data
  });
}
/*
  单电站数据
 */
export function dataSingleStation (data) {
  return axios({
    url: 'runanalysis/v1/newpoweranalysis/dataSingleStation',
    method: 'post',
    data
  });
}
/*
  导出表格
 */
export function exportAnalysisTable (data) {
  return axios({
    url: 'runanalysis/v1/newpoweranalysis/export',
    method: 'post',
    data
  });
}
/*
  区域项目电站档案数
 */
export function allAreaPsa (data) {
  return axios({
    url: 'runanalysis/v1/newpoweranalysis/allAreaPsa',
    method: 'post',
    data
  });
}
