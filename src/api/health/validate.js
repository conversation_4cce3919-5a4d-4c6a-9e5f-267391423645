const valitateTips = {
  obj: '',
  maxValue: (value, num) => { // 判断最大值
    return Number(value) > num;
  },
  regRule: (value) => { // 是否满足验证规则
    return value && !/^[1-9]+([.]{1}[0-9]{1})?$/.test(value);
  },
  valitate: (name) => {
    return function (rule, value, callback) {
      // 验证支路最大允许输入电流
      if (name === 'validateInputElectricityMax') {
        if (valitateTips.regRule(value) || valitateTips.maxValue(value, 8000)) {
          callback(new Error('请输入0-8000数字且精度为0.1'));
        } else {
          callback();
        }
      }
      // 验证PPT支路数
      if (name === 'validateSubassemblyNumMax') {
        if ((value && !/^[1-9]|0$/.test(value)) || value > 1000) {
          callback(new Error('请输入0-1000数字'));
        } else {
          callback();
        }
      }
      callback();
    };
  }
};
export default valitateTips;
// 验证 温度-200-200
export const validateTemperature = (rule, value, callback) => {
  let reg = /^([-]?([0-9]+([.]{1}[0-9]{1})?))$/;
  if ((value && !reg.test(value)) || Number(value) > 200 || Number(value) < -200) {
    callback(new Error('请输入-200到200数字且精度为0.1'));
  } else {
    callback();
  }
};
  // 验证-360 - 360 的组件倾角
export const validatep130004 = (rule, value, callback) => {
  let reg = /^([-]?([0-9]+([.]{1}[0-9]{2})?))$/;
  if ((value && !reg.test(value)) || Number(value) > 360 || Number(value) < -360) {
    callback(new Error('请输入-360到3600数字且精度为0.01'));
  } else {
    callback();
  }
};
  // 验证0-100 的数字
export const validate100 = (rule, value, callback) => {
  let reg = /^[1-9]|0$/;
  if ((value && !reg.test(value)) || Number(value) > 100) {
    callback(new Error('请输入0-100的整数'));
  } else {
    callback();
  }
};
export const validate32 = (rule, value, callback) => {
  let reg = /^[1-9]{1}|[0-9]$/;
  if ((value && !reg.test(value)) || Number(value) > 32) {
    callback(new Error('请输入1-32的整数'));
  } else {
    callback();
  }
};
export const valitateChina = (rule, value, callback) => {
  if (value.trim().length > 1000) {
    callback(new Error('最多输入1000字符'));
  }
};
  // 验证 电压-1500-1500
export const validateVoltage = (rule, value, callback) => {
  let reg = /^([-]?([0-9]+([.]{1}[0-9]{1})?))$/;
  if ((value && !reg.test(value)) || Number(value) > 1500 || Number(value) < -1500) {
    callback(new Error('请输入-1500到1500数字且精度为0.1'));
  } else {
    callback();
  }
};
  // 验证0-3000
export const validateNumber = (rule, value, callback) => {
  let reg = /^[0-9]+([.]{1}[0-9]{1})?$/;
  if ((value && !reg.test(value)) || Number(value) > 3000) {
    callback(new Error('请输入0-3000数字且精度为0.1'));
  } else {
    callback();
  }
};
// 验证功率0-20000000
export const validateMaxNumber = (rule, value, callback) => {
  let reg = /^[0-9]+([.]{1}[0-9]{1})?$/;
  if ((value && !reg.test(value)) || Number(value) > 20000000) {
    callback(new Error('请输入0-20000000数字且精度为0.1'));
  } else {
    callback();
  }
};
