// import Vue from 'vue';
import Router from 'vue-router';
import { constantRouterMap } from '@/config/router.config';

Vue.use(Router);
// 解决报错
const originalPush = Router.prototype.push;
const originalReplace = Router.prototype.replace;
// push
Router.prototype.push = function push (location, onResolve, onReject) {
  // if (onResolve || onReject) return originalPush.call(this, location, onResolve, onReject)
  return originalPush.call(this, location).catch(() => {
    originalPush.call(this, location, onResolve, onReject);
  });
};
// replace
Router.prototype.replace = function push (location, onResolve, onReject) {
  // if (onResolve || onReject) return originalReplace.call(this, location, onResolve, onReject)
  return originalReplace.call(this, location).catch(() => {
    originalPush.call(this, location, onResolve, onReject);
  });
};
export default new Router({
  mode: 'hash',
  base: process.env.BASE_URL,
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRouterMap
});
