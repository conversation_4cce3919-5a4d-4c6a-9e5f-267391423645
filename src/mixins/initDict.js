import { get, getDictMap } from '@/api/isolarErp/dictDetail';
export default {
  data () {
    return {
      dicts: [],
      dictMap: {}
    };
  },
  computed: {
    merge () {
      return function (arr0, arr1) {
        if (Array.isArray(arr0) && arr0.length) {
          return arr0;
        }
        if (Array.isArray(arr1) && arr1.length) {
          return arr1;
        }
        return [];
      };
    }
  },
  methods: {
    async getDict (name) {
      const requestMap = {
        'dictName': name
      };
      return new Promise((resolve, reject) => {
        get(requestMap).then(res => {
          this.dicts = res.result_data.rows;
          resolve(res);
        }).catch(err => {
          reject(err);
        });
      });
    },
    // 多个字典查询时使用逗号拼接， 如：
    // 加载多个数据字典，如何调用如下：
    // this.getDict('user_status')
    // 在vue中使用加载出来的字典：
    // dictMap.[字典名称] 如：dictMap.user_status
    async getDictMap (names) {
      // 优先放入到dictMap中，避免页面加载时 undefined
      const requestMap = {
        'dictName': names
      };
      const arr = names.split(',');
      for (let i = 0; i < arr.length; i++) {
        this.dictMap[arr[i]] = [];
      }
      return new Promise((resolve, reject) => {
        getDictMap(requestMap).then(res => {
          let resultData = res.result_data;
          for (let key in resultData) {
            resultData[key].forEach(item => {
              item.dispName = item.dataLable;
              item.codeValue = item.dataValue;
            });
          }
          this.dictMap = resultData;
          resolve(res);
        }).catch(err => {
          reject(err);
        });
      }).catch((e) => {

      });
    }
  }
};
