import { getUrlExportFile, exportBigExcel, getExportCerti, exportReportApi } from '@/api/config/downBigExcel';
export default {
  components: {},
  name: 'downBigExcel',
  data () {
    return {
      downloadType: 'excel'
    };
  },
  methods: {
    // vnodeMessage
    vNodeMessage () {
      const h = this.$createElement;
      this.$message.success({
        content: h('span', undefined, [
          h('span', {
            style: 'margin-right: 8px'
          }, '文件内容过大，已打包1项至我的下载，可点击立即查看，或5s后自动跳转到我的下载'),
          h('a-button', {
            class: 'solar-eye-btn-primary',
            on: {
              click: () => {
                this.$router.push('/personal/downBigExcel');
              }
            }
          }, '立即查看')
        ]),
        duration: 5
      });
    },
    // 获取导出id
    async getExportIdCom (loadName, exportBusinessType, searchData) {
      let that = this;
      that[loadName] = true;
      let params = Object.assign({}, searchData);
      params.exportBusinessType = exportBusinessType;
      let id = await this.promiseReturn(params, loadName);
      return new Promise((resolve) => {
        resolve(id);
      });
    },
    promiseReturn (params, loadName, isZip = false) {
      // let fn = isZip ? getExportCerti : exportBigExcel;
      this.downloadType = isZip ? 'zip' : this.downloadType;
      // 获取下载文件api
      const serviceFn = this.getServiceFn();
      return new Promise((resolve, reject) => {
        serviceFn(params).then(res => {
          resolve(res.result_data.id);
        }).catch((err) => {
          this[loadName] = false;
          reject(err);
        });
      });
    },
    getServiceFn () {
      switch (this.downloadType) {
        case 'excel':
          return exportBigExcel;
        case 'zip':
          return getExportCerti;
        case 'docx':
          return exportReportApi;
        default:
          return exportBigExcel;
      }
    },
    /**
     * 导出
     * @param {string} [loadName='expLoad'] 导出按钮的loading ，不传默认 expLoad
     * @param {*} exportBusinessType 获取导出id 的code 码
     * @param {string} downloadType 下载类型 excel|docx|zip，根据相应下载类型调取不同接口
     */
    async exportExcelEvent (loadName = 'expLoad', exportBusinessType, searchData, downloadType = 'excel') {
      this.downloadType = downloadType;
      let getId = !searchData ? this.getExportId(loadName, exportBusinessType) : this.getExportIdCom(loadName, exportBusinessType, searchData);
      let stationRecord = await getId;
      if (!stationRecord) {
        return;
      }
      let i = 0;
      let isShow = true;
      let isNeedRequest = true;
      let isFirstTime = true;
      let timer = setInterval(() => {
        ++i;
        if (i >= 5 && isShow == true) { // 10s 左右提示可跳转到我的下载页
          isShow = false;
          this.vNodeMessage();
          var timeout = setTimeout(() => {
            this.$router.push('/personal/downBigExcel');
          }, 5000);
        }
        if (isNeedRequest) {
          getUrlExportFile({ id: stationRecord }).then(res => {
            isNeedRequest = false;
            if (res.result_code == '1') {
              if (typeof (res.result_data) == 'number') {
                if (res.result_data != 0) {
                  this.clearTimerAndLoading(timer, loadName);
                  this.$message.warning(res.result_msg);
                }
                if (res.result_data == 0 && isFirstTime) {
                  isFirstTime = false;
                  this.$message.warning({ content: res.result_msg, key: 'warn', duration: 5 });
                }
              } else {
                this.clearTimerAndLoading(timer, loadName);
                clearTimeout(timeout);
                this.$message.success('下载成功');
                window.location.href = res.result_data;
              }
            }
          }).catch(() => {
            this.clearTimerAndLoading(timer, loadName);
            clearTimeout(timeout);
          });
        } else {
          isNeedRequest = true;
        }
      }, 2000);
      this.clearTimer(timer, loadName);
    },
    clearTimerAndLoading (timer, loadName) {
      this.$message.destroy();
      clearInterval(timer);
      this[loadName] = false;
    },
    clearTimer (timer, loadName) {
      this.$once('hook:beforeDestroy', () => {
        this[loadName] = false;
        clearInterval(timer);
        this.$message.destroy();
      });
      this.$once('hook:deactivated', () => {
        this[loadName] = false;
        this.$message.destroy();
        clearInterval(timer);
      });
    }
  }
};
