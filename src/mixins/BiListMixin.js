import { getAction } from '@/api/manage';
import Vue from 'vue';
import { ACCESS_TOKEN } from '@/store/mutation-types';
// const url = 'http://10.5.5.9:8085'
const url = process.env.VUE_APP_BI_BASE_URL;
export const BiListMixin = {
  data () {
    return {
      // token header
      tokenHeader: { 'X-Access-Token': Vue.ls.get(ACCESS_TOKEN) },
      /* 查询条件-请不要在queryParam中声明非字符串值的属性 */
      queryParams: {},
      /* 数据源 */
      dataSource: [],
      psName: '',
      /* 筛选参数 */
      filters: {},
      /* table加载状态 */
      loading: false,
      /* table选中keys */
      selectedRowKeys: [],
      /* table选中records */
      selectionRows: [],
      upload: {
        open: false,
        title: '',
        hostUrl: '',
        isUploading: false,
        updateSupport: 0
      },
      fileList: {},
      cardHeight: (window.innerHeight - 48 - 52 - 60) + 'px',
      tableHeight: window.innerHeight - 48 - 52 - 60 - 140,
      timeRange: [],
      placeholder: ['请选择开始时间', '请选择结束时间'],
      isReset: false,
      isHasData: false
    };
  },
  created () {
    if (this.$route.query.token) {
      this.cardHeight = window.innerHeight + 'px';
    }
  },
  computed: {
    scroll: function () {
      var width = window.innerWidth;
      let $antTable = window.document.getElementsByClassName('ant-row');
      if ($antTable[0]) {
        width = $antTable[0].clientWidth;
      }
      return {
        // x:'max-content',
        x: width,
        y: window.innerHeight / 2
      };
    },
    innerHeight: function () {
      var innerHeight = window.innerHeight;
      return innerHeight;
    },
    createUser () {
      return this.$store.getters.userInfo.realname + '-' + this.$store.getters.userInfo.username;
    }
  },
  methods: {
    loadData (arg) {
      this.loading = true;
      if (!this.url.list) {
        this.$message.error('请设置url.list属性!');
        return;
      }
      this.onClearSelected();
      var params = this.getQueryParams(); // 查询条件
      this.loading = true;
      getAction(url + this.url.list, params).then((res) => {
        this.loading = false;
        if (res.code == '200') {
          this.dataSource = res.data;
        } else {
          this.$message.warning(res.message);
        }
        this.isHasData = res.data.length != 0;
        this.setBodyMinHeight && this.setBodyMinHeight(this.isHasData, this.isMointor ? -48 : 0);
        this.loading = false;
        // 如果新增跳转到编辑页
        if (arg === 'toEdit' && this.dataSource && this.dataSource.length) {
          this.edit(this.dataSource[0].reportCode);
        }
      });
    },
    getQueryParams () {
      // 获取查询条件
      for (let item in this.queryParams) { // 删除原查询条件的影响
        if (item.endsWith('Order')) {
          delete this.queryParams[item];
        }
      }
      var param = Object.assign({}, this.queryParams, { createUser: this.createUser }, this.baseParams, this.filters);
      return param;
      // return filterObj(param);
    },
    onClearSelected () {
      this.selectedRowKeys = [];
      this.selectionRows = [];
    },
    searchQuery () {
      this.onClearSelected();
      this.loadData();
    },

    handleAdd () {
      this.detailOpen = true;
      this.form.reportName = '';
    }
  }

};
