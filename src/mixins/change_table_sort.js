export default {
  data () {
    return {

    };
  },
  methods: {
    // 选择图表字段进行排序
    changeTableSort (data) {
      if (data.order) {
        this.queryParam.sortFiled = data.prop;
        if (data.order == 'asc') {
          this.queryParam.sortKind = 'ASC';
        } else {
          this.queryParam.sortKind = 'DESC';
        }
      } else {
        this.queryParam.sortKind = 'DESC';
        this.queryParam.sortFiled = '';
      }
      this.doQuery();
    }
  }
};
