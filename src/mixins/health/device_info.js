
import {
  validateMaxNumber,
  valitateChina,
  validate100,
  validatep130004
} from '@/api/health/validate.js';
export default {
  name: 'deviceInfo',
  data () {
    return {
      form: {
        accessStringNum: '',
        belongLine: '', // 所属线路
        belongMatrix: '', // 所属方阵
        inverterType: null,
        inputElectricityMax: null, // 支路最大允许输入电流（A）
        modulePeakPower: null, // 组件峰值功率
        subassemblyNumMax: null, // 每路MPPT可接入最大组串数
        workingTemperatureMax: null, // 工作温度最大值
        workingTemperatureMin: null, // 工作温度最小值
        deratingTemperaturePoint: null, // 降额温度点
        moduleTemperatureLimit: null, // 模块温度限值
        startingVoltage: null, // 启动电压
        ratedOperatingVoltage: null, // k*额定工作电压
        ratedPower: null, // 额定功率
        inverterUnitNum: null, // 逆变器单元数
        mpptNum: null,
        tableData: [
          {
            'name': '组件峰值功率(W)',
            'value': 'p130001'
          },
          {
            'name': '单串块数',
            'value': 'p130002'
          },
          {
            'name': '组件厂家',
            'value': 'p130003'
          },
          {
            'name': '组件倾角',
            'value': 'p130004'
          },
          {
            'name': '支架类型',
            'value': 'p130005'
          }
        ]
      },
      cols: [
        {
          prop: 'name',
          label: '组件串数',
          width: 130
        }
      ]
    };
  },
  methods: {
    validateInverter () {
      let flag = '';
      this.$refs.form.validateField(
        ['mpptNum', 'subassemblyNumMax'],
        (error) => {
          if (!error) {
            flag = true;
          } else {
            flag = false;
          }
        }
      );
      if (flag) {
        if (
          Number(this.form.mpptNum) * Number(this.form.subassemblyNumMax) <=
            36
        ) {
          this.cols = this.cols.splice(0, 1);
          this.form.tableData.map((item, index) => {
            let name = '';

            for (
              let i = 0;
              i <
                Number(this.form.mpptNum) * Number(this.form.subassemblyNumMax);
              i++
            ) {
              name = `num${i + 1}`;
              this.$set(this.form.tableData[index], name, '');
            }
            this.$set(this.form.tableData[index], 'isCheked', false);
          });
          for (
            let i = 0;
            i < Number(this.form.mpptNum) * Number(this.form.subassemblyNumMax);
            i++
          ) {
            this.cols.push({
              prop: `num${i + 1}`,
              label: `第${i + 1}路`
            });
          }
        } else {
          this.$message.error(
            '每路MPPT可接入最大组串数和MPPT路数的乘积不得大于36'
          );
        }
      }
    },
    validateStringBox () {
      let flag = '';
      this.$refs.form.validateField(
        'accessStringNum',
        (error) => {
          if (!error) {
            flag = true;
          } else {
            flag = false;
          }
        }
      );
      if (flag) {
        if (Number(this.form.accessStringNum) <= 32) {
          this.cols = this.cols.splice(0, 1);
          this.form.tableData.map((item, index) => {
            let name = '';

            for (let i = 0; i < Number(this.form.accessStringNum); i++) {
              name = `num${i + 1}`;
              this.$set(this.form.tableData[index], name, '');
            }
            this.$set(this.form.tableData[index], 'isCheked', false);
          });
          for (let i = 0; i < Number(this.form.accessStringNum); i++) {
            this.cols.push({
              prop: `num${i + 1}`,
              label: `第${i + 1}路`
            });
          }
        } else {
          this.$message.error('可接入最大组串数大于32');
        }
      }
    },
    blurEvent () {
      if (this.provideData.deviceType === 1) {
        this.validateInverter();
      } else {
        this.validateStringBox();
      }
    },
    isModifySameTime (row, value) {
      if (row.isCheked) {
        for (let item in row) {
          if (item.indexOf('num') === 0) {
            row[item] = value;
          }
        }
      }
    },
    getValue (str, flag) {
      let num = '';
      let msg = ''; let validate = '';
      switch (str) {
        case 'p130001':
          num = 0;
          msg = '请输入峰值功率';
          validate = validateMaxNumber;
          break;
        case 'p130002':
          num = 1;
          msg = '请输入单串块数';
          validate = validate100;
          break;
        case 'p130003':
          num = 2;
          msg = '请输入组件厂家';
          validate = valitateChina;
          break;
        case 'p130004':
          num = 3;
          msg = '请输入组件倾角';
          validate = validatep130004;
          break;
      }
      return flag == 0 ? num : flag == 1 ? msg : validate;
    }
  },
  beforeDestroy () {
    this.$refs.form && this.$refs.form.resetFields();
  }
};
