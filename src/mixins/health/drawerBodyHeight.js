export default {
  data () {
    return {
      drawerBodyHeight: ''
    };
  },
  mounted () {
    let drawer = document.getElementsByClassName('ant-drawer-wrapper-body')[0];
    let header = document.getElementsByClassName('ant-drawer-header')[0];
    this.$nextTick(() => {
      if (drawer && header) {
        this.drawerBodyHeight = drawer.offsetHeight - (header ? header.offsetHeight : 0) - 24 * 2 + 'px';
      }
    });
    window.onresize = () => {
      if (drawer && header) {
        this.drawerBodyHeight = drawer.offsetHeight - (header ? header.offsetHeight : 0) - 24 * 2 + 'px';
      }
    };
  },
  updated () {
    let drawer = document.getElementsByClassName('ant-drawer-wrapper-body')[0];
    let header = document.getElementsByClassName('ant-drawer-header')[0];
    if (drawer && header) {
      this.drawerBodyHeight = drawer.offsetHeight - (header ? header.offsetHeight : 0) - 24 * 2 + 'px';
    }
  }
};
