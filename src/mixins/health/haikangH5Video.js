// 是否支持mse
import moment from 'moment';
const { JSPlugin } = window;
const MSE_IS_SUPPORT = !!window.MediaSource;
export default {
  name: 'videoMixin',
  data () {
    return {
      player: null,
      mseSupport: MSE_IS_SUPPORT,
      tabActive: MSE_IS_SUPPORT ? 'mse' : 'decoder',
      labelCol: { span: 5 },
      wrapperCol: { span: 18 },
      playback: {
        startTime: '',
        endTime: '', // 2021-10-29T23:59:59
        valueFormat: moment.HTML5_FMT.DATETIME_LOCAL_SECONDS,
        seekStart: '2021-10-29T12:00:00',
        rate: ''
      },
      muted: true,
      volume: 50,
      volumeOnSvg: {
        template: '<svg t="1624453273744" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1421" width="200" height="200"><path d="M597.994667 138.005333q130.005333 28.010667 213.994667 132.992t84.010667 241.002667-84.010667 241.002667-213.994667 132.992l0-88q93.994667-28.010667 153.002667-106.005333t59.008-180.010667-59.008-180.010667-153.002667-106.005333l0-88zM704 512q0 120-106.005333 172.010667l0-344q106.005333 52.010667 106.005333 172.010667zM128 384l170.005333 0 213.994667-213.994667 0 684.010667-213.994667-213.994667-170.005333 0 0-256z" p-id="1422"></path></svg>'
      },
      volumeOffSvg: {
        template: '<svg t="1624453193279" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9147" width="200" height="200"><path d="M512 170.005333l0 180.010667-90.005333-90.005333zM181.994667 128l714.005333 714.005333-53.994667 53.994667-88-88q-74.005333 58.005333-156.010667 77.994667l0-88q50.005333-13.994667 96-50.005333l-181.994667-181.994667 0 288-213.994667-213.994667-170.005333 0 0-256 202.005333 0-202.005333-202.005333zM810.005333 512q0-101.994667-59.008-180.010667t-153.002667-106.005333l0-88q130.005333 28.010667 213.994667 132.992t84.010667 241.002667q0 96-44.010667 178.005333l-64-66.005333q21.994667-53.994667 21.994667-112zM704 512q0 18.005333-2.005333 26.005333l-104-104 0-93.994667q106.005333 52.010667 106.005333 172.010667z" p-id="9148"></path></svg>'
      },
      isPlay1: true,
      isPlay2: true
    };
  },
  computed: {
    mode: function () {
      return this.tabActive === 'mse' ? 0 : 1;
    }
  },
  methods: {
    initVideo () {
      // 设置播放容器的宽高并监听窗口大小变化
      window.addEventListener('resize', () => {
        this.player.JS_Resize();
      });
    },
    createPlayer (PlayerDomId, splitNum) {
      return new JSPlugin({
        szId: PlayerDomId,
        szBasePath: 'https://staticres.isolareye.com/js/hkplayer',
        iMaxSplit: splitNum,
        iCurrentSplit: splitNum,
        openDebug: true,
        isNeedSplit: false,
        // iWidth: 600,
        // iHeight: 400,
        oStyle: {
          borderSelect: '#FFCC00',
          border: '#343434',
          background: '#000'
        }
      });
    },
    // 事件回调绑定
    windowCallback () {
      let that = this;
      that.player.JS_SetWindowControlCallback({
        windowEventSelect: function (iWndIndex) { // 插件选中窗口回调
          console.log('windowSelect callback: ', iWndIndex);
        },
        pluginErrorHandler: function (iWndIndex, iErrorCode, oError) { // 插件错误回调

          // console.log('pluginError callback: ', iWndIndex, iErrorCode, oError);
          // let current = that.currentSlide - 1,
          //     resCur = that.eventAlarmResult[current]
          // var url0 = resCur.url[0].split('?beginTime='),
          //     url1 = resCur.url[1].split('?beginTime=')

          // that.playbackStart(that.player, resCur.startTime, resCur.endTime, 0, url0[0])
          // that.playbackStart(that.player, resCur.startTime, resCur.endTime, 1, url1[0])
        },
        windowEventOver: function (iWndIndex) { // 鼠标移过回调
          that.windowEventOver(iWndIndex);
        },
        windowEventOut: function (iWndIndex) { // 鼠标移出回调
          that.windowEventOut(iWndIndex);
        },
        windowEventUp: function (iWndIndex) { // 鼠标mouseup事件回调
          // console.log(iWndIndex);
        },
        windowFullCcreenChange: function (bFull) { // 全屏切换回调
          console.log('fullScreen callback: ', bFull);
        },
        firstFrameDisplay: function (iWndIndex, iWidth, iHeight) { // 首帧显示回调
          console.log('firstFrame loaded callback: ', iWndIndex, iWidth, iHeight);
        },
        performanceLack: function () { // 性能不足回调
          console.log('performanceLack callback: ');
        },
        StreamEnd: function () {
          console.log('回调');
        }
      });
    },

    windowEventOver () {
      this.showBtn = true;
    },
    windowEventOut () {
      this.showBtn = false;
    },

    // 两个窗口一起实时播放
    wndsRealPlay (realPlayParams) {
      this.player.aWndList.forEach(async (item, index) => {
        // setTimeout(() => {
        this.realplay(index, realPlayParams['url' + index]);
        // }, 3000)
      });
    },

    /* 预览&对讲 */
    realplay (index, playURL) {
      let { mode } = this;
      // index = player.iCurrentWndIndex;
      this.player.JS_Play(playURL, { playURL, mode }, index).then(
        () => { console.log('realplay success'); },
        e => {
          console.error(e);
          // this.realplay(index,playURL)
        }
      );
    },

    // 两个窗口一起回放
    wndsPlaybackStart (playbackParams) {
      this.player.aWndList.forEach(async (item, index) => {
        // setTimeout(() => {
        this.playbackStart(playbackParams.startTime, playbackParams.endTime, index, playbackParams['url' + index]);
        // }, 3000)
      });
    },

    // 单个窗口播放
    async wndPlaybackStart (index, playbackParams) {
      if (this.player.aWndList[0].bPlay) {
        await this.stopPlay(index);
      }
      setTimeout(() => {
        this.playbackStart(playbackParams.startTime, playbackParams.endTime, index, playbackParams['url' + index]);
      }, 3000);
    },

    stopAllPlay () {
      this.player.JS_StopRealPlayAll().then(
        () => { this.playback.rate = 0; console.log('stopAllPlay success'); },
        e => { console.error(e); }
      );
    },

    /* 回放 */
    playbackStart (startTime, endTime, index, playURL) {
      let { mode } = this;
      // index = player.iCurrentWndIndex;
      startTime += 'Z';
      endTime += 'Z';

      this.player.JS_Play(playURL, { playURL, mode }, index, startTime, endTime).then(
        () => {
          this.playback.rate = 1;
        },
        e => {
          console.error(e, startTime, endTime, index, playURL);
        }
      );
    },

    /** 停止播放 */
    stopPlay (index) {
      this.player.JS_Stop(index).then(
        (res) => {
          // do you want...
        },
        () => {
          console.info('JS_StopRealPlayAll failed');
          // do you want...
        }
      );
    },

    /* 声音、抓图、录像 */
    openSound (player) {
      player.JS_OpenSound().then(
        () => {
          console.log('openSound success');
          this.muted = false;
        },
        e => { console.error(e); }
      );
    },
    closeSound (player) {
      player.JS_CloseSound().then(
        () => {
          this.muted = true;
        },
        e => { console.error(e); }
      );
    },
    playbackPause (index) {
      index == 0 ? this.isPlay1 = false : this.isPlay2 = false;
      this.player.JS_Pause(index).then(
        () => { console.log('playbackPause success'); },
        e => { console.error(e); }
      );
    },

    playbackResume (index) {
      index == 0 ? this.isPlay1 = true : this.isPlay2 = true;
      this.player.JS_Resume(index).then(
        () => { console.log('playbackResume success'); },
        e => { console.error(e); }
      );
    }

  },
  mounted () {

  }
};
