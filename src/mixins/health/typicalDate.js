import {
  getPowerStationTopicalDay
} from '@/api/health/healthapi.js';
import moment from 'moment';
export default {
  data () {
    return {
      containerVue: []
    };
  },
  methods: {
    /**
     * @description 获取典型日日期
     */
    getTopicalDay (psId) {
      let paramsObj = {
        psId: psId, // 电站id

        startTopicalDay: '', // 典型日开始时间
        endTopicalDay: '' // 典型日结束时间
      };
      this.tableLoading = true;
      // 请求查询接口
      this.containerVue = [];
      getPowerStationTopicalDay(paramsObj).then((res) => {
        this.tableLoading = false;
        if (res.result_code === '1') {
          if (res.result_data) {
            res.result_data.forEach((item) => {
              this.containerVue.push(moment(item).format('yyyy/MM/DD'));
            });
          }
        }
      })
        .catch(function (err) {
          console.log(err);
        });
    },
    getCurrentClassName (current) {
      if (this.containerVue.includes(current.format('yyyy/MM/DD'))) {
        return 'topic-date';
      }
      return '';
    }
  }
};
