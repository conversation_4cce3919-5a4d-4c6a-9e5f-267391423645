export default {
  data () {
    return {
      treeHeight: ''
    };
  },
  computed: {
    scroll: function () {
      var width = window.innerWidth;
      let $antTable = window.document.getElementsByClassName('ant-row');
      if ($antTable[0]) {
        width = $antTable[0].clientWidth;
      }
      return {
        // x:'max-content',
        x: width,
        y: window.innerHeight / 2
      };
    },
    innerHeight: function () {
      var innerHeight = window.innerHeight - 60 - 40 - 24;
      return innerHeight > 700 ? innerHeight : 800;
    }
  }
};
