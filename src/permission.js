import router from './router';
import store from './store';
import NProgress from 'nprogress'; // progress bar
import 'nprogress/nprogress.css'; // progress bar style
import notification from 'ant-design-vue/es/notification';
import { ACCESS_TOKEN } from '@/store/mutation-types';
import { generateIndexRouter } from '@/utils/util';
import { generateUuid } from '@/utils/index';
NProgress.configure({
  showSpinner: false
}); // NProgress Configuration

const whiteList = ['/user/login', '/user/alteration']; // no redirect whitelist

router.beforeEach((to, from, next) => {
  const list = ['login'];
  const toName = to.name; // 即将进入的路由名字
  const fromName = from.name; // 即将离开的路由名字
  const toIndex = list.indexOf(toName); // 进入下标
  const fromIndex = list.indexOf(fromName); // 离开下标
  let direction = '';
  // login页显示切换语言  其他页面不显示
  if (toName == 'login') {
    // 中英文切换显示
    let translate = document.getElementById('translate');
    translate.style.display = 'block';
    localStorage.setItem('translate', 'block');
  } else {
    // 中英文切换显示
    let translate = document.getElementById('translate');
    translate.style.display = 'none';
    localStorage.setItem('translate', 'none');
  }
  if (toIndex > -1 && fromIndex > -1) { // 如果下标都存在
    if (toIndex < fromIndex) { // 如果进入的下标小于离开的下标，那么是左滑
      direction = 'left';
    } else {
      direction = 'right'; // 如果进入的下标大于离开的下标，那么是右滑
    }
  }
  if (toIndex > -1 && toName == 'login') {
    direction = 'right';
  }
  store.commit('VIEW_DIRECTION', direction);
  if (!localStorage.getItem('deviceId')) {
    localStorage.setItem('deviceId', generateUuid());
  }
  NProgress.start(); // start progress bar
  if (Vue.ls.get(ACCESS_TOKEN)) {
    /* has token */
    if (store.getters.permissionList.length === 0) {
      store.dispatch('GetPermissionList').then(res => {
        // const menuData = res.result.menu;
        const menuData = res;
        if (menuData === null || menuData === '' || menuData === undefined) {
          return;
        }
        let constRoutes = [];
        constRoutes = generateIndexRouter(menuData);
        // 添加主界面路由
        store.dispatch('UpdateAppRouter', {
          constRoutes
        }).then(() => {
          // 根据roles权限生成可访问的路由表
          // 动态添加可访问路由表
          router.addRoutes(store.getters.addRouters);
          const redirect = decodeURIComponent(from.query.redirect || to.path);
          if (to.path === redirect) {
            // hack方法 确保addRoutes已完成 ,set the replace: true so the navigation will not leave a history record
            next({
              ...to,
              replace: true
            });
          } else {
            // 跳转到目的路由
            next({
              path: redirect
            });
          }
        });
      }).catch((err) => {
        notification.error({
          message: '系统提示',
          description: err || '没有权限，请联系管理员'
        });
        store.dispatch('Logout').then(() => {
          location.reload();
          // 中英文切换显示
          let translate = document.getElementById('translate');
          translate.style.display = 'block';
          localStorage.setItem('translate', 'block');
          next({
            path: '/user/login',
            query: {
              redirect: to.fullPath
            }
          });
        });
      });
    } else if (to.path === '/user/login') {
      next({ path: store.state.user.firstMenu.path });
      NProgress.done();
    } else {
      if (to.path === '/preview' && !to.query.hasOwnProperty('id')) { // 跳转到预览页 且没有id 时，返回到第一页
        next({ path: store.state.user.firstMenu.path });
        NProgress.done();
      } else {
        next();
      }
      next();
    }
  } else {
    if (whiteList.indexOf(to.path) !== -1) {
      // 在免登录白名单，直接进入
      next();
    } else {
      // 中英文切换显示
      let translate = document.getElementById('translate');
      translate.style.display = 'block';
      localStorage.setItem('translate', 'block');
      next({
        path: '/user/login',
        query: {
          redirect: to.fullPath
        }
      });
      NProgress.done(); // if current page is login will not trigger afterEach hook, so manually handle it
    }
  }
});

router.afterEach(() => {
  NProgress.done(); // finish progress bar
});
