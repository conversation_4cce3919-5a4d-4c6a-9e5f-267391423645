function vueRender ({ loading }) {
  return new Vue({
    template: `
        <div id="subapp-container" >
          <a-spin :spinning="loading" class="subapp-loading" size="large" style='margin: 20% 50%;height:50vh'></a-spin>
          <div id="subapp-viewport"></div>
        </div>
      `,
    el: '#subapp-container',
    data () {
      return {
        loading
      };
    }
  });
}

let app = null;

export default function render ({ loading }) {
  if (!app) {
    app = vueRender({ loading });
  } else {
    app.loading = loading;
  }
}
