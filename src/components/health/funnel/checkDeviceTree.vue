<template>
  <div class="left">
    <a-card :bordered="false" :style="{height:innerHeight +'px'}" :bodyStyle="{height:'100%',paddingRight: '8px'}">
      <div class="search-box" v-if="filterStatus">
        <a-input placeholder="输入电站名称" enter-button @key.enter="searchLeft" allowClear v-model="psNamesearch.psName">
          <div slot="suffix">
            <!-- <a-icon type="search" @click="searchLeft()" /> -->
            <span @click="searchLeft()">搜索</span>
          </div>
        </a-input>
      </div>
      <div class="tree-box" :style="{ height: innerHeight -130 +'px' }" v-if="filterStatus">
        <!-- <div style="display:flex;justify-content:center;line-height:30px;align-items:center" v-if="isShowIcon">
          <a-space :size="5">
            <a-space :size="2">
              <a-icon type="check-circle" />已进入运算</a-space>
            <a-space :size="2">
              <a-icon type="close-circle" />未进入运算</a-space>
          </a-space>
        </div> -->
        <a-tree class="tree-line" tree-checkable :replaceFields="replaceFields" :tree-data="pageList.pageList"
          :highlight-current="true" ref="tree" :checkedKeys="checkedKeys" @check="handleNodeCheck" :loadData="loadNode"
          :style="{ height: treeHeight }" :selected-keys="selectedKeys" :expandedKeys="expandedKeys" @select="handleNodeClick"  @expand="handleExpand" :checkable="checkDevice">
          <template slot="custom" slot-scope="{dataRef}">
            <span class="cust-tree-node">
              <a-row class="tree-row">
                <a-col class="cust-tree-node" :span="24 - (isShowIcon?1:0)">
                  <a-tooltip placement="right" :title="dataRef.device_name" :mouseEnterDelay="0.2" :mouseLeaveDelay="0">
                    <span class="show-elli" :style="{width: dataRef.finishedFlag == false ?'70%' : '100%'}">{{ dataRef.device_name }}</span>
                      <a-tag v-if="dataRef.finishedFlag == false" >{{isRealTime?'离线':'未运算'}}</a-tag>
                  </a-tooltip>
                </a-col>
              </a-row>
            </span>
          </template>
        </a-tree>
      </div>
      <Pagination v-if="
            filterStatus &&
            pageList &&
            pageList.pageList &&
            pageList.pageList.length > 0
          "
        :PageList="pageList" class="isolar-page-left" @pageChage="pageChage"></Pagination>
      <Organizational v-if="!filterStatus" @serchPsList="serchPsList" :inverterTypeFlag="inverterTypeFlag"></Organizational>
    </a-card>
  </div>
</template>
<script>
import Pagination from './Pagination';
import treeHeight from '@/mixins/health/leftTreeHeight';
import Organizational from '@/components/health/organizational/Organizational';
import {
  getOpenAlgorithmStationList,
  getOpenAlgorithmPowerStationByInverterTypes,
  getPsTreeMenu
} from '@/api/health/healthapi.js';
export default {
  components: {
    Pagination,
    Organizational
  },
  props: {
    resetChart: {
      type: Function,
      default: null
    },
    inverterTypeFlag: {
      type: Boolean,
      default: false
    },
    isShowIcon: {
      default: true,
      type: Boolean
    },
    checkDevice: {
      default: false,
      type: Boolean
    },
    parentDeviceType: {
      default: 1,
      type: [Number, String]
    },
    isRealTime: {
      default: false,
      type: Boolean
    }
  },
  mixins: [treeHeight],
  data () {
    return {
      isSearch: 0,
      staticInverData: [],
      staticBoxData: [],
      initType: 0, // 初始化设备类型
      selectedKeys: [],
      currentPsData: null, // 选中或展开时获取,然后针对当前电站你的设备,获取默认设备类型和设备
      deviceType: null,
      expandedKeys: [],
      checkedKeys: [],
      replaceFields: {
        title: 'device_name',
        children: 'childrenList',
        key: 'ps_key'
        // isLeaf: "leaf",
      },
      accordion: true,
      psName: '',
      curPage: '1',
      psId: '', // 电站id
      psKey: '', // 设备psKey
      size: '10', // 每页记录条数

      treeOrgId: '', // 组织中的组织id
      treePsId: '', // 组织中的电站id
      treePsIds: '',
      options1: [],
      psNamesearch: {
        psId: '',
        curPage: 1,
        psName: '',
        size: 30,
        treeOrgId: '',
        treePsId: ''
      },
      psKeyList: [],
      pageList: {},
      loading: true,
      nodeHad: undefined,
      resolveHad: undefined,
      filterStatus: true,
      filterDisabled: true
    };
  },
  created () {
    // 从电站首页跳转过来，获取电站id
    let psId = null;
    if (this.$route.query.id) {
      psId = this.$route.query.id;
    }
    this.$parent.psId = psId;
    this.loadNode();
  },
  watch: {
    'parentDeviceType' (newValue, oldValue) {
      // 主页表格内切换设备会触发
      if (this.initType == 0) {
        this.initType = 1;
      } else {
        // 主页面设备类型切换时，将pskeys清空，【echsrts 图同时只能展示一种设备类型 逆变器 或 汇流箱】
        // this.checkedKeys = []
      }
      // 主页面改变设备类型
      if (newValue != '') {
        this.deviceType = newValue;
        this.setCheckable(this.currentPsData, newValue);
      }
    }
  },
  methods: {
    mouseInOut (e) {
      if (e === 1) {
        this.filterDisabled = false;
      } else {
        this.filterDisabled = true;
      }
    },
    // 点击组织的确定按钮，重新请求电站
    serchPsList (obj) {
      this.filterStatus = true;
      if (obj.flag === 0) {
        // falg 0 是确定 1取消
        this.psNamesearch.treePsId = obj.psNameList;
        this.psNamesearch.treeOrgId = obj.orgId;
        this.loadNode().then(() => {});
        // this.treePsId = obj.psNameList;
        // this.treeOrgId = obj.orgId;
        // this.$emit(
        //   "refresh", {
        //     treePsId: obj.psNameList,
        //     treeOrgId: obj.orgId,
        //     psId: "",
        //     psKey: "",
        //   },
        //   true
        // );
        // this.getData();
      }
      //
    },
    changeFilterStatus () {
      this.filterStatus = !this.filterStatus;
    },

    // 左侧树改变页数
    pageChage (pageNum) {
      this.psNamesearch.curPage = pageNum;
      // this.nodeHad.childNodes = [];
      // this.loadNode(this.nodeHad, this.resolveHad);
      this.loadNode().then(() => {});
    },
    searchLeft () {
      this.isSearch = 1;
      this.psNamesearch.curPage = 1;
      // this.nodeHad.childNodes = []; //把存起来的node的子节点清空，不然会界面会出现重复树！
      // this.loadNode(this.nodeHad, this.resolveHad); //再次
      this.loadNode().then(() => {});
    },
    /**
       * @creator wangliang
       * @date 2020/09/03
       * @update 2020/09/21
       * @description 分布加载树节点数据
       */
    loadNode (node) {
      return new Promise((resolve, reject) => {
        // let obj = {
        //   psId: this.psNamesearch.psId,
        //   psName: this.psNamesearch.psName,
        //   curPage: this.psNamesearch.curPage,
        //   size: this.psNamesearch.size,
        //   treeOrgId: this.psNamesearch.treeOrgId,
        //   treePsId: this.psNamesearch.treePsId,
        // };
        if (!node) {
          this.loading = true;
          // this.nodeHad = node;
          let stationFunc = this.inverterTypeFlag ? getOpenAlgorithmPowerStationByInverterTypes(this.psNamesearch)
            : getOpenAlgorithmStationList(this.psNamesearch);
          stationFunc
            .then((res) => {
              if (res.result_code === '1') {
                res.result_data.pageList.forEach((item) => {
                  item['ps_key'] = item.psId;
                  item['checkable'] = false; // 由于电站下面的设备太多 所以电站不能出现复选框
                  item['level'] = 1;
                  item['isLeaf'] = false;
                  item['scopedSlots'] = {
                    title: 'custom'
                  };
                  // item["style"] = {width:"100%",display: "flex","justify-content": "space-between"};
                });
                this.pageList = res.result_data;
                if (this.checkDevice) {
                  // 如果是选择设备的情况下 默认选中第一个电站下的所有设备
                  let firstPs = res.result_data.pageList[0];
                  this.loadLevel2Data({
                    dataRef: firstPs
                  }).then(() => { resolve && resolve(); });
                }
                this.loading = false;
                resolve();
              } else {
                this.$message.error(res.result_msg);
                reject && reject(new Error());
              }
            }).catch((err) => {
              reject && reject(err);
            });
        } else if (node.dataRef.level == 1) {
          this.loadLevel2Data(node).then(() => { resolve && resolve(); }).catch(err => { reject && reject(err); });
        } else {
          resolve();
        }
      });
    },
    // 根据设备类型筛选可选中设备 只对当前展开的树进行筛选
    setCheckable (dataRef, deviceType) {
      let defaultPsData = null;
      const loopMethod = (target) => {
        for (let child of target.childrenList) {
          // 第一遍 逆变器，不可选，第二遍 汇流箱，可选
          child.checkable = child.device_type == deviceType;
          if (child.checkable) {
            defaultPsData = child;
          }
          if (child.childrenList) {
            loopMethod(child);
          }
        }
      };
      loopMethod(dataRef);
      this.pageList.pageList = [...this.pageList.pageList];
      // 切换设备类型时，恢复默认选中
      if (deviceType == 1) {
        this.accordionExpand({
          dataRef: this.staticBoxData.parentData
        }, this.staticBoxData.ps_key);
      } else if (deviceType == 4) {
        this.accordionExpand({
          dataRef: this.staticInverData.parentData
        }, this.staticInverData.ps_key);
      }
      return defaultPsData;
    },
    // 加载二级数据
    loadLevel2Data ({
      dataRef
    }) {
      this.currentPsData = dataRef;
      return new Promise((resolve, reject) => {
        let obj = {
          psId: dataRef.psId
        };
        if (dataRef.childrenList) {
          obj.loaded = true;
        }
        getPsTreeMenu(obj).then((res) => {
          if (res.result_code === '1') {
            let data = res.result_data.childrenList;
            let deviceType = res.result_data.deviceType || 4;
            let defaultPsData = null;
            // if (!obj.loaded) {
            // 如果未加载过树 则加载树
            let level2Keys = [];
            if (data && data.length > 0) {
              // data为电站级别数据
              data.forEach((item) => {
                // item 为逆变器级别
                level2Keys.push(item.ps_key);
                item['level'] = 2;
                item['parentData'] = dataRef;
                item['checkable'] = item.device_type == deviceType;
                if (item['checkable'] && !defaultPsData) {
                  defaultPsData = item;
                }
                if (item.childrenList && item.childrenList.length > 0) {
                  // 如果逆变器下还有子集，继续遍历汇流箱级别数据
                  item['isLeaf'] = false;
                  item.childrenList.forEach((subItem) => {
                    subItem['level'] = 3;
                    subItem['checkable'] = subItem.device_type == deviceType;
                    if (subItem['checkable'] && !defaultPsData) {
                      defaultPsData = subItem;
                      this.staticInverData = subItem;
                    }
                    level2Keys.push(subItem.ps_key);
                    subItem['isLeaf'] = true;
                    subItem['parentData'] = item;
                  });
                  // this.childrenList.push(item.childrenList);
                } else {
                  item['isLeaf'] = true;
                  item['checkable'] = true;
                  defaultPsData = data[0];
                }
              });
              dataRef.childrenList = data;
              dataRef.level2Keys = level2Keys;
            }
            this.deviceType = deviceType;
            this.pageList.pageList = [...this.pageList.pageList];
            if (defaultPsData != null) {
              // 用手风琴模式展开默认的选中项
              // this.staticInverData = defaultPsData;
              this.staticBoxData = data[0];
              this.accordionExpand({
                dataRef: defaultPsData.parentData
              }, defaultPsData.ps_key);
            }
            this.loading = false;
            resolve();
          } else {
            this.$message.error(res.result_msg);
            reject && reject(new Error());
          }
        }).catch((err) => {
          reject && reject(err);
        });
      });
    },
    // 展开设备
    accordionExpand ({
      dataRef
    }, checkedKey) {
      let parentKeys = [dataRef.ps_key];
      const getKeys = (data) => {
        let targetData = data;
        while (targetData.level > 1) {
          targetData = targetData.parentData;
          parentKeys.push(targetData.ps_key);
        }
      };
      getKeys(dataRef);
      // 展开指定的树
      this.expandedKeys = parentKeys;
      if (checkedKey) {
        this.handleNodeCheck([checkedKey], {
          checked: true
        });
      }
    },
    // 重置父组件图表的值
    resetChartList (dd) {
      this.$emit('resetChart', {
        dataList: null
      });
    },
    handleNodeClick (_, {
      selected,
      selectedNodes,
      node
    }) {
      let psId = '';
      let psKey = '';
      let data = node.dataRef;
      let nodeList = node;
      // this.treePsIds = data.psId;
      if (data.ps_key === undefined) {
        return;
      }
      let bool = node.getNodeState() == 'close';
      if (data.level == 1) {
        psId = data.psId;
        psKey = '';
        if (node.getNodeState() == 'close') {
          this.expandedKeys.push(psId);
          if (this.expandedKeys.length > 1) {
            this.expandedKeys.shift();
          }
        } else {
          this.expandedKeys = [];
        }
        this.handleExpand(this.expandedKeys, { expanded: bool, node: nodeList });
      } else {
        psId = '';
        psKey = data.ps_key;
        if (!data.isLeaf) {
          if (node.getNodeState() == 'close') {
            this.expandedKeys.forEach((item, index) => {
              if (item != psKey && typeof item == 'string' && item.indexOf('_') > -1) {
                this.expandedKeys.splice(index, 1);
              }
            });
            this.expandedKeys.push(psKey);
          } else {
            this.expandedKeys.splice(this.expandedKeys.indexOf(psKey), 1);
          }
        }
        this.handleExpand(this.expandedKeys, { expanded: bool, node: nodeList });
      }
    },
    handleExpand (expandedKeys, {
      expanded,
      node
    }) {
      if (expanded) {
        let promise = null;
        if (node.dataRef.level == 1) {
          this.initType = 0;
          // 当展开电站时，就去清空echarts 图表的数据，并重绘图表；
          if (this.resetChart) {
            this.resetChart();
          }
        }
        if (node.dataRef.level == 1 && node.dataRef.childrenList && node.dataRef.childrenList.length) {
          // 展开的是电站-->不加载设备树(已加载过) 获取默认设备类型 并从设备树中取得第一个可选设备作为默认设备
          promise = this.loadLevel2Data(node);
        } else {
          promise = new Promise((resolve, reject) => {
            resolve();
          });
        }
        // 如果是展开 只展开同一枝的数据
        if (node.dataRef.level != 1) {
          promise.then(() => {
            this.accordionExpand(node);
          });
        }
      } else {
        this.expandedKeys = expandedKeys;
      }
    },
    handleNodeCheck (checkedKeys, {
      checked
    }) {
      // 选中复选框时触发-this.checkedkeys-为树状图选中的节点
      this.checkedKeys = checkedKeys;
      let psKeys = [...checkedKeys];
      let psName = this.currentPsData.device_name;
      let psId = this.currentPsData.psId;
      let isSearch = this.isSearch;
      // 主页面 去主页面加载echarts图表及列表数据
      this.$emit('checkedPsKey', {
        psId,
        psName,
        psKeys,
        isSearch
      });
      this.isSearch = 0;
    }
  }
};
</script>
<style lang="less" scoped>
  :deep(.ant-tree-node-content-wrapper) {
    width: 100% !important;
  }

  .tree-row {
    width: 100% !important;
  }
</style>
