<template>
  <div class="isolar-page">
    <a-space :size="1">
      <a-button title="首页" size="small" @click="changePage(0)" icon="el-icon-d-arrow-left" :disabled="pagelist.curPage == 1">
        <a-icon type="double-left" />
      </a-button>
      <a-button title="上一页" size="small" @click="changePage(1)" icon="el-icon-arrow-left" :disabled="pagelist.curPage == 1">
        <a-icon type="left" />
      </a-button>
      <a-input v-model.number="pagelist.curPage" size="small" type="text" @keydown.enter.native="changePage()" style="width:60px;text-align:center"></a-input>
      <a-button title="下一页" size="small" @click="changePage(2)" type="text" class="el-icon-arrow-right" :disabled="pagelist.curPage == pagelist.totalPage">
        <a-icon type="right" />
      </a-button>
      <a-button title="尾页" size="small" type="text" @click="changePage(3)" icon="el-icon-d-arrow-right" :disabled="pagelist.curPage == pagelist.totalPage">
        <a-icon type="double-right" />
      </a-button>
    </a-space>
  </div>
</template>
<script>
export default {
  name: 'Pagination',
  props: {
    PageList: {
      required: true,
      type: Object
    },
    Width: {
      type: String,
      default: '11vw'
      // default:'14%'
    }
  },
  data () {
    return {
      pageNum: 1,
      pagelist: {
        curPage: 1,
        isMore: '',
        size: 30,
        totalPage: undefined
      }
    };
  },
  created () {
    this.pagelist = this.mappingData(this.PageList, this.pagelist);
  },
  watch: {
    'PageList' () {
      this.pagelist = this.mappingData(this.PageList, this.pagelist);
    }
  },
  mounted () {},
  methods: {
    changePage (parm) {
      if (parm === 0) {
        this.pagelist.curPage = 1;
      } else if (parm === 1) {
        this.pagelist.curPage = this.pagelist.curPage <= 1 ? 1 : this.pagelist.curPage - 1;
      } else if (parm === 2) {
        this.pagelist.curPage = Number(this.pagelist.curPage) + 1;
      } else if (parm === 3) {
        this.pagelist.curPage = Number(this.pagelist.totalPage);
      } else {
        if (/^[1-9]*$/.test(this.pagelist.curPage)) {
          this.pagelist.curPage = Number(this.pagelist.curPage) > Number(this.pagelist.totalPage) ? Number(this.pagelist
            .totalPage) : Number(this.pagelist.curPage);
        } else {
          this.pagelist.curPage = 1;
        }
      }
      this.$emit('pageChage', this.pagelist.curPage);
    },
    mappingData (source, target) {
      var obj = {};
      for (var key in target) {
        typeof source[key] !== 'undefined' && (obj[key] = source[key]);
      }
      return Object.assign({}, target, obj);
    }
  }
};
</script>
<style lang="less" scoped>
  .isolar-page {
    padding-top: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 24px;
  }
</style>
