<template>
  <div class="left" >
    <a-card :bordered="false" :style="{height:innerHeight +'px'}" :bodyStyle="{paddingRight: '8px'}">
      <div class="search-box" v-if="filterStatus">
        <a-input class="search-input" placeholder="输入电站名称" v-model="psNamesearch.psName" @key.enter="searchLeft()" allowClear>
            <span slot="suffix"  @click="searchLeft()">
            <span>搜索</span></span>
        </a-input>
        <a-button class="res-btn" @click="searchLeft('reset')">重置</a-button>
      </div>
      <div class="tree-box" :style="{ height: innerHeight -130 +'px' }" v-if="filterStatus">
        <a-tree class="tree-line" :replaceFields="replaceFields" :tree-data="pageList.pageList"
          :highlight-current="true" ref="tree" @select="handleNodeClick" :loadData="loadNode" lazy
          :expandedKeys="expandedKeys" @expand="handleExpand"  :selectedKeys="defaultSelectedKeys">
          <template slot="custom" slot-scope="{dataRef}">
            <span class="cust-tree-node">
              <a-row class="tree-row">
                <a-col class="cust-tree-node" :span="isShowIcon?23:24">
                  <a-tooltip placement="right" :title="dataRef.device_name">
                    <span class="show-elli" :style="{width: dataRef.finishedFlag == false ?'70%' : '100%'}">{{ dataRef.device_name }}</span>
                     <a-tag v-if="dataRef.finishedFlag == false" >{{isRealTime?'离线':'未运算'}}</a-tag>
                  </a-tooltip>
                </a-col>
              </a-row>
            </span>
          </template>
        </a-tree>
      </div>
      <Pagination v-if="
            filterStatus &&
            pageList &&
            pageList.pageList &&
            pageList.pageList.length > 0
          "
        :PageList="pageList" class="isolar-page-left" @pageChage="pageChage"></Pagination>
    </a-card>
  </div>
</template>
<script>
import Pagination from './Pagination';
import treeHeight from '@/mixins/health/leftTreeHeight';
import {
  getOpenAlgorithmStationList,
  getOpenAlgorithmPowerStationByInverterTypes,
  getPsTreeMenu
} from '@/api/health/healthapi.js';
export default {
  components: {
    Pagination
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
    inverterTypeFlag: {
      type: Boolean,
      defautl: false
    },
    isShowIcon: {
      default: true,
      type: Boolean
    },
    isRealTime: {
      default: false,
      type: Boolean
    }
  },
  mixins: [treeHeight],
  data () {
    return {
      expandedKeys: [],
      defaultSelectedKeys: [],
      selectedKeys: '',
      replaceFields: {
        title: 'device_name',
        children: 'childrenList',
        key: 'ps_key'
        // isLeaf: "leaf",
      },
      childrenList: [],
      accordion: true,
      psName: '',
      curPage: '1',
      psId: '', // 电站id
      psKey: '', // 设备psKey
      size: '10', // 每页记录条数

      treeOrgId: '', // 组织中的组织id
      treePsId: '', // 组织中的电站id
      treePsIds: '',
      options1: [],
      psNamesearch: {
        psId: '',
        curPage: 1,
        psName: '',
        size: 30,
        treeOrgId: '',
        treePsId: ''
      },
      psKeyList: [],
      pageList: {},
      loading: true,
      nodeHad: undefined,
      resolveHad: undefined,
      filterStatus: true,
      filterDisabled: true
    };
  },
  watch: {
    value () {
      this.defaultSelectedKeys = [];
      this.defaultSelectedKeys.push(this.value);
      this.$forceUpdate();
      console.log(this.selectedKeys);
    }
  },
  created () {
    // 从电站首页跳转过来，获取电站id
    let psId = null;
    if (this.$route.query.id) {
      psId = this.$route.query.id;
    }
    this.$parent.psId = psId;
    this.loadNode();
  },
  methods: {
    mouseInOut (e) {
      if (e === 1) {
        this.filterDisabled = false;
      } else {
        this.filterDisabled = true;
      }
    },
    // 点击组织的确定按钮，重新请求电站
    serchPsList (obj) {
      this.filterStatus = true;
      if (obj.flag === 0) {
        // falg 0 是确定 1取消
        this.psNamesearch.treePsId = obj.psNameList;
        this.psNamesearch.treeOrgId = obj.orgId;
        this.loadNode();
        this.treePsId = obj.psNameList;
        this.treeOrgId = obj.orgId;
        this.$emit(
          'refresh', {
            treePsId: obj.psNameList,
            treeOrgId: obj.orgId,
            psId: '',
            psKey: ''
          },
          true
        );
        // this.getData();
      }
      //
    },
    changeFilterStatus () {
      this.filterStatus = !this.filterStatus;
    },

    // 左侧树改变页数
    pageChage (pageNum) {
      this.psNamesearch.curPage = pageNum;
      // this.nodeHad.childNodes = [];
      // this.loadNode(this.nodeHad, this.resolveHad);
      this.loadNode();
    },
    searchLeft (isReset) {
      this.$router.replace({
        path: this.$route.path,
        query: {}
      });
      if (isReset) {
        this.psNamesearch.psName = '';
        this.psId = '';
        this.psKey = '';
        this.expandedKeys = [];
        this.$emit(
          'refresh', {
            psId: '',
            psKey: ''
          },
          false
        );
      }
      this.psNamesearch.curPage = 1;
      // this.nodeHad.childNodes = []; //把存起来的node的子节点清空，不然会界面会出现重复树！
      // this.loadNode(this.nodeHad, this.resolveHad); //再次
      this.loadNode();
    },
    /**
       * @creator wangliang
       * @date 2020/09/03
       * @update 2020/09/21
       * @description 分布加载树节点数据
       */
    loadNode (node) {
      return new Promise((resolve, reject) => {
        let obj = {
          psId: this.psNamesearch.psId,
          psName: this.psNamesearch.psName,
          curPage: this.psNamesearch.curPage,
          size: this.psNamesearch.size,
          treeOrgId: this.psNamesearch.treeOrgId,
          treePsId: this.psNamesearch.treePsId
        };

        if (!node) {
          this.loading = true;
          // this.nodeHad = node;
          let stationFunc = this.inverterTypeFlag ? getOpenAlgorithmPowerStationByInverterTypes(obj)
            : getOpenAlgorithmStationList(obj);
          stationFunc
            .then((res) => {
              if (res.result_code === '1') {
                res.result_data.pageList.forEach((item) => {
                  item['ps_key'] = item.psId;
                  item['level'] = 1;
                  item['isLeaf'] = false;
                  item['scopedSlots'] = {
                    title: 'custom'
                  };
                  if (this.$route.query.psId) {
                    this.defaultSelectedKeys.push(this.$route.query.psId);
                  } else {
                    this.defaultSelectedKeys = [];
                  }
                  // item["style"] = {width:"100%",display: "flex","justify-content": "space-between"};
                });
                this.pageList = res.result_data;
                this.loading = false;
                resolve();
              } else {
                this.$message.error(res.result_msg);
                reject(new Error());
              }
            }).catch((err) => {
              reject(err);
            });
        } else if (node.dataRef.level == 1) {
          if (node.dataRef.childrenList) {
            resolve();
            return;
          }
          let obj = {
            psId: node.dataRef.psId
          };
          getPsTreeMenu(obj).then((res) => {
            if (res.result_code === '1') {
              let data = res.result_data.childrenList;
              if (data && data.length > 0) {
                data.forEach((item) => {
                  item['level'] = 2;
                  if (item.childrenList && item.childrenList.length > 0) {
                    item['isLeaf'] = false;
                    item.childrenList.forEach((item) => {
                      item['isLeaf'] = true;
                    });
                    this.childrenList.push(item.childrenList);
                  } else {
                    item['isLeaf'] = true;
                  }
                });
                // dataTree = res.result_data.childrenList;
                node.dataRef.childrenList = data;
                this.pageList.pageList = [...this.pageList.pageList];
                this.loading = false;
              }
              resolve();
            } else {
              this.$message.error(res.result_msg);
              reject(new Error());
            }
          }).catch((err) => {
            reject(err);
          });
        } else {
          resolve();
        }
      });
    },
    /**
       * @creator wangliang
       * @date 2020/09/10
       * @update 2020/09/21
       * @description 树节点点击事件，获取电站id
       */
    handleNodeClick (_, {
      node
    }) {
      let psId = '';
      let psKey = '';
      let data = node.dataRef;
      // this.treePsIds = data.psId;
      if (data.ps_key === undefined) {
        return;
      }
      if (data.level == 1) {
        psId = data.psId;
        psKey = '';
        if (node.getNodeState() == 'close') {
          this.expandedKeys.push(psId);
          if (this.expandedKeys.length > 1) {
            this.expandedKeys.shift();
          }
        } else {
          this.expandedKeys = [];
        }
      } else {
        psId = '';
        psKey = data.ps_key;
        if (!data.isLeaf) {
          if (node.getNodeState() == 'close') {
            this.expandedKeys.forEach((item, index) => {
              if (item != psKey && typeof item == 'string' && item.indexOf('_') > -1) {
                this.expandedKeys.splice(index, 1);
              }
            });
            this.expandedKeys.push(psKey);
          } else {
            this.expandedKeys.splice(this.expandedKeys.indexOf(psKey), 1);
          }
        }
      }
      // this.treePsId = data.psId;
      this.$emit(
        'refresh', {
          psId: psId,
          psKey: psKey,
          treePsId: data.psId
        },
        false
      );
    },

    handleExpand (expandedKeys, {
      expanded,
      node
    }) {
      if (expanded) {
        // 如果是展开 只展开同一枝的数据
        const getParentKeys = node => {
          let parentNode = [node.dataRef.ps_key];
          const getKeys = (node) => {
            let targetNode = node;
            let targetData = node.dataRef;
            while (targetData.level > 1) {
              targetNode = targetNode.$parent;
              targetData = targetNode.dataRef;
              parentNode.push(targetData.ps_key);
            }
          };
          getKeys(node);
          return parentNode;
        };
        this.expandedKeys = getParentKeys(node);
      } else {
        this.expandedKeys = expandedKeys;
      }
    }
  }
};
</script>
<style lang="less" scoped>

  :deep(.ant-tree-node-content-wrapper) {
    width: 100% !important;
  }

  .tree-row {
    width: 100% !important;
  }

  // wwf
  .search-box{
    width: 70%;
    margin: 0;

    .res-btn{
        position:absolute;
        margin-left: 16px;
    }
  }
      @media screen and(max-width: 1600px) {
      .search-box {
      width: 60%;
    }
    }
     @media screen and(max-width: 1360px) {
      .search-box {
       width: 50%;
    }
    }
  :deep(.ant-card-body){
    padding:20px 10px;
    .ant-tree-treenode-switcher-close,.ant-tree-node-content-wrapper{
      &:hover {
        background: #f0f0f0;
      }
    }
  }
</style>
