<template>
  <div class="left">
    <a-card :bordered="false" :style="{height:innerHeight +'px'}" :bodyStyle="{height:innerHeight,paddingRight: '8px'}">
      <div class="search-box" v-if="filterStatus">
        <a-input placeholder="输入电站名称" enter-button @search="leftSearchClick" v-model="psNamesearch.psName" @key.enter="searchLeft" allowClear>
          <div slot="suffix">
             <span @click="leftSearchClick()">搜索</span>
          </div>
        </a-input>
      </div>
      <div class="tree-box" :style="{ height: innerHeight -130 +'px' }" v-if="filterStatus">
        <a-tree :tree-data="pageList.pageList" :replaceFields="defaultProps" :highlight-current="true" ref="tree"
          @select="handleNodeClick" :style="{ height: treeHeight }" :selectedKeys="selectedKeys" :showIcon="false"
          :showLine="false" class="main-tree">
          <a-icon slot="switcherIcon" style="display: none;" />
          <template slot="custom" slot-scope="{dataRef}">
            <span class="cust-tree-node" :class="{'no-icon': !isShowIcon,'show-icon':isShowIcon}">
              <a-row class="tree-row">
                <a-col :span="isShowIcon?23:24">
                  <a-tooltip placement="right" :title="dataRef.device_name">
                    <span class="show-elli" :style="{width: dataRef.isInterrupt == true || dataRef.finishedFlag == false?'75%' : '100%'}">
                      {{ dataRef.device_name }}
                    </span>
                    <a-tag v-if="dataRef.isInterrupt == true" class="solareye-tag">离线</a-tag>
                     <a-tag v-if="dataRef.finishedFlag == false" >{{isRealTime?'离线':'未运算'}}</a-tag>
                  </a-tooltip>
                </a-col>
              </a-row>
            </span>
          </template>
        </a-tree>
      </div>
      <Pagination v-if="
        filterStatus &&
        pageList &&
        pageList.pageList &&
        pageList.pageList.length > 0
      "
        :PageList="pageList" class="isolar-page-left" @pageChage="pageChage" />
    </a-card>
  </div>
</template>
<script>
import Pagination from './Pagination';
import treeHeight from '@/mixins/health/leftTreeHeight';
import {
  getAllPowerStation,
  getOpenAlgorithmStationList
} from '@/api/health/healthapi.js';
export default {
  components: {
    Pagination
  },
  props: {
    allPsData: {
      default: false,
      type: Boolean
    },
    allPsId: {
      default: false,
      type: Boolean
    },
    isShowIcon: {
      default: true,
      type: Boolean
    },
    isRealTime: {
      default: false,
      type: Boolean
    },
    onlyCleanPsFlag: {
      default: false,
      type: Boolean
    }
  },
  mixins: [treeHeight],
  data () {
    return {
      selectedKeys: [],
      rules: {},
      paramName: '',
      paramCN: '', // 参数名称
      psId: '', // 电站id
      treeLoad: true,
      paramValue: new Date(),
      currentPage1: 1,
      rowCount1: 0,
      isDisabled: true,
      accordion: '',
      psNamesearch: {
        psId: '',
        curPage: '1',
        psName: '',
        size: '30',
        treePsId: '',
        treeOrgId: ''
      },
      data: [],
      defaultProps: {
        title: 'psName',
        key: 'psId'
      },
      pageList: {},
      filterStatus: true,
      filterDisabled: true,
      valueT: [],
      value: '1'
    };
  },
  created () {
    this.getLeftData(this.psNamesearch);
  },
  mounted () {},
  methods: {
    mouseInOut (e) {
      if (e === 1) {
        this.filterDisabled = false;
      } else {
        this.filterDisabled = true;
      }
    },
    // 点击组织的确定按钮，重新请求电站
    serchPsList (obj) {
      this.filterStatus = true;
      if (obj.flag === 0) {
        // falg 0 是确定 1取消
        this.psNamesearch.treePsId = obj.psNameList;
        this.psNamesearch.treeOrgId = obj.orgId;
        this.getLeftData(this.psNamesearch);
      }
    },
    changeFilterStatus () {
      this.filterStatus = !this.filterStatus;
    },
    pageChage (pageNum) {
      this.psNamesearch.curPage = pageNum;
      this.getLeftData(this.psNamesearch);
    },
    leftSearchClick () {
      this.psNamesearch.curPage = 1;
      this.getLeftData(this.psNamesearch);
    },
    /**
       * 获取树数据
       * params{*搜索条件} psNamesearch
       */
    getLeftData (psNamesearch) {
      let params = {
        psId: psNamesearch.psId,
        psName: psNamesearch.psName,
        curPage: psNamesearch.curPage,
        size: psNamesearch.size,
        treePsId: psNamesearch.treePsId,
        treeOrgId: psNamesearch.treeOrgId,
        onlyCleanPsFlag: this.onlyCleanPsFlag,
        onlyFinishFlag: false
      };
      let promise = null;
      if (this.allPsData) {
        promise = getAllPowerStation(params);
      } else {
        promise = getOpenAlgorithmStationList(params);
      }
      promise.then((res) => {
        if (res.result_code === '1') {
          this.pageList = res.result_data;
          this.treeLoad = false;
          for (let itm of this.pageList.pageList) {
            itm['scopedSlots'] = {
              title: 'custom'
            };
          }
          if (!this.allPsId) {
            this.$nextTick(() => {
              this.selectedKeys = [this.pageList.pageList[0].psId];
            });
          }
          // 左边数据返回成功后，数据不为空时获取第一个电站的psId查询右边的参数信息
          if (this.pageList && this.pageList.pageList && this.pageList.pageList.length) {
            this.$emit('refreshData', {
              psId: this.allPsId ? '' : this.pageList.pageList[0].psId,
              psName: this.pageList.pageList[0].psName
            });
          }
        }
      }).catch((err) => {
        console.log(err);
      });
    },
    handleNodeClick (selectedKeys, {
      selected,
      selectedNodes,
      node,
      event
    }) {
      this.selectedKeys = selectedKeys;
      this.$emit('refreshData', {
        psId: node.dataRef.psId,
        psName: node.dataRef.psName
      });
    },
    setInterruptInfo (interruptInfo) {
      let arr = [];
      for (let key in interruptInfo) {
        if (interruptInfo[key]) {
          // 中断的电站设为红色
          arr.push(key);
        }
      }
      let data = this.pageList.pageList;
      for (let info of data) {
        info.isInterrupt = arr.indexOf(info.psId + '') >= 0;
      }
      this.pageList.pageList = [...data];
    }
  }
};
</script>
<style lang="less" scoped>
  .main-tree{
    :deep(.ant-tree-switcher) {
      display: none !important;
    }
  }
  :deep(.ant-tree-node-content-wrapper) {
    width: 100% !important;
  }

  .tree-row {
    width: 100% !important;
  }

  .show-icon {
    width: 95% !important;
  }

  .no-icon {
    width: 100% !important;
  }
</style>
