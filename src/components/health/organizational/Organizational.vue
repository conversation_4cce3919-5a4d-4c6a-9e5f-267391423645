<template>
  <div class="organization tree-box isolar-layout-content">
    <a-spin :spinning="load" />
    <a-tree class="relation-tree" :tree-data="orglist" :replaceFields="defaultProps" :checkable="true" ref="orgtree"
      :loadData="loadNode" @select="selectNode" :expandedKeys="expandedKeys" @expand="handleExpand" @check="onCheck"
      :checkedKeys="checkedKeys">
      <template slot="custom" slot-scope="{dataRef}">
        <span class="cust-tree-node">
          <a-tooltip placement="topLeft" :title="dataRef.name">
            <span class="show-elli">{{ dataRef.name }}</span>
          </a-tooltip>
        </span>
      </template>
    </a-tree>
    <a-button type="primary" size="small" @click="serchPsList(0)" style="width: 100%">确定</a-button>
    <a-row type="flex" align="middle" justify="center" style="margin-top: 10px;margin-bottom: 10px;">
      <a-col>
        <a-button size="small" @click="serchPsList(1)">取消</a-button>
      </a-col>
      <a-col :offset="1">
        <a-button type="waring" size="small" @click="reset">重置</a-button>
      </a-col>
    </a-row>
  </div>
</template>
<script>
import {
  getRegionalTree,
  selectPsIdAndPsNameByOrgId
} from '@/api/health/healthapi.js';
export default {
  props: {
    treeFlag: String,
    inverterTypeFlag: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      orglist: [],
      selectList: [],
      expandedKeys: [],
      defaultProps: {
        title: 'name',
        children: 'list',
        key: 'org_id'
      },
      checkedKeys: [],
      checkedNodes: [],
      orgId: '',
      load: true
    };
  },
  created () {
    this.loadNode();
  },
  methods: {
    /**
       * @creator wangliang
       * @date 2020/09/03
       * @description 分布加载树节点数据
       */
    loadNode (node) {
      return new Promise((resolve, reject) => {
        let dataTree = [];
        if (!node) {
          this.load = true;
          getRegionalTree()
            .then((res) => {
              this.load = false;
              if (res.result_code === '1') {
                dataTree = res.result_data;
                dataTree['level'] = 1;
                dataTree['scopedSlots'] = {
                  title: 'custom'
                };
                dataTree['isLeaf'] = false;
                dataTree[0].list.forEach((item) => {
                  item['level'] = 2;
                  item['scopedSlots'] = {
                    title: 'custom'
                  };
                  if (item.isParent == 0) {
                    item['isLeaf'] = false;
                  } else {
                    item['isLeaf'] = true;
                  }
                });
                // this.dataTreeList = res.result_data[0].list;
                this.orglist = dataTree;
                this.expandedKeys = [dataTree[0].org_id];
                // resolve(dataTree);
                resolve();
              }
              reject(new Error());
            }).catch(function (err) {
              reject(err);
            });
        } else if (node.dataRef.level == 1) {
          this.load = false;
          resolve();
        } else if (node.dataRef.level == 2) {
          this.orgId = node.dataRef.org_id;
          let obj = {
            algorithmFlag: '',
            inverterType: '',
            treeOrgId: this.orgId
          };
          if (this.treeFlag == 'all') {
            obj.algorithmFlag = '';
          } else {
            obj.algorithmFlag = '1';
          }
          if (this.inverterTypeFlag) {
            obj.inverterType = '1,2';
          } else {
            obj.inverterType = '';
          }
          let dataTreeChild = [];
          selectPsIdAndPsNameByOrgId(obj)
            .then((res) => {
              this.load = false;
              if (res.result_code === '1') {
                res.result_data.forEach((item) => {
                  item['org_id'] = item.psId;
                  item['level'] = 3;
                  item['scopedSlots'] = {
                    title: 'custom'
                  };
                  if (item.isParent != 0) {
                    item['isLeaf'] = true;
                  } else {
                    item['isLeaf'] = false;
                  }
                });
                dataTreeChild = res.result_data;
                node.dataRef.list = dataTreeChild;
                this.orglist = [...this.orglist];
                resolve();
              }
              reject(new Error());
            }).catch(function (err) {
              reject(err);
            });
        } else {
          resolve();
        }
      });
    },
    onCheck (checkedKeys, {
      checked: bool,
      checkedNodes,
      node,
      event
    }) {
      this.checkedNodes = checkedNodes;
      this.checkedKeys = checkedKeys;
    },
    serchPsList (flag) {
      let psNameArrList = [];
      let orgIdList = [];
      this.checkedNodes.forEach((item) => {
        let data = item.data.props.dataRef;
        if (!data.psId) {
          orgIdList.push(data.org_id);
        } else {
          psNameArrList.push(data.psId);
        }
      });
      this.$emit('serchPsList', {
        orgId: orgIdList.join(','),
        flag: flag,
        psNameList: psNameArrList.join(',')
      });
    },
    selectNode (data) {},
    reset () {
      this.checkedKeys = [];
    },
    handleExpand (expandedKeys, {
      expanded,
      node
    }) {
      if (expanded) {
        // 如果是展开 只展开同一枝的数据
        const getParentKeys = node => {
          let parentNode = [node.dataRef.org_id];
          const getKeys = (node) => {
            let targetNode = node;
            let targetData = node.dataRef;
            while (targetData.level > 1) {
              targetNode = targetNode.$parent;
              targetData = targetNode.dataRef;
              parentNode.push(targetData.org_id);
            }
          };
          getKeys(node);
          return parentNode;
        };
        this.expandedKeys = getParentKeys(node);
      } else {
        this.expandedKeys = expandedKeys;
      }
    }
  }
};
</script>
<style lang="less" scoped>
  .relation-tree {
    overflow: auto;
    height: calc(100vh - 200px);
    :deep(.cust-tree-node) {
      width: 80% !important;

      .show-elli {
        display: block;
        text-align: left;
        width: 100%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }
</style>
