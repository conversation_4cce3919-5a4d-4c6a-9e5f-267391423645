<template>
  <!-- 电站名和天气 -->
  <div class="weather-area color_bg">
    <div class="weather-left">
      <h3 style="margin-bottom: 0;font-size:16px;font-weight: 500;">{{psName}}</h3>
    </div>
    <a-popover v-if="weatherInfo.areaName" placement="rightBottom" trigger="click">
      <div class="weather-right-card">
        <a-card hoverable size="small" :bodyStyle="{padding: '4px 10px',display:'flex','align-items':'center'}">
          <a-space class="weather-right">
            <span>{{weatherInfo.areaName}}</span>
            <a-col class="weather-icon">
              <img v-if="weatherInfo.iconDir" :src="weatherInfo.iconDir" style="height: 30px;object-fit: contain" />
            </a-col>
            <span v-if="(weatherInfo.minTemp+'') || (weatherInfo.maxTemp+'')">{{weatherInfo.minTemp}}℃~{{weatherInfo.maxTemp}}℃</span>
          </a-space>
        </a-card>
      </div>
      <template slot="content">
        <div class="weather-popper">
          <a-table :data-source="weatherInfo.fifthWeather" :pagination="false" :scroll="{x:500,y:300}" size="small">
            <a-table-column width="150" key="predictDate" data-index="predictDate" title="日期" align="center">
               <template slot-scope="text,record">
                {{moment(record.timeDate).format("yyyy-MM-DD")}}
              </template>
            </a-table-column>
            <a-table-column width="200" key="conditionDay" data-index="conditionDay" title="天气" align="center">
              <template slot-scope="text,record">
                <div class="weather-row">
                  <img style="height: 30px; object-fit: contain;margin-right: 5px;" :src="mapWeatherIconById(record.conditionIdDay)" />
                  <span>{{record.conditionDay}}</span>
                </div>
              </template>
            </a-table-column>
            <a-table-column width="200" key="tempDay" data-index="tempDay" title="气温[℃]" align="center"></a-table-column>
          </a-table>
        </div>
      </template>
    </a-popover>
  </div>
</template>

<script>
import moment from 'moment';
import {
  getAround7DaysWeatherByPsId,
  getFuture15DaysWeatherByPsId
} from '@/api/health/healthapi';
export default {
  props: {
    psId: {
      type: [String, Number],
      required: true
    },
    psName: {
      type: String,
      default: ''
    },
    isAroundDate: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
      loading: true,
      weatherInfo: {}
    };
  },
  mounted () {
    this.editWeatherInfo(this.psId);
  },
  watch: {
    psId () {
      this.editWeatherInfo(this.psId);
    }
  },
  methods: {
    moment,
    editWeatherInfo (
      psId
    ) {
      this.loading = true;
      this.weatherInfo = {};
      let promise = null;
      if (this.isAroundDate) {
        promise = getAround7DaysWeatherByPsId;
      } else {
        promise = getFuture15DaysWeatherByPsId;
      }
      promise({
        psId
      }).then(res => {
        if (res.result_code == 1) {
          let {
            city,
            forecast
          } = res.result_data;
          let fifthWeather = this.isAroundDate ? forecast : forecast.filter((item) => {
            return moment(item.timeDate).isSameOrAfter(new Date(), 'day');
          });
          let todayInfo = forecast.find((item) => {
            return moment(item.timeDate).isSame(new Date(), 'day');
          });
          this.weatherInfo = {
            areaName: city.name,
            iconDir: this.mapWeatherIconById(todayInfo.conditionIdDay),
            minTemp: todayInfo.tempNight,
            maxTemp: todayInfo.tempDay,
            fifthWeather
          };
          this.loading = false;
        } else {
          this.loading = false;
          this.$message.error(res.result_msg);
        }
      });
    },
    mapWeatherIconById (conditionId) {
      if (conditionId >= 0) {
        return require(`@/assets/images/weather/W${conditionId}.png`);
      }
      return null;
    }
  }
};
</script>

<style lang="less" scoped>
  .weather-area {
    flex-grow: 0;
    display: inline-flex;
    align-items: center;
    justify-content: space-between;
   // height: 60px;
    font-size: 16px;
    box-shadow: 0px 1px p4x 0px rgba(21, 60, 104, 0.09);
    border-radius: 4px 4px 0 0;
   // padding: 25px;
    .weather-left {
      font-size: 100%;
      display: flex;
      align-items: center;
    }

    .weather-right-card {
      cursor: pointer;
    }
  }
</style>
