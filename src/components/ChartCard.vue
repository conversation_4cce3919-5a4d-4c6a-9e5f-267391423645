<template>
  <a-card
    :loading="loading"
    :body-style="{ padding: '20px 24px 8px' }"
    :bordered="false"
    :style="{ cursor: path ? 'pointer' : 'default' }"
    @click="openPage"
  >
    <div :class="{ 'solareye-chart': iconname ? true : '' }">
      <div class="chart-left">
        <svg-icon :iconClass="iconname" :style="{fontSize: getFontSize + 'px'}"></svg-icon>
      </div>
      <div class="chart-right">
        <div class="chart-card-header">
          <div class="meta">
            <span class="chart-card-title">{{ title }}</span>
            <span class="chart-card-action" v-if="!iconname">
              <slot name="action"></slot>
            </span>
          </div>
          <div class="total">
            <span>{{ total || total ===0 ? total :'--' }}</span>
            <span class="meta" style="padding-left:10px">{{unit ? unit :''}}</span>
          </div>
        </div>
        <div class="chart-card-content" v-if="content">
              <div class="content-fix">
           <slot  v-bind:content="content"> {{content}}</slot>
          </div>
        </div>
      </div>
    </div>
    <div class="chart-card-footer" v-if="Object.keys(footer).length > 0">
      <div class="field">
        <slot  v-bind:footer="footer">
             {{Object.values(footer)[0]}}
        <span>{{Object.values(footer)[1]}}</span>
        </slot>

      </div>
    </div>
  </a-card>
</template>

<script>
export default {
  name: 'ChartCard',
  props: {
    title: {
      type: String,
      default: ''
    },
    total: {
      type: [String, Number],
      default: ''
    },
    fontSize: {
      type: String,
      default: '72'
    },
    iconname: {
      type: String,
      default: ''
    },
    loading: {
      type: Boolean,
      default: false
    },
    content: {
      type: String,
      default: ''
    },
    path: {
      type: String,
      default: ''
    },
    psId: {
      type: [String, Number],
      default: ''
    },
    unit: {
      type: String,
      default: ''
    },
    footer: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data () {
    return {
      imageSrc: '../../assets/svg/',
      isString: false
    };
  },
  created () {
    this.imageSrc = this.imageSrc + this.imageUrl;
    if (typeof this.footer === 'string') {
      this.isString = true;
    } else {
      this.isString = false;
    }
  },
  computed: {
    getFontSize () {
      return window.innerHeight > 720 ? this.fontSize : '36';
    }
  },
  methods: {
    openPage () {
      if (this.path) {
        this.$router.push({
          path: this.path,
          query: {
            token: this.$route.query.token,
            psId: this.psId ? this.psId : ''
          }
        });
      } else {

      }
    }
  }
};
</script>

<style lang="less" scoped>
.solareye-chart {
  display: flex;

  .svg-icon {
    font-size: 72px;
  }
  .chart-right {
    flex: 1;
    align-items: center;
    padding-left: 16px;
  }
  .chart-left {
    width: fit-content;
  }
}
.chart-card-header {
  position: relative;
  overflow: hidden;
  width: 100%;
  margin-top: 6px;

  .meta {
    position: relative;
    overflow: hidden;
    width: 100%;
    color: "#333333";
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
  }
}

.chart-card-action {
  cursor: pointer;
  position: absolute;
  top: 0;
  right: 0;
}

.chart-card-footer {
  border-top: 1px solid #e8e8e8;
  padding-top: 9px;
  margin-top: 8px;

  > * {
    position: relative;
  }

  .field {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin: 0;
    span {
      float: right;
      color: rgba(0, 0, 0, 0.85);
    }
  }
}

.chart-card-content {
  margin-bottom: 12px;
  position: relative;
  height: 46px;
  width: 100%;

  .content-fix {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
  }
}

.total {
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  white-space: nowrap;
  color: #000;
  margin-top: 4px;
  margin-bottom: 0;
  font-size: 30px;
  line-height: 38px;
  height: 38px;
}
</style>
