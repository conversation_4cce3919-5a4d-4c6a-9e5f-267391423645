<template>
  <a-layout class="layout">
    <template v-if="layoutMode === 'sidemenu'">
      <a-drawer
        v-if="device === 'mobile'"
        :wrapClassName="'drawer-sider ' + navTheme"
        placement="left"
        @close="() => (this.collapsed = false)"
        :closable="false"
        :visible="collapsed"
        width="200px"
      >
        <side-menu
          mode="inline"
          v-if="device === 'mobile'"
          :menus="menus"
          @menuSelect="menuSelect"
          :theme="navTheme"
          :collapsed="false"
          :collapsible="true"
        ></side-menu>
      </a-drawer>
      <side-menu
        v-show="device === 'desktop'"
        mode="inline"
        :menus="menus"
        @menuSelect="myMenuSelect"
        :theme="navTheme"
        :collapsed="collapsed"
        :collapsible="true"
      ></side-menu>
    </template>
    <!-- 下次优化这些代码 -->
    <!-- <template v-else>
      <a-drawer
        v-if="device === 'mobile'"
        :wrapClassName="'drawer-sider ' + navTheme"
        placement="left"
        @close="() => (this.collapsed = false)"
        :closable="false"
        :visible="collapsed"
        width="200px"
      >
        <side-menu
          mode="inline"
          :menus="menus"
          @menuSelect="menuSelect"
          :theme="navTheme"
          :collapsed="false"
          :collapsible="true"
        ></side-menu>
      </a-drawer>
    </template> -->

    <a-layout
      :class="[layoutMode, `content-width-${contentWidth}`]"
      :style="{ paddingLeft: fixSiderbar && isDesktop() ? `${sidebarOpened ? 200 : 80}px` : '0' }"
    >
      <!-- layout header -->
      <global-header
        :mode="layoutMode"
        :menus="menus"
        theme="dark"
        :collapsed="collapsed"
        :device="device"
        @toggle="toggle"
      />

      <!-- layout content -->
      <a-layout-content :style="{ height: '100vh', width: '100vw', paddingTop: fixedHeader ? '56px' : '0' }">
        <slot></slot>
      </a-layout-content>
    </a-layout>
  </a-layout>
</template>

<script>
import SideMenu from '@/components/menu/SideMenu';
import GlobalHeader from '@/components/page/GlobalHeader';
import { triggerWindowResizeEvent } from '@/utils/util';
import { mapState, mapActions } from 'vuex';
import { mixin, mixinDevice } from '@/utils/mixin.js';
import { isSupportWebp } from '@/utils/hasPermission';
export default {
  name: 'GlobalLayout',
  components: {
    SideMenu,
    GlobalHeader
    // update-start---- author:os_chengtgen -- date:20190830 --  for:issues/463 -编译主题颜色已生效，但还一直转圈，显示主题 正在编译 ------
    // // SettingDrawer
    // 注释这个因为在个人设置模块已经加载了SettingDrawer页面
    // update-end ---- author:os_chengtgen -- date:20190830 --  for:issues/463 -编译主题颜色已生效，但还一直转圈，显示主题 正在编译 ------
  },
  mixins: [mixin, mixinDevice],
  data () {
    return {
      collapsed: false,
      activeMenu: {},
      menus: []
    };
  },
  computed: {
    ...mapState({
      // 主路由
      mainRouters: (state) => state.permission.addRouters,
      // 后台菜单
      permissionMenuList: (state) => state.user.permissionList
    })
  },
  watch: {
    sidebarOpened (val) {
      this.collapsed = !val;
    }
  },
  created () {
    let isEnglish = translate.language.getCurrent() == 'english' ? 'english' : '';
    document.getElementsByTagName('body')[0].classList = 'solar-eye-' + this.navTheme + ' ' + isEnglish + (isSupportWebp() ? ' webpa' : '');
    // --update-begin----author:scott---date:20190320------for:根据后台菜单配置，判断是否路由菜单字段，动态选择是否生成路由（为了支持参数URL菜单）------
    // this.menus = this.mainRouters.find((item) => item.path === '/').children;
    this.menus = this.permissionMenuList;
    // 根据后台配置菜单，重新排序加载路由信息
    // --update-end----author:scott---date:20190320------for:根据后台菜单配置，判断是否路由菜单字段，动态选择是否生成路由（为了支持参数URL菜单）------
  },
  methods: {
    ...mapActions(['setSidebar']),
    toggle () {
      this.collapsed = !this.collapsed;
      this.setSidebar(!this.collapsed);
      triggerWindowResizeEvent();
    },
    menuSelect () {
      if (!this.isDesktop()) {
        this.collapsed = false;
      }
    },
    // update-begin-author:taoyan date:20190430 for:动态路由title显示配置的菜单title而不是其对应路由的title
    myMenuSelect (value) {
      // 此处触发动态路由被点击事件
      this.findMenuBykey(this.menus, value.key);
      this.$emit('dynamicRouterShow', value.key, this.activeMenu.meta.title);
      // update-begin-author:sunjianlei date:20191223 for: 修复刷新后菜单Tab名字显示异常
      let storeKey = 'route:title:' + this.activeMenu.path;
      this.$ls.set(storeKey, this.activeMenu.meta.title);
      // update-end-author:sunjianlei date:20191223 for: 修复刷新后菜单Tab名字显示异常
    },
    findMenuBykey (menus, key) {
      for (let i of menus) {
        if (i.path == key) {
          this.activeMenu = { ...i };
        } else if (i.children && i.children.length > 0) {
          this.findMenuBykey(i.children, key);
        }
      }
    }
    // update-end-author:taoyan date:20190430 for:动态路由title显示配置的菜单title而不是其对应路由的title
  }
};
</script>

<style lang="less">
body {
  // 打开滚动条固定显示
  //overflow-y: scroll;

  &.colorWeak {
    filter: invert(80%);
  }
}

section.ant-layout {
  height: 100% !important;
  overflow: hidden;

  &.mobile {
    .ant-layout-content {
      .content {
        margin: 24px 0 0;
      }
    }

    /**
       * ant-table-wrapper
       * 覆盖的表格手机模式样式，如果想修改在手机上表格最低宽度，可以在这里改动
       */
    .ant-table-wrapper {
      .ant-table-content {
        overflow-y: auto;
      }
      .ant-table-body {
        min-width: 800px;
      }
    }
    .sidemenu {
      .ant-header-fixedHeader {
        &.ant-header-side-opened,
        &.ant-header-side-closed {
          width: 100%;
        }
      }
    }

    .topmenu {
      /* 必须为 topmenu  才能启用流式布局 */
      &.content-width-Fluid {
        .header-index-wide {
          margin-left: 0;
        }
      }
    }
    .header,
    .top-nav-header-index {
      .user-wrapper .action {
        padding: 0 12px;
      }
    }
  }

  &.ant-layout-has-sider {
    flex-direction: row;
  }

  .trigger {
    font-size: 22px;
    line-height: 42px;
    padding: 0 18px;
    cursor: pointer;
    transition: color 300ms, background 300ms;

    &:hover {
      background: rgba(255, 255, 255, 0.3);
    }
  }

  .topmenu {
    .ant-header-fixedHeader {
      position: fixed;
      top: 0;
      right: 0;
      z-index: 9;
      width: 100%;
      transition: width 0.2s;

      &.ant-header-side-opened {
        width: 100%;
      }

      &.ant-header-side-closed {
        width: 100%;
      }
    }
    /* 必须为 topmenu  才能启用流式布局 */
    &.content-width-Fluid {
      .header-index-wide {
        max-width: unset;
        margin-left: 24px;
      }

      .page-header-index-wide {
        max-width: unset;
      }
    }
  }

  .sidemenu {
    .ant-header-fixedHeader {
      position: fixed;
      top: 0;
      right: 0;
      z-index: 9;
      width: 100%;
      transition: width 0.2s;

      &.ant-header-side-opened {
        width: calc(100% - 200px);
      }

      &.ant-header-side-closed {
        width: calc(100% - 80px);
      }
    }
  }

  .header {
    height: 64px;
    padding: 0 12px 0 0;
    background: #fff;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
    position: relative;
  }

  .header,
  .top-nav-header-index {
    .user-wrapper {
      float: right;
      height: 100%;

      .action {
        cursor: pointer;
        padding: 0 8px;
        display: inline-block;
        transition: all 0.3s;

        height: 70%;
        line-height: 46px;
        &:hover {
          background: rgba(255, 255, 255, 0.3);
          border-radius: 3px;
        }

        .avatar {
          margin: 20px 10px 20px 0;
          color: #1890ff;
          background: hsla(0, 0%, 100%, 0.85);
          vertical-align: middle;
        }

        .icon {
          font-size: 16px;
          padding: 4px;
        }

        .anticon {
          color: inherit;
        }
      }
    }

    &.dark {
      .user-wrapper {
        .action {
          color: black;

          &:hover {
            background: rgba(0, 0, 0, 0.05);
          }

          .anticon {
            color: inherit;
          }
        }
      }
    }
  }

  &.mobile {
    .top-nav-header-index {
      .header-index-wide {
        .header-index-left {
          .trigger {
            color: rgba(255, 255, 255, 0.85);
            padding: 0 12px;
          }

          .logo.top-nav-header {
            text-align: center;
            width: 56px;
            line-height: 58px;
          }
        }
      }

      .user-wrapper .action .avatar {
        margin: 20px 0;
      }

      &.light {
        .header-index-wide {
          .header-index-left {
            .trigger {
              color: rgba(0, 0, 0, 0.65);
            }
          }
        }
        //
      }
    }
  }

  &.tablet {
    // overflow: hidden; text-overflow:ellipsis; white-space: nowrap;
    .top-nav-header-index {
      .header-index-wide {
        .header-index-left {
          .logo > a {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
  }

  .top-nav-header-index {
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
    position: relative;
    transition: background 0.3s, width 0.2s;

    .header-index-wide {
      width: 100%;
      margin: auto;
     // padding: 0 10px 0 0;
      display: flex;
      height: 56px;
      margin-left: 0 im !important;
      .ant-menu.ant-menu-horizontal {
        border: none;
        height: 64px;
        line-height: 64px;
      }

      .header-index-left {
        flex: 1 1;
        display: flex;
        .logo.top-nav-header {
          width: 250px;
          height: 56px;
          position: relative;
          line-height: 56px;
          transition: all 0.3s;
          overflow: hidden;
          background: url('../../assets/nav_head_bg_light.png') no-repeat;
          background-size: 250px 56px;
          img {
            display: inline-block;
            vertical-align: middle;
            height: 32px;
          }

          h1 {
            color: #fff;
            // display: inline-block;
            vertical-align: top;
            // margin: 0 0 0 12px;
            font-weight: 400;
            font-family: PingFangSC-Medium;
            font-size: 24px;
            letter-spacing: 0.92px;
            text-align: center;
          }
        }
      }

      .header-index-right {
        justify-content: flex-end;
        float: right;
        height: 56px;
        overflow: hidden;
        flex-shrink: 0;
        .action:hover {
          background-color: rgba(0, 0, 0, 0.05);
        }
      }
    }

    &.light {
      background-color: #fff;

      .header-index-wide {
        .header-index-left {
          background: url('');
          .logo {
            h1 {
              color: #002140;
            }
          }
        }
      }
    }

    &.dark {
      .user-wrapper {
        .action {
          color: white;

          &:hover {
            background: rgba(255, 255, 255, 0.3);
          }
        }
      }
      .header-index-wide .header-index-left .trigger:hover {
        background: rgba(255, 255, 255, 0.3);
      }
    }
  }

  // 内容区
  .layout-content {
    margin: 24px 24px 0px;
    height: 64px;
    padding: 0 12px 0 0;
  }
}

.topmenu {
  .page-header-index-wide {
    margin: 0 auto;
    width: 100%;
  }
  :deep(.ant-menu-dark) {
    //background-color: rgb(48, 65, 86);
    background: #3a4049;
    //box-shadow: 0 5px 12px 0 rgba(0, 0, 0, 0.4);
    :deep(.ant-menu-submenu:hover) {
     // background-color: #263445;
      background-color: #4F5763;
    }
    :deep( .ant-menu-item:hover) {
      //background-color: #263445;
      background-color: #4F5763;
    }
  }
  :deep(.ant-menu.ant-menu-dark) {
    .ant-menu-item,.ant-menu-submenu-title {
    font-size: 16px;
  }
  }
  :deep(.ant-menu.ant-menu-dark .ant-menu-item-selected),
  :deep(.ant-menu.ant-menu-dark .ant-menu-submenu-selected) {
    //background-color: #263445;
    // opacity: 0.25;
   // background-image: linear-gradient(180deg, rgba(255, 200, 120, 0.25) 0%, rgba(255, 151, 67, 0.35) 100%);
   // background-color: transparent;
    font-size: 16px;
    //border-bottom: 4px solid #ff8f33;
  }
  :deep(.ant-menu.ant-menu-dark .ant-menu-item-selected i) {
    //color: rgb(24, 144, 255);
    color: #fff;
  }
  :deep(.ant-menu.ant-menu-dark .ant-menu-item-selected span) {
    // color: rgb(24, 144, 255);
    color: #fff;
  }
  :deep(.ant-menu-dark .ant-menu-submenu-active) {
    color: #ffffff !important;
  }
}
.layout .top-nav-header-index.dark .user-wrapper .action:hover {
  background-color: #4F5763 !important; // #263445
}
.layout .top-nav-header-index .dark .user-wrapper .action i {
  color: #ffffff !important;
}
.layout .top-nav-header-index .user-wrapper .action .anticon {
  color: inherit !important;
}
.dark.ant-dropdown-menu {
  background-color: #999999;
}
// drawer-sider 自定义
.ant-drawer.drawer-sider {
  .sider {
    box-shadow: none;
  }

  &.dark {
    .ant-drawer-content {
      background-color: rgb(0, 21, 41);
    }
  }
  &.light {
    box-shadow: none;
    .ant-drawer-content {
      background-color: #fff;
    }
  }

  .ant-drawer-body {
    padding: 0;
  }
}

// 菜单样式
.sider {
  box-shadow: 2px 116px 6px 0 rgba(0, 21, 41, 0.35);
  position: relative;
  z-index: 10;

  &.ant-fixed-sidemenu {
    position: fixed;
    height: 100%;
  }

  .logo {
    height: 64px;
    position: relative;
    line-height: 64px;
    padding-left: 24px;
    -webkit-transition: all 0.3s;
    transition: all 0.3s;
    background: #002140;
    overflow: hidden;

    img,
    h1 {
      display: inline-block;
      vertical-align: middle;
    }

    img {
      height: 32px;
    }

    h1 {
      color: #fff;
      font-size: 18px;
      margin: 0 0 0 8px;
      font-family: 'Chinese Quote', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
        'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
        'Segoe UI Symbol';
      font-weight: 600;
    }
  }

  &.light {
    background-color: #fff;
    box-shadow: 2px 116px 8px 0 rgba(29, 35, 41, 0.05);

    .logo {
      background: #fff;
      box-shadow: 1px 1px 0 0 #e8e8e8;

      h1 {
        color: unset;
      }
    }

    .ant-menu-light {
      border-right-color: transparent;
    }
  }
}

// 外置的样式控制
.user-dropdown-menu-wrapper.ant-dropdown-menu {
  padding: 4px 0;

  .ant-dropdown-menu-item {
    width: 160px;
  }

  .ant-dropdown-menu-item > .anticon:first-child,
  .ant-dropdown-menu-item > a > .anticon:first-child,
  .ant-dropdown-menu-submenu-title > .anticon:first-child .ant-dropdown-menu-submenu-title > a > .anticon:first-child {
    min-width: 12px;
    margin-right: 8px;
  }
}

// 数据列表 样式
.table-alert {
  margin-bottom: 16px;
}

.table-page-search-wrapper {
  .ant-form-inline {
    .ant-form-item {
      display: flex;
      margin-bottom: 24px;
      margin-right: 0;

      .ant-form-item-control-wrapper {
        flex: 1 1;
        display: inline-block;
        vertical-align: middle;
      }

      > .ant-form-item-label {
        line-height: 32px;
        padding-right: 8px;
        width: auto;
      }
      .ant-form-item-control {
        height: 32px;
        line-height: 32px;
      }
    }
  }

  .table-page-search-submitButtons {
    display: block;
    margin-bottom: 24px;
    white-space: nowrap;
    .ant-btn{
      margin-left: 16px;
    }
  }
}

.content {
  .table-operator {
    margin-bottom: 18px;

    button {
      margin-right: 8px;
    }
  }
}
.ant-menu-vertical.ant-menu-sub {
  min-width: auto !important;
}
.ant-menu-dark .ant-menu-vertical.ant-menu-sub li:hover {
  background-color: #001528;
}
.ant-menu-dark .ant-menu-vertical .ant-menu-item {
  padding: 0 20px;
}
.ant-menu-dark.ant-menu-horizontal > .ant-menu-item, .ant-menu-dark.ant-menu-horizontal > .ant-menu-submenu {
  font-size: 16px;
  font-family: PingFangSC-Regular, PingFang SC;
}
.ant-menu-dark.ant-menu-horizontal > .ant-menu-item-selected {
  font-weight: 600;
}
.ant-menu-dark.ant-menu-horizontal .ant-menu-submenu-selected {
  font-weight: 600;
}
// mmzuo add topmenu flex
.ant-menu-submenu-popup.ant-menu-dark {
  border-radius: 0 0 6px 6px;
  top:56px !important;
  ul {
  //  display: flex;
  .solary_right_border:not(:last-child) {
    height: auto;
    margin: 60px 10px 20px;
    border: 1px solid #fff;
    opacity: 0.15;
  }
  .ant-menu-item > a,
  .ant-menu-submenu-title {
   // color: #ff8f33;
    font-size: 16px;
  }
  .ant-menu-item {
    &:hover {
      background: transparent;
      span,
      i {
        color: #fff;
        opacity: 0.7;
      }
    }
  }
  .ant-menu-item-selected {
    background-color: transparent;
    span {
      opacity: 0.7;
    }
  }
  .ant-menu-item-group {
    margin-bottom: 20px;
    &:hover {
      background: transparent;
    }
    .ant-menu-item-group-title {
      color: #ff8f33;
      height: 40px;
      margin: 4px 0;
      font-size: 16px;
    }
    &.isHidden {
      .ant-menu-item-group-title {
        display: none;
      }
      .ant-menu-item-group-list  {
        padding-top: 16px;
      }
    }
    .ant-menu-item-group-list {
      display: flex;
      flex-direction: column;
      align-items: baseline;
      .ant-menu-item-selected {
        background: transparent;
        //  .router-link-active{
        //   color: #FF8F33;;
        //  }
      }
      .ant-menu-item {
        height: 24px;
        line-height: 24px;
        span,
        i {
          opacity: 0.7;
          font-family: PingFangSC-Regular;
          font-size: 16px;
          color: #ffffff;
          text-align: center;
          line-height: 18px;
        }
        &:hover {
          background: transparent;
          span,
          i {
            opacity: 1 !important;
          }
        }
      }
    }
  }
}
}
// end
.ant-menu-submenu-popup.ant-menu-dark .ant-menu-item-selected span {
  //color: rgb(24, 144, 255);
  color: #ff8f33 !important;
  font-size: 16px;
}
.ant-menu-submenu-popup.ant-menu-dark .ant-menu-item-selected i {
  //color: rgb(24, 144, 255);
  color: #ff8f33;
  font-size: 16px;
}
.ant-menu-submenu-popup.ant-menu-dark .ant-menu-sub {
  padding-bottom: 10px;
}
.ant-menu-submenu-popup.ant-menu-dark .ant-menu-item {
        height: 30px;
        line-height: 30px;
        span,
        i {
          opacity: 0.7;
          font-family: PingFangSC-Regular;
          font-size: 16px;
          color: #ffffff;
          text-align: center;
          line-height: 18px;
        }
        &:hover {
          background: transparent;
          span,
          i {
            opacity: 1 !important;
          }
        }
      }
.ant-menu-dark .ant-menu-sub {
  background: rgba(79,87,99, 0.95) !important;
  box-shadow: 0 5px 12px 0 rgba(0,0,0,0.40);
  border-radius: 0 0 6px 6px;
  .ant-menu-submenu-open,
  .ant-menu-submenu-active {
    color: #ffffff !important;
  }
}

.solar-eye-dark{
  .header-index-right {
    background: #24262B !important;
  }
  .logo.top-nav-header {
    background: url('../../assets/nav_head_bg_dark.png') no-repeat !important;
    background-size: 250px 56px !important;
  }
}

</style>
