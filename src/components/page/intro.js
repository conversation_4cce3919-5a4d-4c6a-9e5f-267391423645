import introJs from 'intro.js'; // introjs库
import 'intro.js/introjs.css'; // intro.js的基础样式文件
import 'intro.js/themes/introjs-modern.css';// 主题样式文件
import { hasMenuByUrl } from '@/utils/util';
import { USER_INFO } from '@/store/mutation-types';

export default {
  name: 'intro',
  data () {
    return {
      baseOptions: {
        nextLabel: '下一步', // 下一步按钮的显示名称
        prevLabel: '上一步', // 上一步按钮的显示名称
        // skipLabel: '跳过', // 跳过按钮的显示名称
        doneLabel: '完成', // 结束按钮的显示名称
        tooltipClass: 'tooltipClass', // 引导说明文本框的样式
        highlightClass: 'highlightClass', // 说明高亮区域的样式
        helperElementPadding: 2, // 提示边框的padding
        exitOnEsc: true, // 是否使用键盘Esc退出
        exitOnOverlayClick: false, // 是否允许点击空白处退出 点击叠加层时是否退出介绍
        showStepNumbers: false, // 是否显示说明的数据步骤 是否显示红色圆圈的步骤编号
        keyboardNavigation: true, // 是否允许键盘来操作
        showButtons: true, // 是否按键来操作
        showBullets: false, // 是否使用点点点显示进度 是否显示面板指示点
        showProgress: false, // 是否显示进度条
        scrollToElement: true, // 是否滑动到高亮的区域
        overlayOpacity: 0.6, // 遮罩层的透明度
        positionPrecedence: ['bottom', 'right', 'left', 'top'], // 当位置选择自动的时候，位置排列的优先级
        disableInteraction: true, // 是否禁止与元素的相互关联 是否禁用与突出显示的框内的元素的交互，就是禁止点击
        hintPosition: 'top-middle', // 默认提示位置
        hintButtonLabel: 'Got it', // 默认提示内容
        hidePrev: true, // 在第一步中是否隐藏上一个按钮
        hideNext: false, // 在最后一步中是否隐藏下一个按钮
        steps: [] // 引导步骤
      }
    };
  },
  updated () {
  },
  created () {
  },
  methods: {
    getIntro (options) {
      return introJs().setOptions(
        Object.assign(JSON.parse(JSON.stringify(this.baseOptions)), options)
      );
    },

    // 新手指引
    guide () {
      let count = '';
      let steps = [];
      let logoIntro = this.$refs.logoIntro;
      let userInfo = Vue.ls.get(USER_INFO);
      let arr = userInfo.roles.filter(item => {
        return item.roleCode == 'r10055';
      });
      if (arr.length === 0) {
        logoIntro.isVisible = true;
        logoIntro.isShow = true;
        steps.push({
          element: '#guide-step1',
          intro: '欢迎登录 SolarEye平台！<br>我们的菜单更新啦，点击这里可切换业务平台哦~',
          position: 'right'
        });
      }
      steps.push({
        element: '#guide-step2',
        intro: '菜单导航栏有所调整，调整后功能更聚焦，使用更便捷。一起来看看吧！',
        position: 'bottom'
      });
      // 个人门户主页
      if (hasMenuByUrl('/personal')) {
        steps.push({
          element: '#guide-step3',
          intro: '<span style="color: #7ffdfe">【我的流程】</span>入口搬到这里啦！<br>点击“MORE>”查看更多待办、已办和我的申请哦！',
          position: 'bottom'
        });
      }
      if (hasMenuByUrl('/workPortal')) {
        steps.push({
          element: '#guide-step工作门户',
          intro: '【工作门户】中展示了电站各项重要运维指标、任务进度、流程待办和通知公告。',
          position: 'bottom'
        });
        if (hasMenuByUrl('/taskCenter')) {
          steps.push({
            element: '#guide-step任务中心',
            intro: '【任务中心】承载站端全部运维任务，去【任务创建】创建任务，在【任务大厅】跟踪任务进度。<br><span style="color: #7ffdfe">【交接班】</span>功能搬到<span style="color: #7ffdfe">【数据填报】</span>里了，去点开看看吧！',
            position: 'bottom'
          });
        }
        if (hasMenuByUrl('/orderHall')) {
          steps.push({
            element: '#guide-step工单大厅',
            intro: '【工单大厅】展示全部工单，任务将通过工单执行闭环。',
            position: 'bottom'
          });
        }
        const addApplyOrderHallDOM = document.querySelector('#guide-step6');
        if (addApplyOrderHallDOM) {
          steps = [...steps, ...[
            {
              element: '#guide-step6',
              intro: '【任务视图】展示电站履约任务和安全任务的进展情况，点击右上角图标切换全屏模式！<br>点击“任务视图”、“任务列表”切换显示模式！',
              position: 'right'
            },
            {
              element: '#guide-step7',
              intro: '<span style="color: #7ffdfe">【我的流程】</span>入口搬到这里啦！<br>点击“MORE>>”查看更多待办、已办和我的申请哦！',
              position: 'left'
            }]
          ];
        }
      }
      const homeIntro = this.getIntro({
        steps: steps
      });
      let that = this;
      setTimeout(() => {
        homeIntro.onbeforechange((targetElement) => {
          if ((targetElement.id === 'guide-step1' && count === '2')) {
            logoIntro.isVisible = true;
            logoIntro.isShow = true;
          } else if (count === '1') {
            logoIntro.isVisible = false;
          }
        }).onchange((targetElement) => {
          count = targetElement.id.slice(10);
        }).oncomplete(() => {
          logoIntro.isVisible = false;
          // logoIntro && logoIntro.closeEvent();
        }).onexit(() => {
          logoIntro.isVisible = false;
          if (that.$ls.get('firstTimeLogin')) { that.$refs.modifyPassword && that.$refs.modifyPassword.showModal(); }
          // logoIntro && logoIntro.closeEvent();
        }).start();
      }, 500);
    }

  }
};
