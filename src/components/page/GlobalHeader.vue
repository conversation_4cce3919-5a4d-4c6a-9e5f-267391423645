<template>
<div>
  <a-layout-header
    v-if="!headerBarFixed"
    :class="[fixedHeader && 'ant-header-fixedHeader', sidebarOpened ? 'ant-header-side-opened' : 'ant-header-side-closed', ]"
    :style="{ padding: '0' }" v-webpBg>

    <div v-if="mode === 'sidemenu'" class="header" :class="theme">
      <a-icon
        v-if="device==='mobile'"
        class="trigger"
        :type="collapsed ? 'menu-fold' : 'menu-unfold'"
        @click="toggle"></a-icon>
      <a-icon
        v-else
        class="trigger"
        :type="collapsed ? 'menu-unfold' : 'menu-fold'"
        @click="toggle"/>

      <span v-if="device === 'desktop'">欢迎进入 SolarEye 平台</span>
      <spn v-else>SolarEye</spn>

      <user-menu :theme="theme"/>
    </div>
    <!-- 顶部导航栏模式 -->
    <div v-else :class="['top-nav-header-index', theme]">
      <div class="header-index-wide">
        <div class="header-index-left" :style="topMenuStyle.headerIndexLeft" id="guide-step2">
          <logo ref="logoIntro" class="top-nav-header" :show-title="device !== 'mobile'" :style="topMenuStyle.topNavHeader"/>
          <div :style="topMenuStyle.topSmenuStyle">
            <s-menu
              mode="horizontal"
              :menu="menus"
              :theme="theme" v-webpBg></s-menu>
          </div>
          <!-- <a-icon
            v-else
            class="trigger"
            :type="collapsed ? 'menu-fold' : 'menu-unfold'"
            @click="toggle"></a-icon> -->
        </div>
        <user-menu class="header-index-right" :theme="theme" :style="topMenuStyle.headerIndexRight" @change="changeEvent"/>
      </div>
    </div>
    <modify-password ref="modifyPassword"></modify-password>
  </a-layout-header>
  <audio ref="alertAudioEl" :autoplay="firstLoading" muted preload :src="alertAudio" type="hidden"/>
  <a-button ref="playBtn" @click="startPlay" hidden="hidden"></a-button>
</div>
</template>

<script>
import UserMenu from '../tools/UserMenu';
import SMenu from '../menu/';
import Logo from '../tools/Logo';
import { getIsExistNewAlarmForDep } from '@/api/health/AlarmEvents.js';
import ModifyPassword from '@/views/user/alteration/ModifyPassword.vue';
import { mixin } from '@/utils/mixin.js';
import intro from './intro';
import { EventBus } from '@/components/com/data-role/bus.js';
export default {
  name: 'GlobalHeader',
  components: {
    UserMenu,
    SMenu,
    Logo,
    ModifyPassword
  },
  mixins: [mixin, intro],
  props: {
    mode: {
      type: String,
      // sidemenu, topmenu
      default: 'sidemenu'
    },
    menus: {
      type: Array,
      required: true
    },
    theme: {
      type: String,
      required: false,
      default: 'dark'
    },
    collapsed: {
      type: Boolean,
      required: false,
      default: false
    },
    device: {
      type: String,
      required: false,
      default: 'desktop'
    }
  },
  data () {
    return {
      alarmDialog: false, // 弹窗控制参数  避免打开多个弹窗
      firstLoading: false,
      alertAudio: require('@/assets/images/health/alarm1.mp3'),
      headerBarFixed: false,
      // update-begin--author:sunjianlei---date:20190508------for: 顶部导航栏过长时显示更多按钮-----
      topMenuStyle: {
        headerIndexLeft: {},
        topNavHeader: {},
        headerIndexRight: {},
        topSmenuStyle: {}
      },
      chatStatus: ''
    };
  },
  watch: {
    /** 监听设备变化 */
    device () {
      if (this.mode === 'topmenu') {
        this.buildTopMenuStyle();
      }
    },
    /** 监听导航栏模式变化 */
    mode (newVal) {
      if (newVal === 'topmenu') {
        this.buildTopMenuStyle();
      }
    }
  },
  // 顶部导航栏过长时显示更多按钮-----
  mounted () {
    window.addEventListener('scroll', this.handleScroll);
    if (this.mode === 'topmenu') {
      this.buildTopMenuStyle();
    }
    // 十分钟轮询一次告警信息
    this.getAlarmInfo();
    this.alarmInterval = setInterval(this.getAlarmInfo, 1000 * 60 * 10);
    this.$once('hook:beforeDestroy', () => {
      clearInterval(this.timer);
      this.timer = null;
      clearInterval(this.alarmInterval);
      this.alarmInterval = null;
      EventBus.$off();
    });
  },
  methods: {
    getAlarmInfo () {
      let params = {
        alarmStatus: '01',
        faultStatus: '1',
        depCodes: 'A09'
      };
      getIsExistNewAlarmForDep(params).then(res => {
        if (res.result_data != 0) {
          this.firstLoading = true;
          this.alertAudio = require('@/assets/images/health/alarm' + res.result_data + '.mp3');
          this.$refs.playBtn.$el.click();
          if (!this.alarmDialog) {
            this.toAlarmDetail();
          }
          this.alarmDialog = true;
        }
      });
    },
    startPlay () {
      this.$refs.alertAudioEl.currentTime = 0;
      this.$refs.alertAudioEl && this.$refs.alertAudioEl.play();
    },
    // 告警弹窗提示
    toAlarmDetail () {
      let _this = this;
      this.$confirm({
        title: '告警提示',
        iconType: 'exclamation-circle',
        content: h => <div style='font-weight:bold; font-size: 20px; margin-top: 20px; display: flex; flex-direction: column; align-items: center'><span>您有新的设备告警信息未查看</span><span>请及时查看！</span> </div>,
        okText: '去查看',
        cancelText: '忽略',
        onOk () {
          _this.alarmDialog = false;
          if (_this.$route.name != 'AlarmCenter') {
            _this.$router.push({
              path: '/AlarmCenter'
            });
          }
          setTimeout(() => {
            EventBus.$emit('queryList');
          }, 500);
        },
        onCancel () {
          _this.alarmDialog = false;
        }
      });
    },
    handleScroll () {
      if (this.autoHideHeader) {
        let scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop;
        if (scrollTop > 100) {
          this.headerBarFixed = true;
        } else {
          this.headerBarFixed = false;
        }
      } else {
        this.headerBarFixed = false;
      }
    },
    toggle () {
      this.$emit('toggle');
    },
    // 顶部导航栏过长时显示更多按钮-----
    buildTopMenuStyle () {
      if (this.mode === 'topmenu') {
        this.topMenuStyle.topNavHeader = { 'min-width': this.$refs.logoIntro.isTrue() ? '250px' : '' };
        this.topMenuStyle.topSmenuStyle = { 'width': 'calc(100% - 250px)', background: '#24262B' };
        this.$nextTick(() => {
          let rightWidth = document.getElementsByClassName('header-index-right')[0].offsetWidth + 'px';
          this.topMenuStyle.headerIndexLeft = { 'width': `calc(100% - ${rightWidth})` };
        });
        // if (this.device === 'mobile') {
        //   // 手机端需要清空样式，否则显示会错乱
        //   this.topMenuStyle.topNavHeader = {};
        //   this.topMenuStyle.topSmenuStyle = {};
        //   this.topMenuStyle.headerIndexRight = {};
        //   this.topMenuStyle.headerIndexLeft = {};
        // } else {
        //   let rightWidth = '510px';
        //   this.topMenuStyle.topNavHeader = { 'min-width': this.$refs.logoIntro.isTrue() ? '300px' : '' };
        //   this.topMenuStyle.topSmenuStyle = { 'width': 'calc(100% - 300px)' };
        //   this.topMenuStyle.headerIndexRight = { 'min-width': rightWidth };
        //   this.topMenuStyle.headerIndexLeft = { 'width': `calc(100% - ${rightWidth})` };
        // }
      }
    },
    // update-begin--author:sunjianlei---date:20190508------for: 顶部导航栏过长时显示更多按钮-----
    changeEvent (value) {
      let rightWidth = document.getElementsByClassName('header-index-right')[0].offsetWidth + (value ? 207 : -207) + 'px';
      this.topMenuStyle.headerIndexLeft = { 'width': `calc(100% - ${rightWidth})` };
    }
  }
};
</script>

<style lang="less" scoped>
  /* update_begin author:scott date:20190220 for: 缩小首页布局顶部的高度*/

  @height: 56px;

  .layout {

    .top-nav-header-index {
      background: #24262B;

      .header-index-wide {
        //margin-left: 10px;

        .ant-menu.ant-menu-horizontal {
          height: 40px;
          margin: 8px 0;
          line-height: 40px !important;
        }
        .ant-menu-item-selected {
          border-bottom: 2px solid #000000;
        }
      }
      .trigger {
        line-height: 64px;
        &:hover {
          background: rgba(0, 0, 0, 0.05);
        }
      }
    }

    .header {
      z-index: 2;
      color: white;
      height: @height;
      background-color: @primary-color;
      transition: background 300ms;

      /* dark 样式 */
      &.dark {
        color: #000000;
        box-shadow: 0 0 4px rgba(0, 0, 0, 0.2);
        background-color: white !important;
      }
    }

    .header, .top-nav-header-index {
      &.dark .trigger:hover {
        background: rgba(0, 0, 0, 0.05);
      }
    }
  }

  .ant-layout-header {
    height: @height;
    line-height: @height;
  }
  // /deep/ .ant-menu-item{
  //   font-size: 16px;
  // }

  /* update_end author:scott date:20190220 for: 缩小首页布局顶部的高度*/

</style>
<style>
.highlightClass {
  border:2px;
  border-radius: 8px;
  box-shadow: rgb(250 250 250 / 80%) 0px 0px 1px 2px,
  rgb(33 33 33 / 60%) 0px 0px 0px 5000px!important;
}

.tooltipClass {
  width: 400px;
  font-size: 18px;
  font-weight: 400;
  color: #ffffff;
  line-height: 20px;
  background: rgb(121, 121, 122,0.6);
}
.introjs-bottom-left-aligned {
  left: 45% !important;
}

.introjs-tooltip{
  max-width: 400px !important;
}

.introjs-tooltip-title {
  font-size: 18px;
  font-weight: normal;
  color: #ffffff;
}

.introjs-arrow.top {
  border-color: transparent transparent rgb(121, 121, 122,0.6);
}
.introjs-arrow.left {
  border-color: transparent rgb(121, 121, 122,0.6) transparent transparent;
}
.introjs-arrow.right {
  border-color: transparent transparent transparent rgb(121, 121, 122,0.6);
}
.introjs-skipbutton {
  color: #FFFFFF;
}
</style>
