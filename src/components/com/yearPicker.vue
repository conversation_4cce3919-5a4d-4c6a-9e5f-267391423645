<template>
  <!-- 年度日期控件 -->
  <a-date-picker v-model="year" format="YYYY" :open="isopen" :allowClear="allowClear" placeholder="请选择年份"
   :disabled="disabled" mode="year" @change="panelChangeOne" @openChange="openChangeOne" @panelChange="panelChangeOne">
  </a-date-picker>

</template>

<script>
import moment from 'moment';
export default {
  name: 'yearPicker',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: Object,
      default: null
    },
    allowClear: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    value (val, old) {
      if (val) {
        this.year = moment(val);
      } else {
        this.year = null;
      }
    }
  },
  data () {
    return {
      year: null,
      isopen: false
    };
  },
  mounted () {
    if (this.value) {
      this.year = moment(this.value);
    } else {
      this.year = null;
    }
  },
  methods: {
    moment,
    // 弹出日历和关闭日历的回调
    openChangeOne (status) {
      this.isopen = status;
    },
    // 得到年份选择器的值
    panelChangeOne (value) {
      this.year = value;
      if (value) {
        this.$emit('change', moment(value).format('YYYY'));
      } else {
        this.$emit('change', '');
      }
      this.isopen = false;
    }
  }
};
</script>
