<template>
  <a-tree-select
    v-model="selectId"
    :treeExpandedKeys.sync="treeExpandedKeys"
    show-search
    :multiple="multiple"
    :searchValue.sync="searchValue"
    :maxTagCount="1"
    :tree-checkable="checkable"
    style="width: 100%"
    :disabled="disabled"
    :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
    placeholder="请选择节点"
    :allowClear="allowClear"
    :tree-data="treeData"
    :replace-fields="{children:'children', title:'title', key:'key', value: 'key'}"
    treeNodeFilterProp="title"
    dropdownClassName="dep-tree-select-dropdown"
    @search="search"
    @change="onChange"
    @click="handleVisible($event)">
  </a-tree-select>
</template>

<script>
import { getKpiDepTree } from '@/api/isolarErp/com/funnel';
export default {
  name: 'kpiTreeSelect',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: String,
      default: ''
    },
    // 树的数据范围  1截止到区域，2截止到运维组，3截止到电站
    type: {
      type: String,
      default: '3'
    },
    // 清除
    allowClear: {
      type: Boolean,
      default: true
    },
    // 禁止编辑
    disabled: {
      type: Boolean,
      default: false
    },
    // 将区域置灰
    areaDisabeld: {
      type: Boolean,
      default: false
    },
    // 将区域运维组一起置灰
    areaPewerDisabeld: {
      type: Boolean,
      default: false
    },
    // 是否支持多选
    multiple: {
      type: Boolean,
      default: false
    },
    // 是否支持复选框
    checkable: {
      type: Boolean,
      default: false
    },
    // 是否主页面使用
    simple: {
      type: Boolean,
      default: false
    },
    // 是否要默认显示第一个节点
    showNode: {
      type: Boolean,
      default: false
    },
    isNeedSelect: {
      type: Boolean,
      default: false
    },
    partialTree: {
      type: String,
      default: ''
    },
    isArea: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      selectId: null,
      treeExpandedKeys: [],
      searchValue: '',
      treeData: [],
      nodes: [],
      arrData: [],
      depType: ''
    };
  },
  computed: {
    userInfo () {
      return this.$store.getters.userInfo;
    }
  },
  watch: {
    value (val, old) {
      if (this.multiple) {
        this.selectId = (val ? val.split(',') : []);
      } else {
        this.selectId = val;
      }
    },
    isNeedSelect () {
      this.selectId = '';
      if (!this.isNeedSelect) {
        this.selectDepart(this.arrData);
      }
    },
    isArea () {
      this.selectId = '';
      if (this.isArea) {
        this.selectDepart(this.arrData);
        if (!this.depType && this.type == '1' && this.userInfo.hasAllData != '1') {
          this.treeData = [];
        }
      }
    }
  },
  created () {
    this.loadKpiDepTree();
  },
  mounted () {
    if (this.multiple) {
      this.selectId = (this.value ? this.value.split(',') : []);
    } else {
      this.selectId = this.value;
    }
    if (this.multiple) {
      window.addEventListener('click', this.clearSearchValue, false);
    }
  },
  beforeDestroy () {
    window.removeEventListener('click', this.clearSearchValue, false);
  },
  methods: {
    clearSearchValue () {
      this.searchValue = '';
    },
    handleVisible (el) {
      this.setTreeExpandedKeys();
      this.$nextTick(() => {
        let box = document.querySelector('.ant-select-tree li .ant-select-tree-node-content-wrapper.ant-select-tree-node-selected');
        let dropdown = document.querySelector('.dep-tree-select-dropdown');
        if (box && !dropdown.display) {
          box.scrollIntoView(false);
        }
      });
    },
    setTreeExpandedKeys () {
      let self = this;
      let treeExpandedKeys = this.treeExpandedKeys = [];
      self.$nextTick(() => {
        if (!this.selectId || (Array.isArray(this.selectId) && this.selectId.length == 0)) {
          return;
        }
        let keyId = (this.multiple ? [...this.selectId][0] : this.selectId);

        function find (selectId) {
          let node = self.nodes.find(item => item.key == selectId);
          if (node) {
            treeExpandedKeys.push(node.key);
            if (node.pkey) {
              find(node.pkey);
            }
          }
        }
        find(keyId);
        this.treeExpandedKeys = treeExpandedKeys.reverse();
      });
    },
    // 加载部门树
    loadKpiDepTree () {
      // 1截止到区域，2截止到运维组，3截止到电站
      getKpiDepTree({ 'type': this.type, partialTree: this.partialTree }).then(res => {
        if (res.result_code == '1') {
          this.arrData = res.result_data;
          let arr = [...res.result_data];
          this.handleClosedDisabeld(arr);
          this.treeData = arr;
          if (this.showNode) {
            // 初始化加载时默认顶级节点
            let top = this.treeData[0];
            this.selectId = top.key;
            this.$emit('change', top.id, top.title);
          }
          if (this.isNeedSelect) {
            this.selectDepart(this.arrData);
            if (!this.depType && this.type == '1' && this.userInfo.hasAllData != '1') {
              this.treeData = [];
            }
          }
        } else {
          this.treeData = this.nodes = [];
        }
      }).catch(() => {
        this.treeData = this.nodes = [];
      });
    },
    // 选中所在部门的节点
    selectDepart (arr) {
      arr.forEach(item => {
        if (item.id == this.userInfo.depId) {
          this.depType = item.type;
          if (!item.disabled && item.type == this.type) {
            this.$emit('change', item.id, item.title);
            this.selectId = item.key;
          }
        } else if (item.children) {
          this.selectDepart(item.children);
        } else {
          this.depType = '';
        }
      });
    },
    // 禁用
    loopClosedDisabeld (td) {
      if (this.areaDisabeld) {
        td.disabled = td.type == '1' || td.type == '0';
      }
      if (this.areaPewerDisabeld) {
        td.disabled = td.type == '1' || td.type == '2' || td.type == '0';
      }
      if (td.children && Array.isArray(td.children) && td.children.length) {
        for (let cd of td.children) {
          this.loopClosedDisabeld(cd);
        }
      }
    },
    handleClosedDisabeld (treeData) {
      if (this.areaDisabeld || this.areaPewerDisabeld) {
        for (let td of treeData) {
          this.loopClosedDisabeld(td);
        }
      }
      this.getSimpleNodes(treeData);
    },
    getSimpleNodes (treeData) {
      let nodes = [];
      if (Array.isArray(treeData)) {
        // eslint-disable-next-line no-inner-declarations
        function find (data) {
          data.forEach(node => {
            let o = JSON.parse(JSON.stringify(node));
            delete o.children;
            nodes.push(o);
            if (Array.isArray(node.children)) {
              find(node.children);
            }
          });
        }
        find(treeData);
      }
      this.nodes = nodes;
    },
    onChange (value, label, extra) {
      if (!value) {
        if (this.simple) {
          this.$emit('change', '', '');
        } else {
          this.$emit('change', '');
        }
        this.selectId = (this.multiple ? [] : '');
      } else if (value instanceof Array) {
        this.$emit('change', extra.triggerNode.dataRef.id.join(','));
        this.selectId = value;
      } else {
        this.selectId = (this.multiple ? [value] : value);
        if (this.simple) {
          this.$emit('change', extra.triggerNode.dataRef.id.toString(), label[0]);
        } else {
          this.$emit('change', extra.triggerNode.dataRef.id.toString());
        }
      }
    },
    search (val) {
      this.throttle(this.setExpandedKeys(), 500);
    },
    // 搜索展开节点
    setExpandedKeys () {
      this.treeExpandedKeys = this.nodes.map(item => { return item.key; });
    },
    // 节流throttle
    throttle (func, delay) {
      let timer = null;
      let startTime = Date.now();
      return function () {
        let curTime = Date.now();
        let remaining = delay - (curTime - startTime);
        let context = this;
        let args = arguments;
        clearTimeout(timer);
        if (remaining <= 0) {
          func.apply(context, args);
          startTime = Date.now();
        } else {
          timer = setTimeout(func, remaining);
        }
      };
    }
  }
};
</script>
<style lang="less" scoped>
  :deep(.ant-select) {
    :deep(.ant-select-selection__choice__content) {
      max-width: 90px !important;
    }
  }
</style>
