<template>
  <div class="gbutton-group">
    <template v-for="(v,k) in buttonList">
      <a-divider v-if="k>0" type="vertical" :key="k" />
      <a-button v-if="!v.isSvg" size="default" :class="className" :title="v.tooltipMsg || v.name" :key="k" :icon="v.icon" @click="$emit(v.emit)">
        {{(v.icon ? "" : v.name)}}
      </a-button>
      <div class="div-center gbtn-svg-icon" v-else  @click="$emit(v.emit)" :title="v.name" :key="k">
        <svg-icon :iconClass="v.icon" :style="v.style"></svg-icon>
      </div>
    </template>
    <!--  -->
    <a-divider v-if="dropList.length>0" type="vertical" />
    <a-dropdown placement="bottomLeft" :trigger="['hover']" v-if="dropList.length>0">
      <a class="ant-dropdown-link" @click="e => e.preventDefault()">
        <span v-if='isShowMore'>更多<a-icon type="down" /></span>
        <div class="div-center gbtn-svg-icon" v-else><svg-icon iconClass="dash"></svg-icon></div>
      </a>
      <a-menu slot="overlay">
        <template v-for="(v,k) in dropList">
          <a-menu-item :key="k" class="gbutton-group">
            <a-button v-if="!v.isSvg" size="default" :class="className" :title="v.tooltipMsg || v.name" :icon="v.icon" @click="$emit(v.emit)">
              {{(v.icon ? "" : v.name)}}
            </a-button>
            <div class="div-center gbtn-svg-icon" v-else  @click="$emit(v.emit)" :title="v.name">
              <svg-icon :iconClass="v.icon" :style="v.style"></svg-icon>
            </div>
          </a-menu-item>
        </template>
      </a-menu>
    </a-dropdown>
  </div>
</template>

<script>
export default {
  props: {
    list: {
      type: Array,
      default: () => {
        return [];
      }
    },
    limit: {
      type: Number,
      default: 2
    },
    isShowMore: {
      type: Boolean,
      default: true
    },
    className: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      buttonList: [], // 默认显示按钮
      dropList: [] // 更多内按钮
    };
  },
  watch: {
    list: {
      handler (newVal) {
        let _btnList = [];
        // 循环处理按钮
        Array.from(newVal, (item, index) => {
          // 将类数组对象或可迭代对象转化为数组。
          let show = this.isShow(item.has) && item.show && !(item.disabled);
          if (show) {
            _btnList.push({
              emit: item.emit,
              name: item.name,
              icon: (item.icon ? item.icon : ''),
              isSvg: !!item.isSvg,
              style: item.style || {},
              tooltipMsg: item.tooltipMsg
            });
          }
        });
        let len = _btnList.length;
        if (len > 0) {
          if (len <= this.limit) {
            this.buttonList = _btnList;
            this.dropList = [];
          } else {
            this.buttonList = _btnList.slice(0, this.limit - 1);
            this.dropList = _btnList.slice(this.limit - 1, len);
          }
        } else {
          this.buttonList = [];
          this.dropList = [];
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    isShow: function (perms) {
      if (perms === true) {
        return true;
      } else {
        // 根据权限标识和外部指示状态进行权限判断
        let userAuth = sessionStorage.getItem('LOGIN_USER_BUTTON_AUTH');
        userAuth = (userAuth ? JSON.parse(userAuth) : []);
        let arr = userAuth.filter(item => (item.type == '1' && item.action == perms));
        return arr.length == 1;
      }
    }
  }
};
</script>

<style lang="less" scoped>
.gbutton-group{
  display: flex;
  align-items: center;
  justify-content: center;
  .ant-dropdown-trigger {
    padding: 0 5px !important;
  }
}
:deep(.ant-dropdown-menu-item .ant-btn) {
  margin: auto 8px;
  border: 0;
  background-color: unset;
  min-width: 28px;
  height: 28px;
  box-shadow: 0 0 black;
}
.gbtn-svg-icon {
  font-size: 18px;
  margin: 5px 0px;
}
</style>
