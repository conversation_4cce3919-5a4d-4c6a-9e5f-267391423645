<template>
  <a-tree-select
    v-model="selectId"
    :treeExpandedKeys.sync="treeExpandedKeys"
    show-search
    :multiple="multiple"
    :searchValue.sync="searchValue"
    :maxTagCount="1"
    :tree-checkable="checkable"
    style="width: 100%"
    :disabled="disabled"
    :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
    placeholder="请选择部门"
    :allowClear="allowClear"
    :tree-data="treeData"
    :replace-fields="{children:'children', title:'title', key:'key', value: 'value'}"
    treeNodeFilterProp="title"
    dropdownClassName="dep-tree-select-dropdown"
    @search="search"
    @change="onChange"
    @click="handleVisible($event)">
  </a-tree-select>
</template>
<script>
import { queryDepartTreeList, queryDepartTreeListByOrgCode, deptTree } from '@/api/api';
import { carDeptTree } from '@/api/isolarErp/orderHall';
import { USER_INFO } from '@/store/mutation-types';
export default {
  name: 'depTreeSelect',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: String,
      default: ''
    },
    allowClear: {
      type: Boolean,
      default: true
    },
    all: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    // 将关闭的部门置灰
    closedDisabeld: {
      type: Boolean,
      default: false
    },
    // 是否支持多选
    multiple: {
      type: Boolean,
      default: false
    },
    // 是否支持复选框
    checkable: {
      type: Boolean,
      default: false
    },
    // 是否主页面使用
    simple: {
      type: Boolean,
      default: false
    },
    // 是否要默认显示第一个节点
    showNode: {
      type: Boolean,
      default: false
    },
    // 是否显示关闭的
    show: {
      type: String,
      default: '1'
    },
    // true表示该数据角色下所有部门，不区分该部门下有无电站(使用场景 车辆登记)
    isCarTree: {
      type: Boolean,
      default: () => false
    },
    isExtra: {
      type: Boolean,
      default: false
    },
    isDeptTree: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      selectId: null,
      treeExpandedKeys: [],
      searchValue: '',
      treeData: [],
      nodes: []
    };
  },
  watch: {
    value (val, old) {
      if (this.multiple) {
        this.selectId = (val ? val.split(',') : []);
      } else {
        this.selectId = val;
      }
    }
  },
  created () {
    this.loadDepart();
  },
  mounted () {
    if (this.multiple) {
      this.selectId = (this.value ? this.value.split(',') : []);
    } else {
      this.selectId = this.value;
    }
    if (this.multiple) {
      window.addEventListener('click', (e) => {
        this.searchValue = '';
      }, false);
    }
  },
  methods: {
    handleVisible (el) {
      this.setTreeExpandedKeys();
      this.$nextTick(() => {
        let box = document.querySelector('.ant-select-tree li .ant-select-tree-node-content-wrapper.ant-select-tree-node-selected');
        let dropdown = document.querySelector('.dep-tree-select-dropdown');
        if (box && !dropdown.display) {
          box.scrollIntoView(false);
        }
      });
    },
    setTreeExpandedKeys () {
      let _this = this;
      let treeExpandedKeys = this.treeExpandedKeys = [];
      _this.$nextTick(() => {
        if (!this.selectId || (Array.isArray(this.selectId) && this.selectId.length == 0)) {
          return;
        }
        let keyId = (this.multiple ? [...this.selectId][0] : this.selectId);
        function find (selectId) {
          let node = _this.nodes.find(item => item.key == selectId);
          if (node) {
            treeExpandedKeys.push(node.key);
            if (node.parentId) {
              find(node.parentId);
            }
          }
        }
        find(keyId);
        this.treeExpandedKeys = treeExpandedKeys.reverse();
      });
    },
    // 加载部门树
    loadDepart () {
      const user = Vue.ls.get(USER_INFO);
      if (this.isCarTree) {
        this.getCarTreeFn();
      } else if (this.isDeptTree) {
        this.getTreeData(deptTree);
      } else if (this.all || user.hasAllData == '1') {
        // 0表示不显示关闭的，1表示显示
        queryDepartTreeList({ 'show': this.show, all: this.isExtra ? 1 : 0 }).then(res => {
          if (res.success) {
            let arr = [...res.result];
            this.handleClosedDisabeld(arr);
            this.treeData = arr;
            if (this.showNode) {
              // 初始化加载时默认顶级节点
              let top = this.treeData[0];
              this.selectId = top.value;
              this.$emit('change', top.value);
            }
          } else {
            this.treeData = this.nodes = [];
          }
        }).catch(() => {
          this.treeData = this.nodes = [];
        });
      } else {
        queryDepartTreeListByOrgCode({
          orgCode: user.orgCode,
          show: this.show }).then(res => {
          if (res.success) {
            let arr = res.result ? [...res.result] : [];
            this.handleClosedDisabeld(arr);
            this.treeData = arr;
            if (this.showNode) {
              // 初始化加载时默认顶级节点
              let top = this.treeData[0];
              this.selectId = top.value;
              this.$emit('change', top.value);
            }
          } else {
            this.treeData = this.nodes = [];
          }
        }).catch(() => {
          this.treeData = this.nodes = [];
        });
      }
    },
    getTreeData (func) {
      func({
        show: this.show }).then(res => {
        if (res.success) {
          let arr = res.result ? [...res.result] : [];
          this.handleClosedDisabeld(arr);
          this.treeData = arr;
          if (this.showNode) {
            // 初始化加载时默认顶级节点
            let top = this.treeData[0];
            this.selectId = top.value;
            this.$emit('change', top.value);
          }
        } else {
          this.treeData = this.nodes = [];
        }
      }).catch(() => {
        this.treeData = this.nodes = [];
      });
    },
    loopClosedDisabeld (td) {
      td.disabled = td.status != 1;
      if (td.children && Array.isArray(td.children) && td.children.length) {
        for (let cd of td.children) {
          this.loopClosedDisabeld(cd);
        }
      }
    },
    handleClosedDisabeld (treeData) {
      if (this.closedDisabeld) {
        for (let td of treeData) {
          this.loopClosedDisabeld(td);
        }
      }
      this.getSimpleNodes(treeData);
    },
    getSimpleNodes (treeData) {
      let nodes = [];
      if (Array.isArray(treeData)) {
        // eslint-disable-next-line no-inner-declarations
        function find (data) {
          data.forEach(node => {
            let o = JSON.parse(JSON.stringify(node));
            delete o.children;
            nodes.push(o);
            if (Array.isArray(node.children)) {
              find(node.children);
            }
          });
        }
        find(treeData);
      }
      this.nodes = nodes;
    },
    onChange (value, label, extra) {
      if (!value) {
        if (this.simple) {
          this.$emit('change', '', '');
        } else {
          this.$emit('change', '');
        }
        this.selectId = (this.multiple ? [] : '');
      } else if (value instanceof Array) {
        this.$emit('change', value.join(','));
        this.selectId = value;
      } else {
        this.selectId = (this.multiple ? [value] : value);
        if (this.simple) {
          this.$emit('change', value.toString(), label[0]);
        } else {
          this.$emit('change', value.toString(), extra);
        }
      }
    },
    search (val) {
      this.throttle(this.setExpandedKeys(), 500);
    },
    /*
        搜索展开节点
      */
    setExpandedKeys () {
      this.treeExpandedKeys = this.nodes.map(item => { return item.key; });
    },
    /*
        节流throttle
      */
    throttle (func, delay) {
      let timer = null;
      let startTime = Date.now();
      return function () {
        let curTime = Date.now();
        let remaining = delay - (curTime - startTime);
        let context = this;
        let args = arguments;
        clearTimeout(timer);
        if (remaining <= 0) {
          func.apply(context, args);
          startTime = Date.now();
        } else {
          timer = setTimeout(func, remaining);
        }
      };
    },
    getCarTreeFn () {
      let user = Vue.ls.get(USER_INFO) || {};
      let map = {
        'userId': user.id
      };
      carDeptTree(map).then(res => {
        if (res.result_code == '1') {
          let arr = [...res.result_data];
          this.handleClosedDisabeld(arr);
          this.treeData = arr;
          if (this.showNode) {
            // 初始化加载时默认顶级节点
            let top = this.treeData[0];
            this.selectId = top.value;
            this.$emit('change', top.value);
          }
        } else {
          this.treeData = this.nodes = [];
        }
      }).catch(() => {
        this.treeData = this.nodes = [];
      });
    }
  }
};
</script>
<style lang="less" scoped>
  :deep(.ant-select){
    //width: 160px;
    :deep(.ant-select-selection__choice__content){
      max-width: 90px !important;
    }
  }
</style>
