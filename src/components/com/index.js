import funnel from './funnel.vue';
import SearchModule from './searchModule.vue';
import SearchUnitModule from './searchUnitModule.vue';
import FunnelSelect from './funnelSelect.vue';
import erpButton from './erpButton.vue';
import gButton from './gButton.vue';
import gCascader from './gCascader.vue';
import pagePagination from './pagePagination.vue';
import TabCheck from './TabCheck.vue';
import throttleButton from './throttle-button.vue';
import treeSelect from './proTreeSelect';
import depTreeSelect from './depTreeSelect';
import flowchart from './flowchart';
import backNullify from './backNullify';
import yearRange from './yearRange';
import yearPicker from './yearPicker';
import monthRange from './monthRange';
import tableSort from './tableSort';
import drawerView from './drawerView';
import FlowChartDrawer from './FlowChartDrawer';
import PsTreeSelect from './PsTreeSelect';
import kpiTreeSelect from './kpiTreeSelect';
// import fileUpload from "./fileUpload"
import roleTreeSelect from './roleTreeSelect';
import psaSelect from './data-role/psaSelect';
import psSelect from './data-role/psSelect';
import DetailLayout from './DetailLayout';
import materialsInventoryTree from './materialsInventoryTree';
import fileUploadView from './fileUploadView';
export default {
  install (Vue) {
    Vue.component('funnel', funnel);
    Vue.component('SearchUnitModule', SearchUnitModule);
    Vue.component('SearchModule', SearchModule);
    Vue.component('FunnelSelect', FunnelSelect);
    Vue.component('erpButton', erpButton);
    Vue.component('gButton', gButton);
    Vue.component('pagePagination', pagePagination);
    Vue.component('gCascader', gCascader);
    Vue.component('TabCheck', TabCheck);
    Vue.component('throttleButton', throttleButton);
    Vue.component('treeSelect', treeSelect);
    Vue.component('depTreeSelect', depTreeSelect);
    Vue.component('flowchart', flowchart);
    Vue.component('backNullify', backNullify);
    Vue.component('yearRange', yearRange);
    Vue.component('yearPicker', yearPicker);
    Vue.component('monthRange', monthRange);
    Vue.component('tableSort', tableSort);
    Vue.component('drawerView', drawerView);
    Vue.component('FlowChartDrawer', FlowChartDrawer);
    Vue.component('PsTreeSelect', PsTreeSelect);
    Vue.component('kpiTreeSelect', kpiTreeSelect);
    Vue.component('roleTreeSelect', roleTreeSelect);
    Vue.component('psaSelect', psaSelect);
    Vue.component('psSelect', psSelect);
    Vue.component('DetailLayout', DetailLayout);
    Vue.component('materialsInventoryTree', materialsInventoryTree);
    Vue.component('fileUploadView', fileUploadView);
  }
};
