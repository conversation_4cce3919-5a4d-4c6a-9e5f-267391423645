<template>
  <div v-if="options" :class="wrapClass">
    <div class="se-tab-checbox" @click="selectAllEvent()" :class="{ 'btn-selected': isAllSelect }" v-if="isShowAll">{{showAllName}}</div>
    <div
      class="se-tab-checbox"
      :class="{
        'btn-selected': item.checked,
      }"
      v-bind:selected="selected"
      @click="changeEvent(item)"
      v-for="item in options"
      :key="item.value"
    >
      <span>{{ item[replaceFields.name] }}</span>
      <slot name="item" v-bind:item="item"> {{ item[replaceFields.value] }}</slot>
    </div>
  </div>
  <div v-else>
    <div>
      <div
        class="se-tab-checbox"
        :class="{
          'btn-selected': checked,
        }"
        @click="changeEvent(value, checked)"
      >
        <slot></slot>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'tabCheck',
  data () {
    return {
      isAllSelected: [],
      isAllSelect: true,
      modelType: false // v-model 是否为字符串
    };
  },
  model: {
    prop: 'selected',
    event: 'change'
  },
  props: {
    options: {
      type: Array,
      require: false
    },
    selected: {
      type: [Array, String]
    },
    checked: {
      type: [Boolean, String],
      default: false
    },
    wrapClass: {
      type: [Object, String],
      default: 'tab-inline'
    },
    isShowAll: {
      type: Boolean,
      default: true
    },
    showAllName: {
      type: String,
      default: '全选'
    },
    replaceFields: {
      type: Object,
      require: false,
      default: () => {
        return {
          name: 'name',
          value: 'value'
        };
      }
    }
  },
  components: {},
  created () {
    this.render();
    let selected = this.selected; let isString = typeof (selected) === 'string';
    let arr = isString ? selected.split(',') : selected;
    this.modelType = isString;
    this.$nextTick(() => {
      this.options.map((item) => {
        if (arr.indexOf(item[this.replaceFields.value]) > -1) {
          item.checked = true;
          this.isAllSelected.push(item);
        } else {
          item.checked = false;
        }
      });
      this.isAllSelect = this.getAllSelectState();
    });
  },
  mounted () {},
  methods: {
    changeEvent (item, isSingle) {
      if (isSingle) {
        this.value = value;
        this.$emit('change', value);
      } else {
        item.checked = !item.checked;
        this.getAllSelectValue(false, true);
      }
    },
    getAllSelectState () {
      let isAll = this.isAllSelected.length == this.options.length;
      return !!isAll;
    },
    selectAllEvent () {
      // 全选
      this.isAllSelect = !this.isAllSelect;
      this.getAllSelectValue(this.isAllSelect);
    },
    getAllSelectValue (isAllBtn, isSingleBtn) {
      this.isAllSelected = [];
      this.options.forEach((item) => {
        if (isSingleBtn == undefined) {
          item.checked = isAllBtn;
        }
        if (item.checked) {
          this.isAllSelected.push(item[this.replaceFields.value]);
        }
      });
      this.isAllSelect = this.getAllSelectState();
      this.$emit('change', this.modelType ? this.isAllSelected.join(',') : this.isAllSelected);
    },
    render (h) {
      return this.options.map((item) => {
        var name = item[this.replaceFields.name];
        var value = item[this.replaceFields.value];
        var tabOptions = {
          name: name,
          value: value
        };
        return tabOptions;
      });
    }
  }
};
</script>
<style scoped lang='less'>
.tab-inline {
  display: flex;
  flex-direction: row;

}

</style>
