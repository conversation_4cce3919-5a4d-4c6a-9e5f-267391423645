<!-- 电站列表 -->
<template>
  <a-select
  v-model="idList"
  @change="dataChange"
  @dropdownVisibleChange="search(null)"
  :allowClear="allowClear"
  :maxTagCount="1"
  :maxTagTextLength="32"
  :mode="isMultiple ? 'multiple' : ''"
  show-search
  :filter-option="false"
  @search="search"
  style="width: 100%"
  dropdownClassName="all-psa-dropdown"
  >
    <a-icon slot="suffixIcon" type="down" class="select-icon"/>
    <template slot="placeholder">请选择电站名称</template>
    <template slot="dropdownRender" slot-scope="menu">
      <v-nodes :vnodes="menu" />
      <template v-if="showMore">
        <a-divider style="margin: 4px 0" />
        <div style="padding: 4px 8px; text-align: right" @mousedown="(e) => e.preventDefault()">
          <a-button type="link" :loading="moreLoading" :disabled="moreLoading" @click="loadMore()">更多</a-button>
        </div>
      </template>
    </template>
    <a-select-option
      v-for="item in options"
      :key="item.psId"
      :value="item.psId"
      :disabled="item.disabled"
      :title="item.psName"
      >{{ item.psName }}</a-select-option
    >
  </a-select>
</template>

<script>
import { psPage } from '@/api/api';
export default {
  name: 'MultipleSelect',
  components: {
    VNodes: {
      functional: true,
      render: (h, ctx) => ctx.props.vnodes
    }
  },
  model: {
    prop: 'ids',
    event: 'change'
  },
  props: {
    ids: {
      type: Array,
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: false
    },
    // 运维状态
    maintenanceStatus: {
      type: String,
      default: ''
    },
    isMultiple: {
      type: Boolean,
      default: false
    },
    allowClear: {
      type: Boolean,
      default: false
    },
    topPsIds: {
      type: Array,
      default: () => []
    },
    psaIds: {
      type: Array,
      default: () => []
    },
    // 根据psName判断是否回显不存在的电站 单选时候使用 如编辑 默认回显 下拉删除此选项
    psName: {
      type: String,
      default: ''
    },
    isOnlyOne: {
      type: Boolean,
      default: false
    },
    isRequiredPsaIds: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    topPsIds: {
      handler (val) {
        this.params.topPsIds = val;
        this.search(null);
      },
      deep: true,
      immediate: true
    },
    maintenanceStatus (val, oldVal) {
      this.params.maintenanceStatus = val;
      this.search(null);
    },
    psaIds: {
      handler (val, oldVal) {
        this.params.psaIds = val;
        if (JSON.stringify(val) !== JSON.stringify(oldVal)) {
          this.search(null);
        }
      },
      deep: true,
      immediate: true
    }
  },
  data () {
    return {
      isDel: false,
      idList: [],
      times: null,
      options: [],
      showMore: false,
      moreLoading: false,
      params: {
        curPage: 1,
        size: 100,
        psName: '',
        topPsIds: this.topPsIds,
        psaIds: [],
        maintenanceStatus: this.maintenanceStatus
      }
    };
  },
  created () {
    this.search(null);
  },
  mounted () {
  },
  methods: {
    // 重置
    reset () {
      this.isDel = false;
      this.idList = [];
      this.options = [];
      this.params = {
        curPage: 1,
        size: 100,
        psName: '',
        topPsIds: [],
        psaIds: [],
        maintenanceStatus: ''
      };
      this.search(null);
    },
    /*
        change事件
      */
    dataChange (value) {
      this.$emit('change', value);
      let option = this.options.find((item) => item.psId === value);
      this.$emit('select', option);
      if (!value) {
        this.search(null);
      }
    },
    /*
        查询数据
      */
    getList () {
      let _this = this;
      _this.moreLoading = true;
      return new Promise((resolve, reject) => {
        let paramsList = Object.assign({}, _this.params);
        paramsList.topPsIds = this.params.topPsIds.join(',');
        paramsList.psaIds = this.params.psaIds.join(',');
        psPage(paramsList)
          .then((res) => {
            let data = res.result_data || {};
            _this.showMore = data.hasNextPage;
            let result = Array.isArray(data.list) ? data.list : [];
            // 单个电站时 传入topPsIds 是否在电站列表 是否回显
            let ids = result.map(item => item.psId);
            this.isDel = false;
            if (this.topPsIds && this.topPsIds.length && this.topPsIds[0] && !Array.isArray(this.topPsIds[0]) && !ids.includes(this.topPsIds[0])) {
              this.isDel = true;
              result.unshift({
                psId: this.topPsIds[0],
                psName: this.psName
              });
            }
            if (result && result.length == 1 && _this.isOnlyOne) {
              _this.$emit('change', result[0].psId);
            }
            resolve(result);
            _this.moreLoading = false;
          })
          .catch((err) => {
            reject(err);
            _this.showMore = false;
            _this.moreLoading = false;
          });
      });
    },
    /*
        电站筛选
      */
    search (input) {
      // 删除添加项
      if ((this.isRequiredPsaIds && this.params.psaIds.length > 0) || !this.isRequiredPsaIds) {
        if (this.psName && this.isDel) {
          let templt = [...this.options];
          templt.splice(0, 1);
          this.options = [...templt];
          this.isDel = false;
          // 清空所选项
          this.$emit('clearTopPsIds');
        }
        let { ids, options } = this;
        let _this = this;
        let filterData = () => {
          let selected = [];
          if (!Array.isArray(ids)) {
            ids = [ids];
          }
          // 找出已选项
          if (ids) {
            selected = options.filter((item) => ids.includes(item.psId));
          }
          Object.assign(_this.params, { curPage: 1, psName: input });
          _this
            .getList()
            .then((res) => {
              _this.idList = _this.ids;
              _this.options = Object.freeze(
                selected.length == 0 ? res : selected.concat(res.filter((item) => !ids.includes(item.psId)))
              );
            })
            .catch(() => {
              _this.$forceUpdate();
            });
        };
        this.debounce(filterData, 800);
      } else {
        this.options = [];
      }
    },
    /*
        加载更多
      */
    loadMore () {
      let { value, options } = this;
      if (this.params.curPage >= 5) {
        this.$notification.info({ message: '提示', description: '试试搜索', duration: 3 });
        return;
      }
      this.params.curPage += 1;
      let selected = [];
      // 找出已选项
      if (value) {
        selected = options.filter((item) => value == item.psId);
      }
      this.getList()
        .then((res) => {
          let all = res.concat(options);
          this.options = Object.freeze(selected.concat(all.filter((item) => value != item.psId)));
          this.$forceUpdate();
          let dropdown = document.querySelector('.all-psa-dropdown .ant-select-dropdown-menu');
          dropdown && dropdown.scrollTo(0, 0);
        })
        .catch(() => {
          this.$forceUpdate();
        });
    },
    /*
        防抖
      */
    debounce (fn, wait = 500) {
      if (this.times) {
        clearTimeout(this.times);
        this.times = null;
      }
      this.times = setTimeout(fn, wait);
    }
  },
  beforeDestroy () {
    this.options = [];
    this.times && clearTimeout(this.times);
    this.times = null;
  }
};
</script>

<style lang="less" scoped>
:deep(.ant-btn-link) {
  border: 0;
  color: #ff8f33;
}
:deep(.ant-select-selection--multiple) {
  height: 32px;
}
</style>
