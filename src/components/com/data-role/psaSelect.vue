<!-- 填报页面-电站组件 -->
<template>
  <a-select v-model="psaId" @change="psaChange" :disabled="disabled || !params.deptCode" :allowClear="allowClear" show-search :filter-option="false" @search="psaSearch" style="width:100%;"
    dropdownClassName="all-psa-dropdown">
    <template slot="placeholder">输入关键字搜索</template>
    <template slot="dropdownRender" slot-scope="menu">
      <v-nodes :vnodes="menu"/>
      <template v-if="showMore">
        <a-divider style="margin: 4px 0;" />
        <div style="padding: 4px 8px;text-align:right;" @mousedown="e => e.preventDefault()">
          <a-button type="link" :loading="moreLoading" :disabled="moreLoading" @click="loadMore()">更多</a-button>
        </div>
      </template>
    </template>
    <a-select-option v-for="item in psaOptions" :key="item.id" :value="item.id" :disabled="item.disabled" :title="item.name">{{ item.name }}</a-select-option>
  </a-select>
</template>

<script>
import { EventBus } from './bus.js';
import { relPsas, psas } from '@/api/api';
import { getTreeList } from '@/api/isolarErp/com/funnel';
import { USER_INFO, USER_LCA } from '@/store/mutation-types';
export default {
  components: {
    VNodes: {
      functional: true,
      render: (h, ctx) => ctx.props.vnodes
    }
  },
  model: {
    prop: 'psaId',
    event: 'change'
  },
  props: {
    psaId: {
      type: [String, Number],
      default: undefined
    },
    disabled: {
      type: Boolean,
      default: false
    },
    allowClear: {
      type: Boolean,
      default: false
    },
    // 是否仅展示电站选择
    onlyPsa: {
      type: Boolean,
      default: true
    },
    // 是否禁用已关闭的电站
    isCheck: {
      type: Boolean,
      default: true
    },
    isPsName: {
      type: String,
      default: ''
    },
    excludeHy: { // 是否排除户用
      type: Boolean,
      default: false
    },
    excludeClosed: { // 排除已关闭电站：
      type: Boolean,
      default: false
    },
    isRelPsas: {
      default: true,
      type: Boolean
    },
    isSelectFirst: {
      default: false,
      type: Boolean
    },
    isOnlyOne: {
      default: false,
      type: Boolean
    },
    isSelect: {
      default: false,
      type: Boolean
    }
  },
  watch: {
    getUpdate: {
      immediate: true,
      handler (val, old) {
        if (JSON.stringify(val) != JSON.stringify(old)) {
          let { disabled, psaId } = val;
          if ((disabled && psaId) || (!disabled && this.onlyPsa && !this.isFirst)) this.onUpdate(val, old);
        }
      }
    }
  },
  computed: {
    getUpdate () {
      return {
        'psaId': this.psaId,
        'disabled': this.disabled
      };
    }
  },
  data () {
    return {
      isFirst: false,
      times: null,
      psaOptions: [],
      showMore: false,
      moreLoading: false,
      isSearch: false,
      params: {
        userId: undefined,
        deptCode: undefined,
        pageNo: 1,
        pageSize: 100,
        fuzz: ''
      }
    };
  },
  created () {
    this.params.excludeHy = this.excludeHy;
    this.params.excludeClosed = this.excludeClosed;
    let user = Vue.ls.get(USER_INFO) || {};
    this.params.userId = user.id;
    if (this.onlyPsa) {
      let lca = Vue.ls.get(USER_LCA) || {};
      this.params.deptCode = lca.code;
    }
    // if (this.isPsName) {
    //   this.psaSearch(this.isPsName);
    // }
  },
  mounted () {
    let _this = this;
    /*
        部门树的change事件
      */
    EventBus.$on('updateOptions', (deptCode) => {
      if (_this.params.deptCode == deptCode) return;
      _this.psaId = undefined;
      _this.$emit('change', undefined);
      Object.assign(_this.params, { 'pageNo': 1, 'deptCode': deptCode, 'fuzz': '' });
      _this.getPsaList().then(res => {
        _this.psaOptions = Object.freeze(res);
        _this.$forceUpdate();
      }).catch(() => {
        _this.psaOptions = [];
        _this.$forceUpdate();
      });
    });
  },
  methods: {
    /*
        事件监听
      */
    onUpdate (val, old) {
      let _this = this;
      let fn = () => {
        let { disabled, psaId } = val;
        // 详情
        if (disabled && psaId) {
          let isString = (typeof psaId == 'string');
          _this.psaAllInDep = [];
          getTreeList({ 'initValue': psaId, 'disabled': true }).then(res => {
            _this.psaOptions = res.result_data.map(item => {
              return {
                id: isString ? (item.id).toString() : Number(item.id),
                name: item.name
              };
            });
          }).catch(() => {
            _this.psaOptions = [];
          });
          return;
        }
        _this.isFirst = true;
        // 新增、编辑
        Object.assign(_this.params, {
          'pageNo': 1,
          'fuzz': '',
          'top': psaId
        });
        _this.getPsaList().then(res => {
          _this.psaOptions = Object.freeze(res);
          if (res && res[0] && res[0].status != 3 && _this.isSelectFirst) {
            _this.$emit('change', res[0].id);
          }
          if (res && Array.isArray(res) && res.length == 1 && _this.isOnlyOne) {
            _this.$emit('change', res[0].id);
          }
          // 电站加载完成后通知父级组件加载完成并携带电站数据
          this.$emit('loadPsaFinish', _this.psaOptions);
          _this.$forceUpdate();
        }).catch(() => {
          _this.psaOptions = [];
        });
      };
      this.debounce(fn);
    },
    /*
        change事件
      */
    psaChange (value) {
      if (this.isSelect) {
        this.$emit('change', '');
        return;
      }
      this.$emit('change', value);
      let option = this.psaOptions.find(item => item.id === value);
      this.$emit('select', option);
      if (!value || this.isSearch) {
        this.isSearch = false;
        this.psaSearch(null);
      }
    },
    /*
        查询数据
      */
    getPsaList () {
      let _this = this;
      _this.moreLoading = true;
      return new Promise((resolve, reject) => {
        let isRelPsas = _this.isRelPsas ? relPsas : psas;
        isRelPsas(_this.params).then(res => {
          let data = res.result || {};
          _this.showMore = data.t1;
          let result = Array.isArray(data.t2) ? data.t2 : [];
          if (_this.isCheck) {
            result.forEach(item => {
              item.disabled = item.status == '3';
            });
          }
          resolve(result);
          _this.moreLoading = false;
        }).catch((err) => {
          reject(err);
          _this.showMore = false;
          _this.moreLoading = false;
        });
      });
    },
    /*
        电站筛选
      */
    psaSearch (input) {
      let { psaId, psaOptions } = this;
      let _this = this;
      let filterPsa = () => {
        let selected = [];
        // 找出已选项
        if (psaId) {
          selected = psaOptions.filter(item => psaId == item.id);
        }
        Object.assign(_this.params, { 'pageNo': 1, 'fuzz': input, 'top': undefined });
        _this.getPsaList().then(res => {
          _this.psaOptions = Object.freeze(selected.length == 0 ? res : selected.concat(res.filter(item => psaId != item.id)));
        }).catch(() => {
          _this.$forceUpdate();
        });
      };
      this.isSearch = true;
      this.debounce(filterPsa, 800);
    },
    /*
        加载更多
      */
    loadMore () {
      let { value, psaOptions } = this;
      if (this.params.pageNo >= 5) {
        this.$notification.info({ message: '提示', description: '试试搜索', duration: 3 });
        return;
      }
      this.params.pageNo += 1;
      let selected = [];
      // 找出已选项
      if (value) {
        selected = psaOptions.filter(item => value == item.id);
      }
      this.getPsaList().then(res => {
        let all = res.concat(psaOptions);
        this.psaOptions = Object.freeze(selected.concat(all.filter(item => value != item.id)));
        this.$forceUpdate();
        let dropdown = document.querySelector('.all-psa-dropdown .ant-select-dropdown-menu');
        dropdown && dropdown.scrollTo(0, 0);
      }).catch(() => {
        this.$forceUpdate();
      });
    },
    /*
        防抖
      */
    debounce (fn, wait = 500) {
      if (this.times) {
        clearTimeout(this.times);
        this.times = null;
      }
      this.times = setTimeout(fn, wait);
    }
  },
  beforeDestroy () {
    this.psaOptions = [];
    EventBus.$off('updateOptions');
    this.times && clearTimeout(this.times);
    this.times = null;
    EventBus.$off();
  }
};
</script>

<style lang="less" scoped>
  :deep(.ant-btn-link) {
    border: 0;
    color: #FF8F33;
  }
  :deep(.ant-select-selection--multiple) {
    height: 32px;
  }
</style>
