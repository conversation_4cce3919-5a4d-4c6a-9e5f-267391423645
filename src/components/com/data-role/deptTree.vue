<template>
  <a-tree-select v-model="deptId" :tree-data="treeData" :disabled="disabled" show-search :replace-fields="{title: 'name', key:'id', value: 'id'}"
   :dropdown-style="{ maxHeight: '50vh', overflow: 'auto' }" placeholder="请选择部门" treeNodeFilterProp="title"
   :treeExpandedKeys.sync="expandedKeys" dropdownClassName="role-dept-tree-dropdown" @search="depSearch" @change="depChange" @click="handleVisible($event)">
  </a-tree-select>
</template>

<script>
import { EventBus } from './bus.js';
import { USER_INFO } from '@/store/mutation-types';
import { myDeptTree, getDepdetail } from '@/api/api';
export default {
  model: {
    prop: 'deptId',
    event: 'change'
  },
  props: {
    deptId: {
      type: String,
      default: undefined
    },
    disabled: {
      type: Boolean,
      default: false
    },
    // 是否仅展示部门选择
    onlyDep: {
      type: Boolean,
      default: false
    },
    // 是否只可选择叶子节点
    onlySelectedLeaf: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      times: null,
      nodes: [],
      treeData: [],
      expandedKeys: [],
      top: {},
      hasData: false
    };
  },
  computed: {
    propsUpdate () {
      let { deptId, disabled } = this;
      return {
        'deptId': deptId,
        'disabled': disabled
      };
    }
  },
  watch: {
    propsUpdate: {
      immediate: true,
      handler (val, old) {
        this.onUpdate(val, old);
      }
    }
  },
  methods: {
    /*
        部门树change事件
      */
    depChange (value) {
      this.$emit('change', value);
      if (this.onlyDep) {
        return;
      }
      let node = this.nodes.find(node => node.id == value);
      if (node) EventBus.$emit('updateOptions', node.code);
    },
    /*
        数据监听
      */
    onUpdate (val, old) {
      let _this = this;
      let fn = () => {
        if (JSON.stringify(val) == JSON.stringify(old)) {
          return;
        }
        let { disabled, deptId } = val;
        if (disabled && deptId) {
          getDepdetail({ 'id': deptId }).then(res => {
            let nodes = res.result ? [res.result] : [];
            _this.treeData = nodes.map(node => {
              return {
                id: node.id,
                code: node.orgCode,
                name: node.departName,
                parentId: node.parentId
              };
            });
          }).catch(() => {
            _this.treeData = [];
          });
          return;
        }
        if (!disabled && !_this.hasData) _this.getMyDeptTree();
      };
      this.debounce(fn);
    },
    /* 获取树 */
    getMyDeptTree () {
      let user = Vue.ls.get(USER_INFO) || {};
      let map = {
        'userId': user.id
      };
      let _this = this;
      _this.hasData = true;
      myDeptTree(map).then(res => {
        let treeData = Array.isArray(res.result) ? res.result : (res.result ? [res.result] : []);
        _this.getNodes(treeData);
        let top = treeData[0] || {};
        _this.treeData = treeData;
        _this.expandedKeys = [top.id];
        _this.top = top;
        _this.$forceUpdate();
      }).catch(() => {
        _this.treeData = [];
      });
    },
    /*
        部门筛选事件
      */
    depSearch () {
      let _this = this;
      let find = () => {
        _this.expandedKeys = _this.nodes.map(node => {
          return node.id;
        });
        _this.$forceUpdate();
      };
      this.debounce(find);
    },
    /*
        节点点击事件
      */
    handleVisible () {
      this.setExpandedKeys();
      this.$nextTick(() => {
        let box = document.querySelector('.ant-select-tree li .ant-select-tree-node-content-wrapper.ant-select-tree-node-selected');
        let dropdown = document.querySelector('.role-dept-tree-dropdown');
        if (box && !dropdown.display) {
          box.scrollIntoView(false);
        }
      });
    },
    /*
        设置展开节点
      */
    setExpandedKeys () {
      let expandedKeys = [];
      let _this = this;
      let { deptId, nodes, top } = this;
      let find = (value) => {
        let node = nodes.find(o => o.id == value);
        if (node) {
          expandedKeys.push(node.id);
          node.parentId && find(node.parentId);
        }
      };
      find(deptId);
      _this.expandedKeys = deptId ? expandedKeys : [top.id];
      _this.$forceUpdate();
    },
    /*
        节点平铺
      */
    getNodes (treeData) {
      let nodes = [];
      let onlySelectedLeaf = this.onlySelectedLeaf;
      let fn = (items) => {
        items.forEach(item => {
          Object.assign(item, { order: undefined, type: undefined });
          item.disabled = item.status == '0';
          let o = JSON.parse(JSON.stringify(item));
          o.children = undefined;
          nodes.push(o);
          if (Array.isArray(item.children)) {
            item.disabled = item.disabled || onlySelectedLeaf;
            fn(item.children);
          }
        });
      };
      fn(treeData);
      this.nodes = Object.freeze(nodes);
    },
    /*
        防抖
      */
    debounce (fn, wait = 500) {
      if (this.times) {
        clearTimeout(this.times);
        this.times = null;
      }
      this.times = setTimeout(fn, wait);
    }
  },
  beforeDestroy () {
    this.treeData = this.expandedKeys = this.nodes = [];
    this.times && clearTimeout(this.times);
    this.times = null;
    EventBus.$off();
  }
};
</script>
