<template>
  <div class="solareye_sys_search_module" >
    <a-col v-for="(item, key) in searchItemData"
      :key="item.name"
      :xxl="item.xxl ? item.xxl : 6"
      :xl="item.xl ? item.xl : 8"
      :md="item.md ? item.md : 12"
      v-show="key < 2 || moreShow || !isShowExpand">

      <div class="search-item">
        <span class="search-label">{{item.label}}</span>

        <!-- 下拉选择框 -->
        <template v-if="item.inputType == 'a-select'">
          <a-select allowClear showSearch
            placeholder="请选择"
            optionFilterProp="children"
            :style="{width:inputWidth, height: '32px'}"
            :mode="item.mode"
            :maxTagCount="item.maxTagCount"
            v-model="item.vModel"
            @change="searchItemValChange(item.vModel, item.name)">
            <a-select-option v-for="data in item.options" :key="data.id" :value="data.id">
              {{ data.name }}
            </a-select-option>
          </a-select>
        </template>

        <!-- 开始、结束日期时间选择框 -->
        <template v-else-if="item.inputType == 'a-range-picker'" @click.stop>
          <a-range-picker
            :style="{width:inputWidth}"
            :placeholder="['请选择','请选择']"
            :valueFormat="item.valueFormat"
            :format="item.format"
            :default-value="item.defaultValue"
            :show-time="item.showTime"
            v-model="item.vModel"
            @change="searchItemValChange(item.vModel, item.name)" />
        </template>

        <!-- 级联选择器 -->
        <template v-else-if="item.inputType == 'g-cascader'">
          <g-cascader
            :style="{width:inputWidth}"
            :options="item.options"
            v-model="item.vModel"
            @change="searchItemValChange(item.vModel, item.name)" />
        </template>

        <!-- 普通输入框 -->
        <template v-else-if="item.inputType == 'a-input'">
          <a-input :placeholder="item.placeholder || '请输入'" allow-clear v-model="item.vModel" @change="searchItemValChange(item.vModel, item.name)" />
        </template>

        <!-- 单个日期时间选择框 -->
        <template v-else-if="item.inputType == 'a-date-picker'" @click.stop>
          <a-date-picker
            :style="{width:inputWidth}"
            placeholder="请选择"
            :mode="item.mode"
            :format="item.format"
            :open="item.open"
            v-model="item.vModel"
            @openChange="openChange($event, key)"
            @panelChange="panelChange($event, item, key)" />
        </template>

        <!-- 多选、全选按钮框 -->
        <template v-else-if="item.inputType == 'a-checkbox'" class="check_box">
          <!-- 全选按钮 -->
          <a-checkbox
            :indeterminate="indeterminate(item)"
            :checked="item.checkedList.length == options.length"
            @change="onCheckAllChange($event, item)">全选
          </a-checkbox>
          <a-checkbox
            v-for="(v,k) in item.options" :key="k"
            :checked="item.checkedList.indexOf(v.id) > -1"
            @change="onCheckedChange($event, v.id, item)">
            {{ v.name }}
          </a-checkbox>
        </template>

      </div>
    </a-col>
    <a-col :xxl="6" :xl="8" :md="12">
      <div class="search-item">
        <a-button size="default" style="margin-left: 24px;" @click="resetEvent">重置</a-button>
        <a-button class="solar-eye-btn-primary" style="margin-left: 12px;" @click="seachEvent" size="default">查询</a-button>
        <span class="com-color" @click="changeMoreShowStatus" v-if="searchItemData.length > 2 && isShowExpand" style="margin-left: 12px; margin-top: 5px; cursor:pointer">
          {{ moreShow ? "收起" : "展开" }}
          <a-icon
            :type="moreShow ? 'up' : 'down'"
            style="padding-left: 10px"
          />
        </span>

      </div>
    </a-col>
  </div>
</template>

<script>
export default {
  name: 'searchModule',
  props: {
    // 查询模块输入框类型及初始数据list，每种类型输入框所需参数会有差异，具体可查看相应控件所需参数
    searchItemData: {
      type: Array,
      default: () => {
        return [];
      }
    },

    // 是否显示“展开、收起”，一般都默认为true,不用设置。特殊情况下可通过此参数隐藏不展示，如查询模块外没有电站名称查询条件且总的搜索条件就3个。
    isShowExpand: {
      type: Boolean,
      default: true
    }

    // 重置、确定布局栅格偏移量，使其一直保持在最右侧,需根据搜索框个数进行计算
    // offset6: {
    //   type: Number,
    //   default: 0
    // },
    // offset8: {
    //   type: Number,
    //   default: 16
    // },
    // offset12: {
    //   type: Number,
    //   default: 0
    // },
  },

  data () {
    return {
      inputWidth: '100%',
      // 是否展开
      moreShow: false
    };
  },
  mounted () {
  },
  methods: {
    // 是否展开事件
    changeMoreShowStatus () {
      this.moreShow = !this.moreShow;
      this.$nextTick(() => {
        this.$emit('sendIsMoreShowChange', this.moreShow);
      });
    },

    // 查询条件变化时，向父组件传递变化值
    searchItemValChange (val, name) {
      let data = this.formatSearchData(val, name);
      this.$emit('sendSearchItemChange', data);
    },

    // 格式化查询条件值，多个相同类型输入框时，用name字段区分事件传值
    formatSearchData (val, name) {
      let data = {
        val: val,
        name: name
      };
      return data;
    },

    // 重置事件，使父组件初始化查询模块输入框类型及初始数据list
    resetEvent () {
      this.$emit('resetSearchInput', true);
    },

    // 查询按钮事件，使父组件触发查询操作
    seachEvent () {
      this.$emit('sendSearchVals', true);
    },

    // 有选中数据但不为全选时标志
    indeterminate (item) {
      return item.checkedList.length > 0 && item.checkedList.length < item.options.length;
    },

    // 全选事件
    onCheckAllChange (e, item) {
      if (e.target.checked) {
        // 全部选中
        let _arr = [];
        Array.from(item.options, (ele, index) => {
          // 将类数组对象或可迭代对象转化为数组。
          _arr.push(ele.id);
        });
        item.checkedList = _arr;
      } else {
        // 取消全选
        item.checkedList = [];
      }
      let data = this.formatSearchData(item.checkedList, item.name);
      this.$emit('sendSearchItemChange', data);
    },

    // 筛选栏单选按钮
    onCheckedChange (e, id, item) {
      if (e.target.checked) {
        item.checkedList.push(id);
      } else {
        item.checkedList.splice(item.checkedList.indexOf(id), 1);
      }
      let data = this.formatSearchData(item.checkedList, item.name);
      this.$emit('sendSearchItemChange', data);
    },

    // 单个日期选择事件 开关
    openChange (status, index) {
      this.searchItemData[index].open = status;
    },
    // 单个日期选择 change 事件
    panelChange (value, item, index) {
      let data = this.formatSearchData(value, item.name);
      this.searchItemData[index].vModel = value;
      this.$emit('sendSearchItemChange', data);
      if (item.open) {
        this.searchItemData[index].open = false;
      }
    }

  }
};
</script>
