<!-- 数据角色数-部门、电站拆分 -->
<template>
  <a-row :gutter="24">
    <a-col :span='hasServe?(toggleSearchStatus ? 5 : 8):6'>
      <div class='search-item'>
        <span class='search-label'>业主单位</span>
        <a-select allowClear showSearch
                  placeholder='请选择'
                  :filter-option="filterOption"
                  v-model='params.ownerId'
                  @change='getProjectByOwners'
        >
          <a-select-option @change='params.psaIds=undefined' v-for='data in owners' :key='data.id' :value='data.id'>
            {{ data.name }}
          </a-select-option>
        </a-select>
      </div>
    </a-col>
    <a-col :span='hasServe?(toggleSearchStatus ? 5 : 8):6'>
      <div class='search-item'>
        <span class='search-label'>项目公司</span>
        <a-select allowClear showSearch
                  placeholder='请选择'
                  @change='params.psaIds=undefined'
                  :filter-option="filterOption"
                  v-model='params.ownerProjectId'
        >
          <a-select-option v-for='data in ownerProjects' :key='data.id' :value='data.id'>
            {{ data.name }}
          </a-select-option>
        </a-select>
      </div>
    </a-col>
    <a-col :span='hasServe?(toggleSearchStatus ? 4 : 8):6'>
      <div class='search-item'>
        <span class='search-label'>所属地区</span>
        <g-cascader @change='changeByArea' :options='options' v-model='params.areaList'
                    class="area-select"
                    placeholder='全国' />
      </div>
    </a-col>
    <a-col :span='5' v-if='hasServe' v-show="toggleSearchStatus">
      <div class='search-item'>
        <span class='search-label'>运维商</span>
        <a-select allowClear showSearch
          placeholder='请选择'
          mode="multiple"
          :filter-option="filterOption"
          v-model='params.deptIds'
          :maxTagCount='1' :maxTagTextLength='32'
          @change='getOrderMaintenance'
        >
          <template v-if="!isNewMaintenance">
            <a-select-option v-for='data in maintenance' :key='data.deptId' :value='data.deptId' :title='data.maintenance'>
              {{ data.maintenance }}
            </a-select-option>
          </template>
          <template v-else>
            <a-select-option v-for='data in maintenance' :key='data.id' :value='data.id' :title='data.operatorName'>
              {{ data.operatorName }}
            </a-select-option>
          </template>
        </a-select>
      </div>
    </a-col>
    <a-col v-show="toggleSearchStatus" :span='hasServe?5:6'>
      <div class='search-item'>
        <span class='search-label'>{{ psaLabel }}</span>
        <a-select v-model='params.psaIds' @change='psaChange' @dropdownVisibleChange='psaSearch(null)'
                  :allow-clear='allowClear' :mode='mode' :maxTagCount='1' :maxTagTextLength='32' show-search
                  placeholder='输入关键字搜索' :filter-option='false' @search='psaSearch' class='psa-select-multiple'
                  dropdownClassName='psa-dropdown'>
          <template slot='dropdownRender' slot-scope='menu'>
            <v-nodes :vnodes='menu' />
            <template v-if='showMore'>
              <a-divider style='margin: 4px 0;' />
              <div style='padding: 4px 8px;text-align:right;' @mousedown='e => e.preventDefault()'>
                <a-button type='link' :loading='moreLoading' :disabled='moreLoading' @click='loadMore()'>更多</a-button>
              </div>
            </template>
          </template>
          <a-select-option v-for='item in psaOptions' :key='item.id' :value='item.id' :title='item.name'>{{ item.name
            }}
          </a-select-option>
        </a-select>
      </div>
    </a-col>
  </a-row>
</template>

<script>
import { myDeptTree } from '@/api/api';
import { orderMaintenance, psaListByArea } from '@/api/operations/orderHall';
import { USER_INFO, USER_DEPT_LCA } from '@/store/mutation-types';
import { areaTree } from '@/api/isolarErp/safetyquality/safetyRisk';
import { ownerOfUser, projectOfUser } from '@api/isolarErp/config/archives';
import { getOperator } from '@/api/isolarErp/safetyquality/safecheck';
export default {
  components: {
    VNodes: {
      functional: true,
      render: (h, ctx) => ctx.props.vnodes
    }
  },
  model: {
    prop: 'psaIds',
    event: 'change'
  },
  props: {
    psaIds: {
      default: []
    },
    hasServe: {
      type: Boolean,
      default: false
    },
    // 电站label
    psaLabel: {
      type: String,
      default: '电站'
    },
    excludeHy: { // 是否排除户用
      type: Boolean,
      default: false
    },
    // 是否包含已关闭的电站
    isClose: {
      type: Boolean,
      default: true
    },
    mode: {
      type: String,
      default: 'multiple'
    },
    // 是否清空按钮
    allowClear: {
      type: Boolean,
      default: true
    },
    isNewMaintenance: {
      type: Boolean,
      default: false
    },
    toggleSearchStatus: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
      ownerProjects: [],
      nodes: [],
      treeData: [],
      owners: [],
      expandedKeys: [],
      depValue: undefined,
      options: [],
      maintenance: [],
      showMore: false,
      psaOptions: [],
      params: {
        psaIds: undefined,
        province: undefined,
        city: undefined,
        counties: undefined,
        userId: undefined,
        deptCode: undefined,
        ownerId: undefined,
        pageNo: 1,
        pageSize: 100,
        psaName: '',
        ownerProjectId: undefined,
        excludeHy: undefined,
        isClose: undefined
      },
      moreLoading: false,
      times: null
    };
  },
  mounted () {
    this.queryArea();
    this.getOwners();
    this.getProjectByOwners();
    if (this.hasServe) {
      this.getOrderMaintenance();
    }
  },
  created () {
    this.getPsaList();
    this.params.excludeHy = this.excludeHy;
    this.params.isClose = this.isClose;
  },
  watch: {
  },
  methods: {
    getOrderMaintenance () {
      if (this.isNewMaintenance) {
        getOperator({}).then(res => {
          if (res.result_data && res.result_data.rows) {
            res.result_data.rows.forEach(item => {
              if (item.status == '2') {
                item.operatorName += '（已删除）';
              } else if (item.status == '3') {
                item.operatorName += '（已禁用）';
              }
            });
            this.maintenance = res.result_data.rows;
          } else {
            this.maintenance = [];
          }
        }).catch(() => {
          this.maintenance = [];
        });
      } else {
        orderMaintenance({ ifHuComp: true }).then(res => {
          this.maintenance = res.result_data;
        }).catch(() => {
          this.maintenance = [];
        });
      }
    },
    getPsaList () {
      let _this = this;
      _this.moreLoading = true;
      return new Promise((resolve, reject) => {
        psaListByArea(_this.params).then(res => {
          let data = res.result_data || {};
          _this.showMore = true;
          let result = Array.isArray(data.rows) ? data.rows : [];
          if (_this.isCheck) {
            result.forEach(item => {
              item.disabled = item.status == '3';
            });
          }
          this.psaOptions = this.arrUnique(result, 'id');
          _this.moreLoading = false;
          resolve(result);
        }).catch((err) => {
          reject(err);
          _this.showMore = false;
          _this.moreLoading = false;
        });
      });
    },
    getProjectByOwners (val) {
      this.ownerProjects = [];
      this.params.ownerProjectId = undefined;
      projectOfUser({ ownerId: val }).then(res => {
        res.payload.forEach(item => {
          item.name = item.projectCompany;
        });
        this.ownerProjects = res.payload;
      }).catch(() => {
        this.ownerProjects = [];
      });
    },
    getOwners () {
      ownerOfUser({}).then(res => {
        res.payload.forEach(item => {
          item.name = item.ownerName;
        });
        this.owners = res.payload;
      }).catch(() => {
        this.owners = [];
      });
    },
    queryArea () {
      areaTree({ }).then((res) => {
        this.options = res.result_data[0].children;
      });
    },
    changeByArea (val) {
      console.log('val', val);
      this.params.province = val[0] || undefined;
      this.params.city = val[1] || undefined;
      this.params.counties = val[2] || undefined;
      this.params.psaIds = undefined;
      this.getPsaList();
    },

    /*
        设置展开节点
      */
    setExpandedKeys () {
      let expandedKeys = [];
      let _this = this;
      let { depValue, nodes } = this;
      let find = (value, isParentId = true) => {
        let node = isParentId ? nodes.find(o => o.id == value) : nodes.find(o => o.code == value);
        if (node) {
          expandedKeys.push(node.code);
          node.parentId && find(node.parentId);
        }
      };
      find(depValue, false);
      _this.expandedKeys = expandedKeys;
      _this.$forceUpdate();
    },
    /*
        部门筛选事件
      */
    depSearch () {
      let _this = this;
      let find = () => {
        _this.expandedKeys = _this.nodes.map(node => {
          return node.code;
        });
        _this.$forceUpdate();
      };
      this.debounce(find);
    },
    clearData () {
      this.params.psaIds = [];
      this.params = {
        province: undefined,
        city: undefined,
        deptIds: undefined,
        counties: undefined,
        userId: undefined,
        deptCode: undefined,
        ownerId: undefined,
        pageNo: 1,
        pageSize: 100,
        psaName: '',
        ownerProjectId: undefined,
        excludeHy: undefined,
        isClose: undefined
      };
      // 项目公司重置
      this.getProjectByOwners();
    },
    /*
            节点点击事件
          */
    handleVisible () {
      this.setExpandedKeys();
      this.$nextTick(() => {
        let box = document.querySelector('.ant-select-tree li .ant-select-tree-node-content-wrapper.ant-select-tree-node-selected');
        let dropdown = document.querySelector('.role-tree-select-dropdown');
        if (box && !dropdown.display) {
          box.scrollIntoView(false);
        }
      });
    },
    /*
          电站选择
        */
    psaChange (value) {
      let _this = this;
      let fn = () => {
        let depValue = _this.depValue;
        _this.$emit('change', depValue, value);
        if (!value.length) {
          _this.psaSearch(null);
        }
      };
      this.debounce(fn);
    },
    /*
          电站筛选
        */
    psaSearch (input) {
      let { psaIds } = this.params;
      let psaOptions = this.psaOptions;
      let _this = this;
      let filterPsa = () => {
        let selected = [];
        // 找出已选项
        if (Array.isArray(psaIds) && psaIds.length) {
          selected = psaOptions.filter(item => psaIds.includes(item.id));
        }
        Object.assign(_this.params, { 'pageNo': 1, 'psaName': input });
        _this.getPsaList().then(res => {
          _this.psaOptions = Object.freeze(selected.concat(res.filter(item => !psaIds.includes(item.id))));
          _this.psaOptions = _this.arrUnique(_this.psaOptions, 'id');
          console.log('psa', _this.psaOptions);
        }).catch(() => {
          _this.$forceUpdate();
        });
      };
      this.debounce(filterPsa, 800);
    },
    /*
          防抖
        */
    debounce (fn, wait = 500) {
      if (this.times) {
        clearTimeout(this.times);
        this.times = null;
      }
      this.times = setTimeout(fn, wait);
    },
    /* 获取树 */
    getMyDeptTree () {
      let user = Vue.ls.get(USER_INFO) || {};
      let map = {
        'userId': user.id,
        excludeHy: this.excludeHy
      };
      let _this = this;
      const serviceApi = myDeptTree;
      serviceApi(map).then(res => {
        let deptLca = Vue.ls.get(USER_DEPT_LCA) || {};
        let treeData = (Array.isArray(res.result) ? res.result : (res.result ? [res.result] : []));
        _this.getNodes(treeData);
        _this.treeData = Object.freeze(treeData);
        if (_this.isCarTree) _this.depValue = treeData && treeData[0] && treeData[0].code;
        else _this.depValue = deptLca.code;
        _this.$forceUpdate();
      }).catch(() => {
        _this.treeData = [];
      });
    },
    /*
          节点平铺
        */
    getNodes (treeData) {
      let nodes = [];
      let fn = (items) => {
        items.forEach(item => {
          Object.assign(item, { order: undefined, type: undefined });
          let o = JSON.parse(JSON.stringify(item));
          o.children = undefined;
          nodes.push(o);
          if (Array.isArray(item.children)) {
            fn(item.children);
          }
        });
      };
      fn(treeData);
      this.nodes = Object.freeze(nodes);
    },

    /*
          电站加载更多
        */
    loadMore () {
      let { psaIds } = this.params;
      let psaOptions = this.psaOptions;
      if (this.params.pageNo >= 5) {
        this.$notification.info({ message: '提示', description: '试试搜索', duration: 3 });
        return;
      }
      this.params.pageNo += 1;
      let selected = [];
      // 找出已选项
      if (Array.isArray(psaIds) && psaIds.length) {
        selected = psaOptions.filter(item => psaIds.includes(item.id));
      }
      this.getPsaList().then(res => {
        let all = res.concat(psaOptions);
        this.psaOptions = Object.freeze(selected.concat(all.filter(item => !psaIds.includes(item.id))));
        this.psaOptions = this.arrUnique(this.psaOptions, 'id');
        this.$forceUpdate();
        document.querySelector('.psa-dropdown .ant-select-dropdown-menu').scrollTo(0, 0);
      }).catch(() => {
        this.$forceUpdate();
      });
    },
    /* 默认选中重置 */
    reset () {
      let deptLca = Vue.ls.get(USER_DEPT_LCA) || {};
      this.depValue = deptLca.code;
      this.params.psaIds = [];
    },
    // 对象数组去重
    arrUnique (arr, key) {
      let returnArr = [];
      let obj = {};
      if (arr && arr.length > 0) {
        returnArr = arr.reduce((cur, next) => {
          if (!obj[next[key]]) {
            obj[next[key]] = true && cur.push(next);
          }
          return cur;
        }, []);
      }
      return returnArr;
    },
    filterOption (input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    }
  },
  beforeDestroy () {
    this.psaOptions = this.treeData = this.nodes = this.expandedKeys = [];
    this.times && clearTimeout(this.times);
    this.times = null;
    this.data = null;
  }
};
</script>

<style lang='less' scoped>
.search-item {
  width: 100%;
}

:deep(.ant-btn-link) {
  border: 0;
  color: #FF8F33;
}

.taskContainer {
  display: flex;
  align-items: center;
}

.role-tree-select, .psa-select-multiple {
  width: 100%;
  height: 32px;
  overflow: hidden;
}

:deep(.ant-select-selection--multiple) {
  height: 32px;
  overflow: hidden;
}
.area-select {
  width: 100%;
  position: relative;
  top: 2px;
}
</style>
