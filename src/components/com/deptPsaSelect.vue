<!-- 填报页面-部门电站组件（仅选择部门，仅选择电站，部门电站同时存在） -->
<template>
  <a-row :gutter="24">
    <a-col :span="onlyDep ? 24 : 12" v-if="!onlyPsa">
      <div class="search-item">
        <span class="search-label">{{ depLabel }}</span>
        <a-tree-select v-model="depValue" :tree-data="treeData" show-search :replace-fields="{title: 'name', key:'code', value: 'code'}"
         :dropdown-style="{ maxHeight: '50vh', overflow: 'auto' }" placeholder="请选择部门" treeNodeFilterProp="title"
         :treeExpandedKeys.sync="expandedKeys" class="role-tree-select" dropdownClassName="role-tree-select-dropdown" @search="depSearch" @change="depChange" @click="handleVisible($event)">
        </a-tree-select>
      </div>
    </a-col>
    <a-col :span="onlyPsa ? 24 : 12" v-if="!onlyDep">
      <a-select v-model="psaValue" @change="psaChange" :disabled="disabled" show-search :filter-option="false" @search="psaSearch" style="width:100%;">
        <template slot="dropdownRender" slot-scope="menu">
          <v-nodes :vnodes="menu" />
          <template v-if="psaAllInDep.length">
            <a-divider style="margin: 4px 0;" />
            <div style="padding: 4px 8px;text-align:right;" @mousedown="e => e.preventDefault()">
              <a-button type="link" :loading="moreLoading" :disabled="moreLoading" @click="loadMore()">更多</a-button>
            </div>
          </template>
        </template>
        <a-select-option v-for="item in psaOptions" :key="item.id" :value="item.id" :title="item.name">{{ item.name }}</a-select-option>
      </a-select>
    </a-col>
  </a-row>
</template>

<script>
import { relPsas } from '@/api/api';
import { getTreeList } from '@/api/isolarErp/com/funnel';
import { USER_INFO, USER_LCA } from '@/store/mutation-types';
export default {
  components: {
    VNodes: {
      functional: true,
      render: (h, ctx) => ctx.props.vnodes
    }
  },
  props: {
    depValue: {
      type: String,
      default: undefined
    },
    depLabel: {
      type: String,
      default: '部门'
    },
    psaValue: {
      type: String,
      default: undefined
    },
    psaLabel: {
      type: String,
      default: '电站'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    // 是否仅展示部门选择
    onlyDep: {
      type: Boolean,
      default: false
    },
    // 是否仅展示电站选择
    onlyPsa: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    propsUpdate: {
      immediate: true,
      handler (val, old) {
        this.onUpdate(val, old);
      }
    }
  },
  computed: {
    propsUpdate () {
      return {
        'depValue': this.depValue,
        'psaValue': this.psaValue,
        'disabled': this.disabled,
        'onlyDep': this.onlyDep,
        'onlyPsa': this.onlyPsa
      };
    }
  },
  data () {
    return {
      treeData: [],
      all: [],
      times: null,
      psaOptions: [],
      psaAllInDep: [],
      moreLoading: false,
      isAll: false
    };
  },
  methods: {
    /*
        事件监听
      */
    onUpdate (val, old) {
      let _this = this;
      let fn = () => {
        if (JSON.stringify(val) == JSON.stringify(old)) {
          return;
        }
        let { depValue, psaValue, disabled } = val;
        // 非可选状态
        if (disabled && (depValue || psaValue)) {
          return;
        }
        // 可选状态
        if (disabled && value) {
          _this.isAll = false;
          let isString = (typeof value == 'string');
          _this.psaAllInDep = [];
          getTreeList({ initValue: value, disabled: true }).then(res => {
            _this.psaOptions = res.result_data.map(item => {
              return {
                id: isString ? (item.id).toString() : Number(item.id),
                name: item.name
              };
            });
          }).catch(() => {
            _this.psaOptions = [];
          });
          return;
        }
        if (!disabled && !_this.isAll) _this.getPsaList();
      };
      this.debounce(fn);
    },
    /*
        change事件
      */
    psaChange (value) {
      this.$emit('change', value);
      let option = this.all.find(item => item.id === value);
      this.$emit('select', option);
      if (!value) {
        this.psaSearch(null);
      }
    },
    /*
        查询数据
      */
    getPsaList () {
      let _this = this;
      _this.isAll = true;
      _this.psaOptions = [];
      let loginUser = Vue.ls.get(USER_INFO) || {};
      let lca = Vue.ls.get(USER_LCA) || {};
      let map = {
        'userId': loginUser.id,
        'deptCode': lca.code
      };
      relPsas(map).then(res => {
        let result = Array.isArray(res.result) ? res.result : (res.result ? [res.result] : []);
        _this.all = JSON.parse(JSON.stringify(result));
        _this.psaAllInDep = JSON.parse(JSON.stringify(result));
        _this.loadMore();
      }).catch(() => {
        _this.psaAllInDep = [];
      });
    },
    /*
        电站筛选
      */
    psaSearch (input) {
      let { value, all } = this;
      let _this = this;
      let filterPsa = () => {
        let psaAllInDep = [];
        if (input) {
          psaAllInDep = all.filter(item => item.name.indexOf(input) != -1);
        } else {
          psaAllInDep = JSON.parse(JSON.stringify(all));
        }
        if (value) {
          psaAllInDep = psaAllInDep.filter(item => value != item.id);
          psaAllInDep.unshift(all.find(item => value == item.id));
        }
        _this.psaAllInDep = psaAllInDep;
        _this.psaOptions = _this.psaAllInDep.splice(0, 50);
        _this.$forceUpdate();
      };
      this.debounce(filterPsa);
    },
    /*
        加载更多
      */
    loadMore () {
      let { value, all } = this;
      this.moreLoading = true;
      let selected = all.find(item => value == item.id);
      let spliceList = this.psaAllInDep.splice(0, 50);
      let psaOptions = [...spliceList, ...this.psaOptions].filter(item => item.id != value);
      this.psaOptions = selected ? psaOptions.unshift(selected) : psaOptions;
      this.$forceUpdate();
      this.moreLoading = false;
    },
    /*
        防抖
      */
    debounce (fn, wait = 500) {
      if (this.times) {
        clearTimeout(this.times);
        this.times = null;
      }
      this.times = setTimeout(fn, wait);
    }
  },
  unmounted () {
    this.$data = null;
  },
  beforeDestroy () {
    this.times && clearTimeout(this.times);
    this.times = null;
  }
};
</script>

<style lang="less" scoped>
  :deep(.ant-btn-link){
    border: 0;
    color: #FF8F33;
  }
  :deep(.ant-select-selection--multiple){
    height: 32px;
  }
</style>
