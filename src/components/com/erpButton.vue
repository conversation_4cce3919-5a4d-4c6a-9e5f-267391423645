<!-- 封装的按钮组件，方便传权限值做判断 -->

<template>
  <a-button :size="size" :disabled="disabled" v-bind:class="{'solar-eye-btn-primary': isPrimary}" :icon="icon" :loading="loading" v-if="show(perms)" @click="handleClick">
    {{label}}
  </a-button>
</template>

<script>
export default {
  name: 'erpButton',
  props: {
    icon: { // 图标
      type: String,
      default: ''
    },
    label: { // 按钮显示文本
      type: String,
      default: ''
    },
    size: { // 按钮尺寸
      type: String,
      default: 'default'
    },
    type: { // 按钮类型
      type: String,
      default: 'primary'
    },
    loading: { // 按钮加载标识
      type: Boolean,
      default: false
    },
    disabled: { // 按钮是否禁用
      type: Boolean,
      default: false
    },
    perms: { // 按钮权限标识，外部使用者传入
      type: String,
      default: null
    }
  },
  data () {
    return {
      modifyType: null
    };
  },
  computed: {
    isPrimary () {
      return !!this.label;
    }
  },
  methods: {
    handleClick: function () {
      // 按钮操作处理函数
      this.$emit('click', {});
    },
    show: function (perms) {
      // 根据权限标识和外部指示状态进行权限判断
      return this.showHandle(perms);
    }
  }
};
</script>
