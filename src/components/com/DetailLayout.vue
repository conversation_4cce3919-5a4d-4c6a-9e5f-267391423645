<!-- 数据角色数-部门、电站拆分 -->
<template>
  <div class="detail_layout" :style="{'--line-height': lineHeight}">
    <div class="title" v-show="title">
      <span class="before"></span>{{title}}<slot name="top-title"></slot>
      <span v-if='toggleBtnShow' style='float: right;cursor: pointer;font-size: 14px' @click='handleToggle'>
        {{show?'收起':'展开'}}
        <svg-icon icon-class='up' :style='`transform: rotate(${show?0:180}deg)`'/>
      </span>
    </div>
    <a-row :gutter="24" style="padding:0 16px">
      <template v-for="item in labelList">
        <!-- 自定义插槽数据 -->
        <slot v-if="item.slot" :name="item.slot" v-bind="form">
        </slot>
        <!-- 正常遍历的数据 -->
        <a-col v-else-if="!item.func || (item.func && item.func(form))" :span="item.span ? item.span : 8" :key="item.key" class="detail_layout_content" :push="item.push?item.push: 0" :offset="item.offset?item.offset: 0" :pull="item.pull?item.pull: 0">
            <span class="left" :title="item.labelFunc ? item.label(form):item.label" :style="{'width': labelWidth ? labelWidth : '160px'}" >{{item.labelFunc ? item.label(form):item.label}}</span>
            <div class="right" :class="isEllipsis(item.ellipsis)" v-if="item.type && item.type=='array'">
              {{ getArrayData(form[item.key]) }}
            </div>
            <div class="right" v-else-if="item.type && item.type.indexOf('file') > -1">
              <uploadFile v-if="form[item.key] && form[item.key].length > 0" :disabled="true" v-model="form[item.key]" :listType="getFileType(item.type)"></uploadFile>
              <span v-else>--</span>
            </div>
            <div class="right" v-else :title="getLabel(form[item.key], dictMap[item.dict])" :class="isEllipsis(item.ellipsis)">
              <span :class="{'two_line':item.lineClamp}" :style="{'-webkit-line-clamp':item.lineClamp}">{{ getLabel(form[item.key], dictMap[item.dict]) }}</span>
            </div>
        </a-col>
      </template>
    </a-row>
  </div>
</template>

<script>
import uploadFile from '@/components/com/fileUploadView';
export default {
  props: {
    // 需要的tile
    labelList: {
      type: Array,
      default: () => {
        return [];
      }
    },
    form: {
      type: Object,
      default: () => {
        return {};
      }
    },
    title: {
      type: String,
      default: '基本信息'
    },
    lineHeight: {
      type: String,
      default: '36px'
    },
    labelWidth: {
      type: String,
      default: '160px'
    },
    dictMap: {
      type: [Object, Array],
      default: () => {
        return [];
      }
    },
    toggleBtnShow: {
      type: Boolean,
      default: () => false
    }
  },
  data () {
    return {
      show: true
    };
  },
  components: {
    uploadFile
  },
  computed: {},
  created () {

  },
  methods: {
    isEllipsis (ellipsis) {
      return ellipsis ? 'ellipsis' : '';
    },
    getFileType (file) {
      let fileType = file.indexOf(':') > 0 ? file.split(':') : [];
      return (fileType && fileType.length > 1) ? fileType[1] : 'text';
    },
    getArrayData (items) {
      let label = Array.isArray(items) ? items.join(',') : items;
      return label || '--';
    },
    handleToggle () {
      this.$emit('handleToggle', this.show);
      this.show = !this.show;
    }
  }
};
</script>

<style lang="less">
  .detail_layout {
    :deep(.ant-row) {
      padding: 0 16px;
    }
    .detail_layout_content {
      display: inline-flex;
      flex-direction: row;
      line-height: var(--line-height);
      .left {
        width: 160px;
        text-align: right;
        white-space:nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        padding-right: 8px;
        color: #666;
        font-weight: 550;
        flex: none;
        &::after {
          content: "："
        }
      }

      .right {
        flex: 1;

        &.ellipsis {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          word-wrap: none;
        }
      }
    }

    .title {
      font-size: 16px;
      font-weight: 500;
      color: #5b5b5b;
      padding-bottom: 8px;
      .before {
        width: 4px;
        height: 15px;
        border-left: 4px solid #FF8F33;
        margin-right: 16px;
        border-radius: 0px 2px 2px 0px;
      }
    }
    .two_line {
          width: 100%;
          word-break: break-all;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
    }
  }
  .solar-eye-dark {
    .detail_layout {
      .title, .left, .right {
        color: #fff;
      }
      .title .before {
        border-left-color:#60CAFE;
      }
    }
  }
</style>
