<template>
  <a-range-picker v-model="years" :mode="['year','year']"   format="YYYY" @openChange="openChangeOne"  @panelChange="handlePanelChange" :open="isopen"
   :allowClear="allowClear" @change="handleChange"></a-range-picker>
</template>

<script>
import moment from 'moment';
export default {
  name: 'yearRange',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: Array,
      default: () => {
        return [];
      }
    },
    allowClear: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    value: {
      immediate: true,
      handler: function (val) {
        if (Array.isArray(val)) {
          this.years = val;
        } else {
          this.years = [];
        }
      }
    }
  },
  data () {
    return {
      years: [],
      mode: ['year', 'year'],
      isopen: false
    };
  },
  methods: {
    moment,
    handleChange (value) {
      if (Array.isArray(value) && value.length) {
        value = [moment(value[0]).format('YYYY'), moment(value[1]).format('YYYY')];
      }
      this.years = value;
      this.isopen = true;
    },
    // 弹出日历和关闭日历的回调
    openChangeOne (status) {
      this.isopen = status;
    },
    handlePanelChange (value, mode) {
      // if(!mode[0]) {
      //   value[1] = moment(value[0])
      // }
      // if(!mode[1]) {
      //   value[0] = moment(value[1])
      // }
      if (Array.isArray(value) && value.length) {
        let start = moment(value[0]).valueOf();
        let end = moment(value[1]).valueOf();
        if (start > end) {
          value = [moment(value[1]).format('YYYY'), moment(value[0]).format('YYYY')];
        } else {
          value = [moment(value[0]).format('YYYY'), moment(value[1]).format('YYYY')];
        }
      }
      this.years = value;
      this.isopen = false;
      this.$emit('change', value);
    },
    panelChange (value, mode) {
      this.mode = ['year', 'year'];
      if (Array.isArray(value) && value.length) {
        let start = moment(value[0]).valueOf();
        let end = moment(value[1]).valueOf();
        if (start > end) {
          value = [moment(value[1]).format('YYYY'), moment(value[0]).format('YYYY')];
        } else {
          value = [moment(value[0]).format('YYYY'), moment(value[1]).format('YYYY')];
        }
      }
      this.years = value;
      this.$emit('change', value);
    }
  }
};
</script>
