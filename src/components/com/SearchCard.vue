<!-- 任务大厅 工单大厅 头部卡片组件-->
<template>
  <div class="search_card">
    <template v-for="(item,index) in itemList">
      <div :class="['search-tab', selectId == item.id ? 'active' : '']" :key="index" @click="clickEvent(item.id,item.value,index)"
          :style="{'width': width +'%'}">
        <div class="image-box">
          <img :src="getPng(item.icon)"/>
        </div>
        <div class="search-title">
          <div class="search-type">{{ item.name }}</div>
          <div class="search-num">{{ item.num }}</div>
        </div>
      </div>
    </template>
    <div v-if="itemList.length > 0" class="high-bg" :style="{left: activeIndex * width + '%',width: width +'%'}"></div>
    <div v-if="itemList.length > 0" class="high-border" :style="{left: (activeIndex * width) + 4.3 + '%',width: width/2 + 1.1 +'%'}"></div>
  </div>
</template>

<script>
import { mixin } from '@/utils/mixin';
export default {
  mixins: [mixin],
  data () {
    return {
      selectId: null,
      activeIndex: 0
    };
  },
  props: {
    itemList: {
      type: Array,
      default: () => {
        return [];
      }
    },
    width: {
      type: Number,
      default: 20
    }
  },
  created () {
    this.selectId = this.itemList[0].id;
  },
  methods: {
    clickEvent (id, value, index) {
      this.selectId = id;
      this.activeIndex = index;
      this.$emit('click', value);
    },
    getPng (name) {
      let navTheme = name == 'up' || name == 'down' ? '' : '_' + this.navTheme;
      return require('@/assets/images/taskAndOrder/' + name + navTheme + '.png');
    }
  }
};
</script>

<style lang="less" scoped>
.search_card {
  display: flex;
  height: 120px;
  background: #FFFFFF;
  box-shadow: 0px 2px 11px 0px rgba(0, 0, 0, 0.05);
  border-radius: 4px;

  .search-tab {
    padding: 20px 0 20px 20px;
    height: 100%;
    display: flex;
    cursor: pointer;
    position: relative;

    &:hover {
      background: #f6f6f6;
    }

    &.active:hover {
      background: transparent;
    }

    &:first-child {
      border-top-left-radius: 4px;
      border-bottom-left-radius: 4px;
    }

    .image-box {
      width: 80px;
      margin-right: 12px;
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        width: 72px;
      }
    }

    .search-title {
      flex: 1;
      background: transparent;

      .search-type {
        color: #333333;
        font-size: 14px;
        margin-right: 8px;
        font-weight: 600;
      }

      .search-num {
        font-weight: 600;
        color: #333333;
        line-height: 42px;
        font-size: 34px;
        margin: 4px 0;
      }
    }
  }

  .high-bg {
    height: 90px;
    background: linear-gradient(180deg, #FFFFFF 0%, #FF8F33 100%);
    opacity: 0.1;
    position: absolute;
    top: 30px;
    cursor: pointer;
  }

  .high-border {
    height: 3px;
    background: #FF8F33;
    border-radius: 2px;
    position: absolute;
    top: 117px;
    transition: left 0.2s;
    cursor: pointer;
  }

  .search-tab + .search-tab::after {
    height: 74px;
    width: 1px;
    margin: 28px 0px 28px -20px;
    border-left: 1px solid #EEEEEE;
    content: '';
    position: absolute;
    top: 0;
  }
}

@media screen and (min-width: 1366px) and (max-width: 1830px) {
  .search_card {
    .search-tab {
      z-index: 1;
      padding: 20px 8px;
    }
    .item-tab + .item-tab::after {
      margin: 28px 0px 28px -8px;
    }
  }
}

.solar-eye-dark {
  .search_card {
    background: #17202F;
    box-shadow: 0px 2px 11px 0px rgba(0, 0, 0, 0.05);

    .search-tab {
      z-index: 1;

      .search-type, .search-num {
        color: #FFFFFF;
      }
    }
  }

  .search-tab:hover {
    background: #1A273B;
  }

  .active:hover {
    background: transparent;
  }

  .high-bg {
    background: linear-gradient(180deg, rgba(35, 51, 78, 0) 0%, #23334E 100%);
    opacity: 1;
  }

  .high-border {
    background: #60CAFE;
  }

  .search-tab + .search-tab::after {
    border-left: 1px solid #364457;
  }
}
</style>
