<template>
  <div class="approve-container">
    <div class="order-dispose">
      <div class="title-box">
        <span class="before"></span>
        <span>审核信息</span>
      </div>
    </div>
    <a-form-model
      ref="approveFormRef"
      :model="dataForm"
      :rules="rules"
      :labelCol="{ style: 'width: 160px' }"
      :wrapperCol="{ style: 'width: calc(100% - 160px)' }"
    >
      <a-row>
        <a-col :xl="24" :sm="24" :xs="24">
          <a-form-model-item label="审核结论" prop="auditStatus">
            <a-radio-group v-model="dataForm.auditStatus" @change="approveStatusChange">
              <a-radio :value="1"> 通过 </a-radio>
              <a-radio :value="0"> 不通过 </a-radio>
            </a-radio-group>
          </a-form-model-item>
        </a-col>
        <a-col :xl="24" :sm="24" :xs="24">
          <a-form-model-item label="备注" prop="auditOpinion">
            <a-textarea
              v-model="dataForm.auditOpinion"
              :maxLength="100"
              @blur="dataForm.auditOpinion = $trim($event)"
              :placeholder="dataForm.auditStatus == 0 ? '请输入不通过原因' : '请输入备注'"
              :auto-size="{ minRows: 4, maxRows: 6 }"
            />
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
  </div>
</template>

<script>
export default {
  name: 'Approve',
  data () {
    return {
      dataForm: {
        auditStatus: 1,
        auditOpinion: undefined
      },
      rules: {
        auditStatus: [{ required: true, message: '请选择审核结论', trigger: 'change' }],
        auditOpinion: [{ required: false, message: '请输入不通过原因', trigger: 'change' }]
      }
    };
  },
  methods: {
    // 审批 change
    approveStatusChange (val) {
      let value = val.target.value;
      this.rules.auditOpinion[0].required = value == 0;
    },
    // 保存校验
    saveApprove () {
      return new Promise((resolve, reject) => {
        this.$refs.approveFormRef.validate((valid) => {
          if (valid) {
            resolve(this.dataForm);
          } else {
            reject(new Error('请校验必填字段是否填写'));
          }
        });
      });
    }
  }
};
</script>

<style lang="less" scoped>
.approve-container{
  margin-top: 16px;
  .title-box {
    padding-bottom: 8px;
  }
}
:deep(.ant-form-item) {
  width: 100%;
  display: inline-flex;
}
</style>
