<!-- 封装的按钮组件，方便传权限值做判断 -->

<template>
 <a-button v-if="show(perms)" :size="size" :disabled="disabled" :icon="icon" :loading="loading"
   v-bind:class="{'solar-eye-btn-primary' : isPrimary}" @click="handleClick" :type="type">
    {{label}}
  </a-button>
</template>

<script>
export default {
  name: 'throttleButton',
  props: {
    icon: { // 图标
      type: String,
      default: ''
    },
    label: { // 按钮显示文本
      type: String,
      default: ''
    },
    size: { // 按钮尺寸
      type: String,
      default: 'default'
    },
    loading: { // 按钮加载标识
      type: Boolean,
      default: false
    },
    // 按钮颜色风格
    type: {
      type: String,
      default: 'primary'
    },
    disabled: { // 按钮是否禁用
      type: Boolean,
      default: false
    },
    perms: { // 按钮权限标识，外部使用者传入
      type: String,
      default: null
    },
    // 节流时间
    delay: {
      type: Number,
      default: 1000
    }
  },
  data () {
    return {
      isRun: true // 是否可执行
    };
  },
  computed: {
    isPrimary () {
      return !!this.label;
    }
  },
  methods: {
    handleClick: function () {
      let self = this;
      if (!self.isRun || self.disabled) {
        return;
      }
      // 按钮操作处理函数
      // self.$message.info("正在下载，请稍候...");
      self.$emit('click', {});
      // 首次点击后设置不可执行
      self.isRun = false;
      setTimeout(() => {
        // 最后在setTimeout执行完毕后再把标记设置为true(关键)表示可以执行下一次循环了。当定时器没有执行的时候标记永远是false，在开头被return掉
        self.isRun = true;
      }, self.delay);
    },
    // 权限控制
    show: function (perms) {
      if (!perms) {
        return true;
      }
      // 根据权限标识和外部指示状态进行权限判断
      return this.showHandle(perms);
    }
  }
};
</script>
