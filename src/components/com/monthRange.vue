<template>
  <!-- 月度区间 -->
  <a-range-picker v-model="months" :mode="mode" :disabled="disabled" format="YYYY-MM" valueFormat="YYYY-MM" :allowClear="allowClear" @panelChange="panelChange"/>

</template>

<script>
import moment from 'moment';
export default {
  name: 'monthRange',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: Object,
      default: null
    },
    allowClear: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    value: {
      immediate: true,
      handler: function (val) {
        if (Array.isArray(val)) {
          this.months = val;
        } else {
          this.months = [];
        }
      }
    }
  },
  data () {
    return {
      months: [],
      mode: ['month', 'month']
    };
  },
  methods: {
    moment,
    panelChange (value, mode) {
      this.mode = ['month', 'month'];
      if (Array.isArray(value) && value.length) {
        value = [moment(value[0]).format('YYYY-MM'), moment(value[1]).format('YYYY-MM')];
      }
      this.months = value;
      this.$emit('change', value);
    }
  }
};
</script>
