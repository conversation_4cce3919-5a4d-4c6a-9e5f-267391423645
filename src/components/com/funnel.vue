<!-- 漏斗筛选条件--全局组件 -->
<template>
  <div class="com-project-tree" :style="{ height: height - 56 - 40 - 24 + 'px' }">
    <div class="top-search">
      <a-input-search style="width: 100%" @change="onChange" />
    </div>
    <a-button @click="expandedAll" class="solar-eye-btn-primary-line" style="width: 100%; margin: 10px 0">
      {{ expandedType ? '收起' : '展开全部' }}
    </a-button>
    <a-spin :spinning="spinning"  :style="{ height: height - 56 - 40 - 24 - 100 + 'px' }">

    <a-tree v-model="selectId" :selected-keys="selectedKeys" :expandedKeys.sync="expandedKeys"
      :defaultExpandedKeys="expandedKeys" @expand="expand" :tree-data="treeNodes" @select="onSelect"
      :style="{ maxHeight: height - 56 - 40 - 24 - 100 + 'px' }"
      :replace-fields="{ children: 'children', title: 'title', key: isEntityStation ?'zid' : 'value' }" class="solar-eye-tree" />
         </a-spin>
  </div>
</template>

<!-- 二.JS脚本 -->

<script>
// 引入封装调接口的方法文件(对象里放方法名)
import { queryDepartTreeList, queryDepartTreeListByOrgCode, deptTree } from '@/api/api';
import { USER_INFO } from '@/store/mutation-types';
import store from '@/store/index';
import debounce from 'lodash/debounce';
export default {
  name: 'funnel',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: [String, Number],
      required: false
    },
    // 项目树、部门树区分
    isproject: {
      type: Boolean,
      default: true
    },
    // 是否显示全部电站档案
    showAllPsa: {
      type: Boolean,
      default: false
    },
    // 是否按权限查询数据
    all: {
      type: Boolean,
      default: false
    },
    objTreeData: {
      type: Object,
      default: () => {
        return {};
      }
    },
    isEntityStation: {
      type: Boolean,
      default: false
    },
    isExpandedAll: { // 是否默认展开所有
      type: Boolean,
      default: false
    },
    isDeptTree: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      stationName: '',
      dataSource: [],
      consoleNodes: [],
      treeNodes: [],
      dataList: [],
      selectedKeys: [],
      expandedKeys: [],
      selectId: '',
      allKeys: [],
      depIds: [],
      expandedType: false,
      height: document.body.clientHeight,
      spinning: false
    };
  },
  created () {

  },
  watch: {
    value (val, old) {
      this.init(val);
    },
    treeType (val, old) {
      this.loadTree();
    }
  },
  mounted () {
    this.init(this.value);
    this.loadTree();
    window.addEventListener('resize', this.heightResize, true);
  },
  deactivated () {
    window.removeEventListener('resize', this.heightResize, true);
  },
  beforeDestroy () {
    window.removeEventListener('resize', this.heightResize, true);
  },
  methods: {
    heightResize: debounce(function () {
      this.height = window.innerHeight;
    }, 400),
    init (val) {
      try {
        val = val.toString();
      } catch (e) {
        val = '';
      }
      this.selectId = val;
      if (val) {
        this.selectedKeys = val.toString() ? [val] : [];
        if (val.toString()) {
          this.expandedKeys.push(val);
        }
      } else {
        this.selectedKeys = [];
        this.expandedKeys = [];
      }
    },
    // 展开或收起
    expandedAll () {
      let keys = this.expandedType ? [] : this.allKeys;
      this.expand(keys, {});
      this.expandedType = !this.expandedType;
    },
    onSelect (selectedKeys, info) {
      // 取消偶次点击取消选中的问题
      if (selectedKeys && selectedKeys.length > 0) {
        this.selectedKeys = selectedKeys;
        this.expandedKeys.push(selectedKeys[0].toString());
        // 返回id、name、
        let dataRef = info.node.dataRef;
        let node = {};
        if (this.isproject) {
          node = this.getNode(dataRef);
        }
        this.$emit('change', selectedKeys[0].toString());
        if (this.isEntityStation) {
          this.$emit('listenChange', dataRef
          );
          return;
        }
        this.$emit('listenChange', selectedKeys[0].toString(), dataRef.title, node);
      }
    },
    loadTree () {
      const self = this;
      if (this.isEntityStation) {
        this.getEntityStationList();
        return;
      }
      // 项目树
      if (self.isproject) {
        if (this.objTreeData.hasOwnProperty('allData')) {
          this.treeNodes = this.objTreeData.treeData;
        } else {
          this.getProjectTreeList();
        }
      } else {
        // 部门树
        this.getDepartTree();
      }
    },
    getEntityStationList () {
      //      let arr = store.getters.realStationTreeList
      // let realStationTreeList = typeof arr === 'string' ? JSON.parse(arr) : arr
      // if (realStationTreeList.length === 0) {
      this.spinning = true;
      store
        .dispatch('GetRealStationTreeList')
        .then((res) => {
          this.spinning = false;
          if (res.result_code == '1') {
            if (res.result_data.length === 0) {
              return;
            }
            this.dealTreeData({
              data: res.result_data,
              isEntityStation: true
            });
          } else {
            this.treeData = [];
            this.allData = [];
          }
        })
        .catch((err) => {
          notification.error({
            message: '提示',
            description: err || '该用户下暂无实体电站树'
          });
        });
      // } else {
      //   this.dealTreeData({
      //     data: realStationTreeList,
      //     isEntityStation: true
      //   })
      // }
    },
    getProjectTreeList () {
      // const user = Vue.ls.get(USER_INFO);
      // const map = {
      //   userId: user.id
      // };
      let self = this;
      let arr = store.getters.projectTreeList;
      let projectTreeList = typeof arr === 'string' ? JSON.parse(arr) : arr;
      if (projectTreeList.length === 0) {
        store.dispatch('GetProjectTreeList', this.all, this.showAllPsa).then((res) => {
          if (res.result_code == '1') {
            if (res.result_data.length === 0) {
              return;
            }
            this.dealTreeData({
              data: res.result_data
            });
          }
        })
          .catch(() => {
            self.treeNodes = self.consoleNodes = [];
          });
      } else {
        this.dealTreeData({
          data: projectTreeList
        });
      }
    },
    dealTreeData (obj) {
      let self = this;
      let keys = [];
      let arr = obj.data.filter((item) => item.projectId);
      arr.forEach((item, index) => {
        item.value = item.projectId.toString();
        item.title = item.name;
        item.key = 'key'.concat(index.toString());
        keys.push(item.value);
      });
      self.consoleNodes = arr;
      self.treeNodes = self.getNodes(arr);
      // 初始化加载时默认顶级节点
      let top = self.treeNodes.length > 0 && self.treeNodes[0];
      let node = self.getNode(top);
      if (!this.selectId) {
        if (!this.isEntityStation) {
          if (!this.isExpandedAll) {
            self.$emit('listenChange', top.value, top.title, node);
          } else {
            if (top) {
              this.dealDataProviced(self.treeNodes);
              if (this.isExpandedAll) {
                this.expandedType = true;
                this.expandedKeys = keys;
              }
            }
          }
        } else {
          self.$emit('listenChange', top);
        }
      }
      self.allKeys = keys;
    },
    dealDataProviced (arr) { // 展开所有，会默认选中一个节点，发送选中节点的信息
      let self = this;
      for (var i = 0; i < arr.length; i++) {
        if (arr[i].children && arr[i].children.length > 0) {
          this.dealDataProviced(arr[i].children);
        } else {
          self.$emit('listenChange', arr[i].value, arr[i].title, arr[i]);
          this.selectedKeys.push(arr[i].value);
          return false;
        }
        return false;
      }
    },
    getDepartTree () {
      let self = this;
      const user = Vue.ls.get(USER_INFO);
      let isHasAllData = this.isDeptTree ? deptTree({
        show: this.show
      }) : (this.all || user.hasAllData == '1'
        ? queryDepartTreeList({
          show: this.show
        })
        : queryDepartTreeListByOrgCode({
          orgCode: user.orgCode,
          show: this.show
        }))
         ;
      isHasAllData.then((res) => {
        if (res.success) {
          if (isHasAllData) {
            self.treeNodes = res.result;
          } else {
            let arr = res.result.filter((item) => item.value);
            self.treeNodes = arr;
          }
          self.consoleNodes = self.getAll(self.treeNodes);
          self.allKeys = self.depIds;
        } else {
          self.consoleNodes = this.treeNodes = [];
        }
        if (this.treeNodes.length) {
          // 初始化加载时默认顶级节点
          let top = this.treeNodes[0];
          // this.selectId = top.value
          if (!this.selectId) {
            this.$emit('listenChange', top.value, top.title);
          }
        }
      });
    },
    // 获取部门数据
    getAll (arr) {
      let all = [];
      const self = this;
      const getChilds = function (arr) {
        arr.forEach((item) => {
          if (item.children && item.children.length > 0) {
            let obj = Object.assign({}, item);
            obj.children = [];
            all.push(obj);
            self.depIds.push(item.key);
            getChilds(item.children);
          } else {
            all.push(item);
          }
        });
      };
      getChilds(arr);
      return all;
    },
    // 树形数据转换
    getNodes (data) {
      let result = [];
      if (!Array.isArray(data)) {
        return result;
      }
      //  空对象
      let map = {};
      data.forEach((item) => {
        map[item.value.toString()] = item;
      });
      data.forEach((item) => {
        let pid = '';
        if (!this.isEntityStation) {
          pid = this.isproject ? item.pId : item.parentId;
        } else {
          pid = item.pid;
        }

        let parent = map[pid.toString()];
        if (parent) {
          ;
          (parent.children || (parent.children = [])).push(item);
        } else {
          result.push(item);
        }
      });
      return result;
    },
    // 搜索框
    onChange (e) {
      const value = e.target.value;
      if (value) {
        let nodes = this.consoleNodes.filter((item) => item.title.indexOf(value) != -1);
        nodes.forEach((item) => {
          delete item.children;
        });
        this.treeNodes = this.getNodes(nodes);
      } else {
        this.consoleNodes.forEach((item) => {
          delete item.children;
        });
        this.treeNodes = this.getNodes(this.consoleNodes);
      }
    },
    expand (expandedKeys, info) {
      this.expandedKeys = expandedKeys;
    },
    // 获取项目节点信息
    getNode (data) {
      if (!data) {
        return {};
      }
      const node = {
        name: data.hasOwnProperty('name') ? data.name : undefined,
        isPjPsa: data.hasOwnProperty('isPjPsa') ? data.isPjPsa : undefined,
        psaId: data.hasOwnProperty('psaId') ? data.psaId : undefined,
        projectId: data.hasOwnProperty('projectId') ? data.projectId : undefined
      };
      return node;
    }
  }
};
</script>

<style lang="less" scoped>
  @width: 12px;

  .com-project-tree {
    width: calc(100% + 12px);
    padding: 10px;
    height: 100%;
    border-radius: @border-radius-base;

    .ant-tree {
      max-height: calc(100% - 100px);
      width: 100%;
      overflow: auto;
    }
  }
</style>
