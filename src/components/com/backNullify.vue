<template>
<a-modal ref="activereview" :title="title" v-model="visible" :maskClosable="false" centered @cancel="cancel" width="450px">
  <a-spin :spinning="loading">
    <a-form-model ref="backNullifyForm" :model="form" :rules="rules" :label-col="{span: 0}" :wrapper-col="{span: 24}">
      <a-form-model-item label="" prop="auditOpinion">
        <a-textarea v-model="form.auditOpinion" @blur="form.auditOpinion = $trim($event)" :max-length="100" :auto-size="{ minRows: 4, maxRows: 6}"
          placeholder="请输入" show-word-limit></a-textarea>
      </a-form-model-item>
    </a-form-model>
    </a-spin>
    <div slot="footer">
      <a-button size="default" class="solar-eye-btn-primary" :loading="loading" @click="commit()">确定</a-button>
      <a-button size="default" :disabled="loading" @click="cancel()">取消</a-button>
      </div>
</a-modal>
</template>

<script>
export default {
  name: 'backNullify',
  model: {
    prop: 'visible',
    event: 'change'
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    // 1、退回  2、作废
    type: {
      type: String,
      default: '1',
      required: true
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    visible (val, old) {
      this.form.auditOpinion = '';
    },
    type (val, old) {
      this.title = (val == '1' ? '退回意见' : '作废原因');
    }
  },
  data () {
    return {
      title: '退回意见',
      form: {
        auditOpinion: '' // 审批意见
      },
      rules: {
        auditOpinion: [{
          required: true, message: '请输入'
        }]
      }
    };
  },
  methods: {
    commit () {
      const self = this;
      self.$refs['backNullifyForm'].validate((valid) => {
        if (valid) {
          // 调用父页面的审批方法并回传审批意见和状态
          self.$emit('backNullify', self.form.auditOpinion);
        } else {
          return false;
        }
      });
    },
    // 关闭回调方法
    cancel () {
      this.loading = false;
      this.form.auditOpinion = '';
      this.$refs['backNullifyForm'].clearValidate();
      this.$emit('change', false);
      // this.visible = false;
      // 确保关闭后可以再次打开
      this.$refs.activereview.$destroyAll();
    }
  }
};
</script>
