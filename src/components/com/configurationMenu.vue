<template>
  <div class="bottom-auto-height">
    <a-card :bodyStyle="{ padding: '16px' }" class="top">
      <a-input v-model="routeName" placeholder="请输入关键字" class="search-input" suffix="搜索"></a-input>
    </a-card>

    <a-card class="bottom" style="overflow: auto" :bodyStyle="{ padding: '16px 0' }">
      <div v-if="menus && menus.length > 0">
        <div v-for="(item, index) in menus" :key="item.path" class="solaryeye-menu">
          <div class="menu-title">
            <div class="menu-title-before"></div>
            {{ item.meta.title }}
            <a-icon type="double-right" @click="handleClick(index)" :class="item.isShow ? 'solar-eye-arrow-down' : 'solar-eye-arrow-up'" />
          </div>
          <div class="menu-content">
            <a-button v-for="child in item.children" v-show="item.isShow" :key="child.path" @click="openNewPath(item, child)"
              :class="isIncludeRouteName(child.meta.title, index)">{{child.meta.title}}</a-button>
          </div>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script>
export default {
  name: 'configurationMenu',
  props: {
    routePath: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      menus: [],
      routeName: ''
    };
  },
  created () {
    this.secondMenu();
  },
  watch: {},
  computed: {
    permissionMenuList () {
      return this.$store.state.user.permissionList;
    }
  },

  methods: {
    secondMenu () {
      this.permissionMenuList.forEach((item) => {
        if (item.path === '/configuration') {
          this.seleteMenu(item.children);
        }
      });
    },
    seleteMenu (data) {
      data.forEach((item) => {
        if (item.path === this.routePath) {
          this.menus = item.children;
          this.menus = this.menus.filter((child) => {
            return child.path != this.routePath;
          });
          this.fillMenuList();
        }
      });
    },
    handleClick (index) {
      this.menus[index].isShow = !this.menus[index].isShow;
    },
    fillMenuList () {
      this.menus.map((item, index) => {
        this.$set(this.menus[index], 'isShow', true);
      });
    },
    isIncludeRouteName (name, index) {
      let isIndexOf = this.routeName && name.indexOf(this.routeName) != -1;
      if (isIndexOf) {
        this.menus[index].isShow = true;
      }
      return isIndexOf ? 'solar-eye-btn-primary' : 'solar-eye-btn-grey';
    },
    /**
       *  打开新的页面
       * params {item}
       * params child
       */
    openNewPath (item, child) {
      let isIframe = child.meta.componentName === 'IframePageView' || child.meta.componentName === 'IframeView';
      let path = '';
      if (isIframe) {
        path = item.path + '/' + child.path;
      } else {
        path = child.path;
      }
      this.$router.push(path);
    }
  }
};
</script>

<style lang="less" scoped>
  .search-input {
    width: 260px;
  }

  .colorize_bg(@color: @white, @alpha: 1) {
    background: hsla(hue(@color), saturation(@color), lightness(@color), @alpha);
  }

  .solaryeye-menu {
    &:last-child {
      border-bottom: transparent;

      .menu-content {
        border-bottom: transparent;
      }
    }

    .menu-title {
      padding: 0 0 16px;
      line-height: 22px;
      font-weight: 500;
      font-size: 16px;
      display: flex;
      align-items: center;

      i {
        margin: 0px 16px;
      }

      .solar-eye-arrow-down {
        transform: rotate(90deg);
      }

      .solar-eye-arrow-up {
        transform: rotate(-90deg);
      }
    }

    .menu-title-before {
      margin-right: 16px;
      height: 16px;
      border-radius:  0px 2px 2px 0px;;
    }

    .menu-content {
      padding: 0 24px 24px 12px;
    }

    &:not(:first-child) {
      .menu-title {
        padding-top: 16px;
      }
    }

    :deep(.ant-btn) {
      margin-left: 0 !important;
      margin-right: 16px !important;
    }
  }
</style>
