<template>
  <div class="com-project-tree">
    <a-spin :spinning="spinning">
      <a-row :gutter="24" class="document-vue-content">
        <a-col class="left-tree solar-eye-pure-bg">
          <div class="document-title">
            <span style="font-weight: bold">物料分类</span>
            <span class="expanded-text" @click="expandedTree"
              >{{ expandedKeys.length === 0 || !expandedKeys.includes('0') ? "展开" : "收起" }}全部</span
            >
          </div>
          <div class="tree-info">
            <div class="search-info">
              <a-input-search
                @search="searchInfo"
                class="search-input"
                v-model="typeKeyWords"
                allowClear
                placeholder="请输入关键词搜索"
              />
              <!-- <a-icon v-has="'materialType:add'" class="add-tree" type="plus-circle" @click="addClick" /> -->
            </div>
            <a-col
              :md="10"
              :sm="24"
              v-bind:style="{
                height: boxHeight + 'px',
                overflow: 'auto',
                width: '100%',
                paddingLeft: '15px',
              }"
            >
              <template>
                <a-tree
                  v-model="selectId"
                  :selected-keys="selectedKeys"
                  :expandedKeys.sync="expandedKeys"
                  @expand="onExpand"
                  :tree-data="treeNodes"
                  @select="onSelect"
                  :expanded-keys="expandedKeys"
                  :auto-expand-parent="autoExpandParent"
                  :replace-fields="{
                    children: 'children',
                    title: 'materialTypeName',
                    key: 'materialTypeCode',
                  }"
                >
                  <template slot="title" slot-scope="item">
                    <div
                      class="device-operation"
                      @mouseover="hoverId = item.materialTypeCode"
                      @mouseleave="hoverId = ''"
                    >
                      <div class="name-info">
                        <svg-icon v-if="item.status === '0'" iconClass="forbidden"></svg-icon>
                        <a-tooltip placement="top">
                          <template #title>{{ item.materialTypeName }}</template>
                          <div class="operation-name">{{ item.materialTypeName }}</div>
                        </a-tooltip>
                      </div>
                      <div
                        class="operation-btns"
                        v-if="hoverId === item.materialTypeCode && hoverId !== '0'"
                      >
                        <a-icon v-has="'materialType:edit'" type="edit" @click.stop="detailClick(item)" />
                        <a-icon v-has="'materialType:delete'" type="delete" @click.stop="deleteClick(item.id)" />
                      </div>
                      <div v-else style="width: 53px"></div>
                    </div>
                  </template>
                </a-tree>
              </template>
            </a-col>
          </div>
        </a-col>
      </a-row>
    </a-spin>
    <!-- 新增、编辑物料分类 -->
    <a-modal
      v-model="detailOpen"
      :maskClosable="false"
      centered
      @cancel="cancel"
      :title="detailTitle"
      width="30%"
    >
      <a-spin :spinning="saveLoad">
        <a-form-model
          :model="materialType"
          :rules="rules"
          ref="materialType"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 18 }"
        >
          <a-row :gutter="24">
            <a-col :span="22">
              <a-form-model-item label="分类名称" prop="materialTypeName">
                <a-input
                  size="default"
                  v-model.trim="materialType.materialTypeName"
                  :max-length="15"
                  style="width: 100%"
                ></a-input>
              </a-form-model-item>
            </a-col>
            <a-col :sm="24" :md="4">
              <div style="width: 100%; height: 1px"></div>
            </a-col>
            <a-col :span="22">
              <a-form-model-item label="所属上级:" prop="pid">
                <a-cascader
                  :show-search="{ filter }"
                  :allowClear="false"
                  v-model="materialType.pid"
                  :options="materialTypeParent"
                  change-on-select
                  size="default"
                  :field-names="{
                    label: 'materialTypeName',
                    value: 'materialTypeCode',
                    children: 'children',
                  }"
                  placeholder="请选择所属上级"
                  style="width: 100%"
                ></a-cascader>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="22">
              <a-form-model-item label="状态" prop="status">
                <!-- 禁用 '0'  正常  '1' -->
                <a-radio-group v-model="materialType.status" name="radioGroup">
                  <a-radio :value="'1'">正常</a-radio>
                  <a-radio :value="'0'">禁用</a-radio>
                </a-radio-group>
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </a-spin>
      <template slot="footer">
        <div class="modal-footer">
          <a-button size="default" :disabled="saveLoad" @click="resetForm(true)">取消</a-button>
          <a-button size="default" :loading="saveLoad" type="primary" @click="submitForm()"
            >确定</a-button
          >
        </div>
      </template>
    </a-modal>
  </div>
</template>

<script>
import {
  getInventoryTreeList,
  insertMatType,
  getMatTypeInfo,
  logicDelMatType,
  updateMatType
} from '@/api/materialsManage/inventory';
import debounce from 'lodash/debounce';
export default {
  name: 'materialsInventoryTree',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: [String, Number],
      required: false
    },
    isSelect: {
      type: Boolean,
      default: true
    },
    belongTo: {
      type: String,
      default: 'material'
    },
    faHeight: {
      type: Number,
      default: 0
    }
  },
  data () {
    return {
      detailOpen: false, // 新增、编辑物料分类弹窗
      materialTypeParent: [], // 所属上级
      detailTitle: '',
      materialType: {
        id: '',
        materialTypeName: '',
        parentCode: '',
        pid: [],
        status: '1'
      },
      saveLoad: false, // 新增、编辑物料分类loading
      rules: {
        materialTypeName: [
          {
            required: true,
            message: '请输入分类名称',
            trigger: 'blur'
          }
        ],
        status: [
          {
            required: true,
            message: '请选择状态',
            trigger: 'change'
          }
        ],
        pid: [
          {
            required: true,
            message: '请选择所属上级',
            trigger: 'change'
          }
        ]
      },
      typeKeyWords: '',
      addOrEdit: '',
      hoverId: '',
      autoExpandParent: true,
      allKeys: [],
      treeNodes: [],
      selectedKeys: ['0'],
      expandedKeys: [],
      selectId: '0',
      expandedType: false,
      height: document.body.clientHeight,
      spinning: false
    };
  },
  created () {},
  watch: {
    value (val, old) {
      this.init(val);
    },
    treeType (val, old) {
      this.getDepartTree();
    }
  },
  mounted () {
    this.init(this.value || '0');
    this.getDepartTree();
    this.boxHeight = window.innerHeight - 240;
    window.addEventListener('resize', this.heightResize, true);
  },
  deactivated () {
    window.removeEventListener('resize', this.heightResize, true);
  },
  beforeDestroy () {
    window.removeEventListener('resize', this.heightResize, true);
  },
  methods: {
    // 搜索后默认选中全部
    searchInfo () {
      this.expandedKeys = [];
      this.selectedKeys = ['0'];
      this.getDepartTree();
      this.onSelect(['0'], {}, true);
    },
    // 删除
    deleteClick (id) {
      let self = this;
      this.$confirm({
        title: '是否确认删除物料分类?',
        okText: '确定',
        cancelText: '取消',
        onOk () {
          self.saveLoad = true;
          logicDelMatType({ id }).then((res) => {
            self.saveLoad = false;
            if (res.result_code == '1') {
              // 删除成功默认全部
              self.onSelect(['0'], {}, true);
              self.$message.success('删除成功');
              self.getDepartTree();
            } else {
              self.$message.warning(res.result_msg);
            }
          });
        }
      });
    },
    // 保存数据
    save () {
      let portName = this.addOrEdit === 'add' ? insertMatType : updateMatType;
      // 新增
      this.materialType.parentCode = this.materialType.pid[this.materialType.pid.length - 1];
      let params = Object.assign({}, this.materialType);
      if (params.parentCode === '0') {
        params.parentCode = '';
      }
      delete params.pid;
      portName(params)
        .then((res) => {
          this.saveLoad = false;
          if (res.result_code == '1') {
            this.getDepartTree();
            this.$message.success('操作成功');
            // 编辑查询右边列表 触发更新
            if (this.addOrEdit === 'edit') {
              this.onSelect([this.selectId], '', true);
            }
            this.resetForm(false);
          } else {
            this.$message.warning(res.result_msg);
          }
        })
        .catch(() => {
          this.saveLoad = false;
        });
    },
    // 保存验证
    submitForm () {
      this.saveLoad = true;
      this.$refs['materialType'].validate((valid) => {
        if (valid) {
          this.save();
        } else {
          this.saveLoad = false;
          return false;
        }
      });
    },
    // 获取所属上级
    initParentMaterialTypeInfoList (type) {
      getInventoryTreeList({ belongTo: this.belongTo, status: '1', typeKeyWords: '' }).then((res) => {
        if (res.result_code == '1') {
          // 添加全部
          let treeNodes = [
            {
              materialTypeName: '全部',
              materialTypeCode: '0',
              children: res.result_data
            }
          ];
          this.materialTypeParent = treeNodes;
          if (type === 'edit') {
            let data = this.materialType.parentCodes.split(',');
            data = ['0'].concat(data);
            data.pop();
            this.$set(this.materialType, 'pid', data);
          }
        }
      });
    },
    // 关闭弹窗
    cancel () {
      this.resetForm(true);
    },
    // 新增
    addClick () {
      this.resetForm(true);
      this.addOrEdit = 'add';
      this.initParentMaterialTypeInfoList('add');
      this.detailTitle = '新增物料分类';
      this.detailOpen = true;
    },
    // 取消
    resetForm (flag) {
      if (this.$refs['materialType']) {
        this.$refs['materialType'].resetFields();
      }
      this.addOrEdit = '';
      this.materialType = {
        id: '',
        materialTypeName: '',
        parentCode: '',
        pid: [],
        status: '1'
      };
      this.saveLoad = false;
      this.detailOpen = false;
      if (!flag) {
        this.getDepartTree();
      }
    },
    // 查询设备类型详情
    detailClick (item) {
      let params = {
        id: item.id,
        belongTo: this.belongTo
      };
      getMatTypeInfo(params)
        .then((res) => {
          if (res.result_code == '1') {
            let data = res.result_data;
            this.materialType = {
              id: data.id,
              materialTypeName: data.materialTypeName,
              materialTypeCode: data.materialTypeCode,
              parentCodes: data.parentCodes,
              status: data.status
            };
            this.addOrEdit = 'edit';
            this.initParentMaterialTypeInfoList('edit');
            this.detailTitle = '编辑物料分类';
            this.detailOpen = true;
          } else {
            this.$message.error(res.result_msg);
          }
        })
        .catch(() => {});
    },
    onExpand (expandedKeys) {
      this.expandedKeys = expandedKeys;
      this.autoExpandParent = false;
    },
    expand (expandedKeys, info) {
      this.expandedKeys = expandedKeys;
    },
    // 展开/收起树
    expandedTree (data) {
      let keys = this.expandedKeys.length === 0 || data === 'expanded' ? this.allKeys : [];
      this.expand(keys, {});
    },
    // 遍历树形数据，设置每一项的expanded属性，实现展开收起
    getAllKeys (data) {
      for (let i = 0; i < data.length; i++) {
        this.allKeys.push(data[i].materialTypeCode);
        if (data[i].children && data[i].children.length) {
          data[i].children.forEach((list) => {
            this.getAllKeys([list]);
          });
        }
      }
    },

    heightResize: debounce(function () {
      this.height = window.innerHeight;
    }, 400),

    init (val) {
      try {
        val = val.toString();
      } catch (e) {
        val = '';
      }
      this.selectId = val;
      if (val || val == '') {
        this.selectedKeys = [val];
        if (val.toString()) {
          this.expandedKeys.push(val);
        }
      } else {
        this.selectedKeys = [];
        this.expandedKeys = [];
      }
    },
    // 查询数据
    getDepartTree () {
      let self = this;
      getInventoryTreeList({ belongTo: self.belongTo, status: '', typeKeyWords: this.typeKeyWords }).then(
        (res) => {
          let treeNodes = [
            {
              materialTypeName: '全部',
              materialTypeCode: '0',
              id: '0',
              children: res.result_data
            }
          ];
          self.treeNodes = treeNodes;
          this.getAllKeys(this.treeNodes);
          if (self.treeNodes.length) {
            // 初始化加载时默认顶级节点
            let top = self.treeNodes[0];
            self.selectId = top.materialTypeCode;
            self.selectedKeys.push(top.materialTypeCode);
            self.expandedKeys.push(top.materialTypeCode);
            if (self.isSelect && !self.selectId) {
              self.$emit('listenChange', top.materialTypeCode, top.materialTypeName);
            }
          }
          // 搜索 展开全部
          if (this.typeKeyWords) {
            this.expandedTree('expanded');
          }
        }
      );
    },
    // 点击查询
    onSelect (selectedKeys, info, isSearch) {
      if (!isSearch) {
        let dataRef = info.node.dataRef;
        if (dataRef.materialTypeCode == '') {
          this.expandedKeys = [''];
        }
      }
      // 取消偶次点击取消选中的问题
      if (selectedKeys && selectedKeys.length > 0) {
        this.selectedKeys = selectedKeys;
        this.$emit('change', selectedKeys[0].toString());
        this.$emit('listenChange', selectedKeys[0].toString());
      }
    }
  }
};
</script>

<style lang="less" scoped>
.com-project-tree {
  height: 100%;
  border-radius: @border-radius-base;

  .com-title {
    width: 100%;
    font-size: 18px;
    line-height: 19px;
    margin: 24px 0;
    display: flex;
    justify-content: space-between;
  }
  .com-title-blue {
    font-size: 14px;
    line-height: 19px;
    color: #ff8f33;
    cursor: pointer;
  }

  .ant-tree {
    width: 280px;
  }

  :deep(.ant-tree-switcher:hover) {
    .anticon.anticon-caret-down.ant-tree-switcher-icon {
      color: #ff8f33 !important;
    }
  }
}
.solar-eye-dark {
  .com-title {
    color: #d0d2d5 !important;
  }
  .com-title-blue {
    color: #409eff !important;
  }
}
.document-title {
  display: flex;
  justify-content: space-between;
  padding: 15px 20px;
}
.expanded-text {
  cursor: pointer;
}
.tree-info {
  .search-info {
    margin: 10px 20px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .search-input {
      display: flex;
      flex: 1;
      border-radius: 50px;
    }
    .add-tree {
      width: 24px;
      text-align: right;
      color: #ff8f33;
      font-size: 16px;
      cursor: pointer;
    }
  }
  .device-operation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .name-info {
      display: flex;
      align-items: center;
    }
    .operation-name {
      margin-left: 5px;
      width: 150px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    .operation-btns {
      margin-right: 15px;
      & > i:nth-child(2) {
        display: inline-block;
        margin-left: 10px;
      }
    }
  }
}
.com-project-tree {
  .document-vue-content {
    display: flex;
  }
  .right-content {
    margin-left: 10px;
    flex: 1;
  }
}
.left-tree {
  width: 100%;
}

</style>
