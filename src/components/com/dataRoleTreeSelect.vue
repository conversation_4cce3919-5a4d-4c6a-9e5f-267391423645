<!-- 数据角色-全量加载，不考虑懒加载和用户权限 -->
<template>
  <div class="data-role-tree-select">
    <a-tree-select
      v-model="selectKeys"
      show-search
      :multiple="false"
      :maxTagCount="1"
      :treeCheckable="false"
      :labelInValue="false"
      :treeCheckStrictly="true"
      style="width: 100%"
      :disabled="disabled"
      placeholder="请选择数据角色"
      :tree-data="nodes"
      @change="onChange"
      :allowClear="allowClear"
      treeNodeFilterProp="title"
      :treeExpandedKeys.sync="treeExpandedKeys"
      dropdownClassName="data-role-tree-select-dropdown"
      :replace-fields="{title: 'name', key:'id', value: 'id'}"
      :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
      @search="search"
      @click="handleVisible($event)">
    </a-tree-select>
  </div>
</template>

<script>
import { getDataRoleTree, getDataRoleTreeBy } from '@/api/api';
export default {
  name: 'dataRoleTreeSelect',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    allowClear: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    getValue (val) {
      if (JSON.stringify(val) != JSON.stringify(this.selectKeys)) {
        this.selectKeys = val || null;
        this.treeExpandedKeys = val ? [val] : [];
      }
    }
  },
  computed: {
    getValue () {
      return this.value;
    }
  },
  data () {
    return {
      all: [],
      nodes: [],
      searchValue: '',
      selectKeys: null,
      treeExpandedKeys: [],
      times: null
    };
  },
  created () {
    this.getNodes();
  },
  methods: {
    /*
        节点change事件
      */
    onChange (value, label) {
      this.$emit('change', this.selectKeys);
    },
    /*
        数据角色-树查询
      */
    getNodes () {
      let { searchValue } = this;
      if (searchValue) {
        getDataRoleTreeBy({ 'keyWord': searchValue }).then(res => {
          this.nodes = res.result;
        }).catch(() => {
          this.nodes = [];
        });
      } else {
        getDataRoleTree().then(res => {
          let result = Array.isArray(res.result) ? res.result : [res.result];
          this.getAllNodes(result);
          this.nodes = result;
        }).catch(() => {
          this.nodes = [];
        });
      }
    },
    search (val) {
      let _this = this;
      let find = () => {
        _this.treeExpandedKeys = _this.all.map(node => {
          return node.id;
        });
        _this.$forceUpdate();
      };
      this.debounce(find);
    },
    /*
        搜索展开节点
      */
    // setExpandedKeys () {
    //   this.treeExpandedKeys = this.nodes.map(item => { return item.key; });
    // },
    /*
        节点平铺
      */
    getAllNodes (treeData) {
      let nodes = [];
      let fn = (items) => {
        items.forEach(item => {
          let o = JSON.parse(JSON.stringify(item));
          o.children = undefined;
          nodes.push(o);
          if (Array.isArray(item.children)) {
            fn(item.children);
          }
        });
      };
      fn(treeData);
      this.all = Object.freeze(nodes);
    },
    /*
        节点点击事件
      */
    handleVisible () {
      this.setExpandedKeys();
      this.$nextTick(() => {
        let box = document.querySelector('.ant-select-tree li .ant-select-tree-node-content-wrapper.ant-select-tree-node-selected');
        let dropdown = document.querySelector('.data-role-tree-select-dropdown');
        if (box && dropdown && !dropdown.display) {
          box.scrollIntoView(false);
        }
      });
    },
    /*
        设置展开节点
      */
    setExpandedKeys () {
      let expandedKeys = [];
      let _this = this;
      let { selectKeys, all } = this;
      let find = (value) => {
        let node = all.find(o => o.id == value);
        if (node) {
          expandedKeys.push(node.id);
          node.parentId && find(node.parentId);
        }
      };
      find(selectKeys);
      _this.treeExpandedKeys = expandedKeys;
      _this.$forceUpdate();
    },
    /*
        防抖
      */
    debounce (fn, wait = 500) {
      if (this.times) {
        clearTimeout(this.times);
        this.times = null;
      }
      this.times = setTimeout(fn, wait);
    }
  },
  beforeDestroy () {
    this.all = this.nodes = this.treeExpandedKeys = [];
    this.times && clearTimeout(this.times);
    this.times = null;
  }
};
</script>

<style lang="less" scoped>
  .data-role-tree-select{
    width: 100%;
  }
</style>
