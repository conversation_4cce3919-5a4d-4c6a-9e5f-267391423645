<template>
<div>
     <a-input-search style="margin: 8px; width:90%" v-model="searchValue" @search="onSearchEvent" v-if="showSearch" allowClear />
      <div class="ztree" :id="ztreeId" style="overflow:auto" :style="{height: height}" v-show="hiddenNodes.length < nodes.length"></div>
        <div v-show="hiddenNodes.length == nodes.length" :style="{height: height}" class="ztree-no-data">
            <svg-icon iconClass="no_data" style="font-size:64px"></svg-icon>
            <span>暂无数据</span>
          </div>
</div>

</template>

<script>

require('@ztree/ztree_v3/js/jquery.ztree.all');
require('@ztree/ztree_v3/js/jquery.ztree.exhide.min');
export default {
  props: {
    setting: {
      type: Object,
      require: false,
      default: function () {
        return {};
      }
    },
    nodes: {
      type: Array,
      require: true,
      default: function () {
        return [];
      }
    },
    showSearch: {
      type: [String, Boolean],
      default: false
    },
    height: {
      default: '300px',
      type: String
    }
  },
  data () {
    return {
      ztreeId: 'ztree_' + parseInt(Math.random() * 1e10),
      ztreeObj: null,
      list: [],
      searchValue: '',
      hiddenNodes: [],
      ztreeSetting: {
        view: {
          showIcon: false // default to hide icon
        },
        callback: {
          onAsyncError: (...arg) => {
            this.$emit('onAsyncError', ...arg);
          },
          onAsyncSuccess: (...arg) => {
            this.$emit('onAsyncSuccess', ...arg);
          },
          onCheck: (...arg) => {
            this.$emit('onCheck', ...arg);
          },
          beforeClick: (...arg) => {
            this.$emit('beforeClick', ...arg);
          },
          beforeCheck: (...arg) => {
            this.$emit('beforeCheck', ...arg);
          },
          onClick: (...arg) => {
            this.$emit('onClick', ...arg);
          },
          onCollapse: (...arg) => {
            this.$emit('onCollapse', ...arg);
          },
          onDblClick: (...arg) => {
            this.$emit('onDblClick', ...arg);
          },
          onDrag: (...arg) => {
            this.$emit('onDrag', ...arg);
          },
          onDragMove: (...arg) => {
            this.$emit('onDragMove', ...arg);
          },
          onDrop: (...arg) => {
            this.$emit('onDrop', ...arg);
          },
          onExpand: (...arg) => {
            this.$emit('onExpand', ...arg);
          },
          onMouseDown: (...arg) => {
            this.$emit('onMouseDown', ...arg);
          },
          onMouseUp: (...arg) => {
            this.$emit('onMouseUp', ...arg);
          },
          onRemove: (...arg) => {
            this.$emit('onRemove', ...arg);
          },
          onRename: (...arg) => {
            this.$emit('onRename', ...arg);
          },
          onRightClick: (...arg) => {
            this.$emit('onRightClick', ...arg);
          }
        }
      }
    };
  },
  watch: {
    nodes: {
      handler: function (nodes) {
        this.list = nodes;

        // update tree
        if (this.ztreeObj) {
          this.searchValue = '';
          this.ztreeObj.destroy();
        }
        this.$nextTick(() => {
          this.ztreeObj = $.fn.zTree.init(
            $('#' + this.ztreeId),
            Object.assign({}, this.ztreeSetting, this.setting),
            this.list
          );
          let nodesData = this.ztreeObj.getNodes();
          if (nodesData[0] && nodesData[0].children && nodesData[0].children.length > 0) {
            nodesData[0].children = this.arrUnique(nodesData[0].children, 'id');
            this.ztreeObj.updateNode(nodesData[0]);
          }
          this.$emit('onCreated', this.ztreeObj);
        });
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    onSearchEvent () {
      let that = this;
      var ztreeObj = $.fn.zTree.getZTreeObj(that.ztreeId);
      // 显示上次搜索后隐藏的结点
      ztreeObj.showNodes(that.hiddenNodes);
      // 查找不符合条件的结点
      // 返回true表示要过滤，需要隐藏，返回false表示不需要过滤，不需要隐藏
      function filterFunc (node) {
        // 如果当前结点，或者其父结点可以找到，或者当前结点的子结点可以找到，则该结点不隐藏
        if (that.searchParent(that.searchValue, node) || that.searchChildren(that.searchValue, node.children)) {
          return false;
        }
        return true;
      };

      // 获取不符合条件的叶子结点
      that.hiddenNodes = ztreeObj.getNodesByFilter(filterFunc);
      // 隐藏不符合条件的叶子结点
      ztreeObj.hideNodes(that.hiddenNodes);
      if (that.searchValue) {
        ztreeObj.expandAll(true);
        this.$emit('onCheck');
      } else {
        ztreeObj.expandAll(false);
        this.$emit('onCheck');
      }
    },
    searchParent (searchValue, node) {
      if (node == null) {
        return false;
      }
      if (node.name.indexOf(searchValue) != -1) {
        return true;
      }
      // 递归查找父结点
      return this.searchParent(searchValue, node.getParentNode());
    },
    searchChildren (searchValue, children) {
      if (children == null || children.length == 0) {
        return false;
      }
      for (var i = 0; i < children.length; i++) {
        var node = children[i];
        if (node.name.indexOf(searchValue) != -1) {
          return true;
        }
        // 递归查找子结点
        var result = this.searchChildren(searchValue, node.children);
        if (result) {
          return true;
        }
      }
      return false;
    },
    // 对象数组去重
    arrUnique (arr, key) {
      let returnArr = [];
      let obj = {};
      returnArr = arr && arr.length > 0 && arr.reduce((cur, next) => {
        // eslint-disable-next-line no-unused-expressions
        obj[next[key]] ? '' : obj[next[key]] = true && cur.push(next);
        return cur;
      }, []);
      return returnArr;
    }

  }
};
</script>

<style scoped>
/* beauty ztree! */
.ztree-no-data {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}
.ztree {
  text-align: left;
  font-size: 14px;
}
.ztree :deep(li) {
  list-style-type: none;
  white-space: nowrap;
  outline: none;
}
.ztree :deep(li ul) {
  position: relative;
  padding: 0 0 0 20px;
  margin: 0;
}
.ztree :deep(.line:before) {
  position: absolute;
  top: 0;
  left: 10px;
  height: 100%;
  content: "";
  border-right: 1px dotted #dbdbdb;
}
.ztree :deep(.roots_docu:before),
.ztree :deep(.roots_docu:after),
.ztree :deep(.center_docu:before),
.ztree :deep(.bottom_docu:before),
.ztree :deep(.center_docu:after),
.ztree :deep(.bottom_docu:after) {
  position: absolute;
  content: "";
  border: 0 dotted #dbdbdb;
}
.ztree :deep(.roots_docu:before) {
  left: 10px;
  height: 50%;
  top: 50%;
  border-left-width: 1px;
}
.ztree :deep(.roots_docu:after) {
  top: 50%;
  left: 11px;
  width: 50%;
  border-top-width: 1px;
}
.ztree :deep(.center_docu:before) {
  left: 10px;
  height: 100%;
  border-left-width: 1px;
}
.ztree :deep(.center_docu:after) {
  top: 50%;
  left: 11px;
  width: 50%;
  border-top-width: 1px;
}
.ztree :deep(.bottom_docu:before) {
  left: 10px;
  height: 50%;
  border-left-width: 1px;
}
.ztree :deep(.bottom_docu:after) {
  top: 50%;
  left: 11px;
  width: 50%;
  border-top-width: 1px;
}
.ztree :deep(li a) {
  display: inline-block;
  line-height: 22px;
  height: 22px;
  margin: 0;
  cursor: pointer;
  transition: none;
  vertical-align: middle;

}
.ztree :deep(.node_name) {
  display: inline-block;
  padding: 0 3px;
  border-radius: 4px;
  white-space: nowrap;
  max-width: 220px;
  min-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
}
.ztree :deep(.curSelectedNode .node_name) {
  color: #000;
  background-color: #c9e9f7;
}
.ztree :deep(.curSelectedNode_Edit) {
  height: 20px;
  opacity: 0.8;
  color: #000;
  border: 1px #6cc2e8 solid;
  background-color: #9dd6f0;
}
.ztree :deep(.tmpTargetNode_inner) {
  opacity: 0.8;
  color: #fff;
  background-color: #4fcbf0;
  filter: alpha(opacity=80);
}
.ztree :deep(.rename) {
  font-size: 12px;
  line-height: 22px;
  width: 80px;
  height: 22px;
  margin: 0;
  padding: 0;
  vertical-align: top;
  border: 0;
  background: none;
}
.ztree :deep(.button) {
  position: relative;
  display: inline-block;
  line-height: 22px;
  height: 22px;
  width: 22px;
  cursor: pointer;
  text-align: center;
  vertical-align: middle;
}

.ztree :deep(.button.edit) {
  color: #25ae88;
}
.ztree :deep(.button.remove) {
  color: #cb4042;
}
.ztree :deep(.button.chk) {
  position: relative;
  width: 14px;
  height: 14px;
  margin: 0 4px 0 0;
  border: 1px solid #d7dde4;
  border-radius: 2px;
  background: #fff;
}
.ztree :deep(.chk.radio_true_full),
.ztree :deep( .chk.radio_false_full),
.ztree :deep( .chk.radio_true_full_focus),
.ztree :deep( .chk.radio_false_full_focus),
.ztree :deep( .chk.radio_false_disable),
.ztree :deep( .chk.radio_true_disable),
.ztree :deep( .chk.radio_true_part),
.ztree :deep( .chk.radio_false_part),
.ztree :deep( .chk.radio_true_part_focus),
.ztree :deep( .chk.radio_false_part_focus) {
  border-radius: 8px;
}
.ztree :deep( .button.chk:after) {
  position: absolute;
  top: 1px;
  left: 4px;
  width: 4px;
  height: 8px;
  content: "";
  transition: -webkit-transform 0.2s ease-in-out;
  transition: transform 0.2s ease-in-out;
  transition: transform 0.2s ease-in-out, -webkit-transform 0.2s ease-in-out;
  -webkit-transform: rotate(0deg) scale(0);
  transform: rotate(0deg) scale(0);
  border-right: 2px solid #fff;
  border-bottom: 2px solid #fff;
}
.ztree :deep(.button.checkbox_false_full_focus) {
  border-color: #ccc;
}
.ztree :deep( .button.checkbox_true_full),
.ztree :deep( .button.checkbox_true_full_focus),
.ztree :deep( .button.checkbox_true_part),
.ztree :deep( .button.checkbox_true_part_focus),
.ztree :deep( .button.checkbox_true_disable) {
  border-color: #FF8F33;
  background-color: #FF8F33;
}
.ztree :deep( .button.checkbox_true_full:after),
.ztree :deep( .button.checkbox_true_full_focus:after),
.ztree :deep( .button.checkbox_true_disable:after) {
  -webkit-transform: rotate(45deg) scale(1);
  transform: rotate(45deg) scale(1);
}
.ztree :deep( .button.checkbox_true_part:after),
.ztree :deep( .button.checkbox_true_part_focus:after) {
  top: 5px;
  left: 1px;
  width: 10px;
  height: 1px;
  -webkit-transform: rotate(0deg) scale(1);
  transform: rotate(0deg) scale(1);
  border-right: 0;
}
.ztree :deep( .button.radio_true_full),
.ztree :deep( .chk.radio_true_full_focus),
.ztree :deep( .chk.radio_true_part),
.ztree :deep( .chk.radio_true_part_focus) {
  border-color: #FF8F33;
}
.ztree :deep( .button.radio_true_full:after),
.ztree :deep( .chk.radio_true_full_focus:after),
.ztree :deep( .chk.radio_true_part:after),
.ztree :deep( .chk.radio_true_part_focus:after) {
  top: 3px;
  left: 3px;
  width: 8px;
  -webkit-transform: rotate(0deg) scale(1);
  transform: rotate(0deg) scale(1);
  border: 0;
  border-radius: 4px;
  background: #FF8F33;
}
.ztree :deep(.button.checkbox_true_disable),
.ztree :deep(.button.checkbox_false_disable),
.ztree :deep( .chk.radio_false_disable),
.ztree :deep( .chk.radio_true_disable) {
  cursor: not-allowed;
}
.ztree :deep( .button.checkbox_false_disable) {
  background-color: #f3f3f3;
}
.ztree :deep( .button.noline_close:before),
.ztree :deep( .button.noline_open:before),
.ztree :deep( .button.root_open:before),
.ztree :deep( .button.root_close:before),
.ztree :deep( .button.roots_open:before),
.ztree :deep( .button.roots_close:before),
.ztree :deep( .button.bottom_open:before),
.ztree :deep( .button.bottom_close:before),
.ztree :deep( .button.center_open:before),
.ztree :deep( .button.center_close:before) {
  position: absolute;
  top: 4px;
  left: 4px;
  content: "";
  transition: -webkit-transform ease 0.3s;
  transition: transform ease 0.3s;
  transition: transform ease 0.3s, -webkit-transform ease 0.3s;
  -webkit-transform: rotateZ(0deg);
  transform: rotateZ(0deg);
  -webkit-transform-origin: 25% 50%;
  transform-origin: 25% 50%;
  border: 4px solid;
  border-color: transparent transparent transparent #d6d6d6;
}
.ztree :deep( .button.noline_open:before),
.ztree :deep( .button.root_open:before),
.ztree :deep( .button.roots_open:before),
.ztree :deep( .button.bottom_open:before),
.ztree :deep( .button.center_open:before) {
  -webkit-transform: rotateZ(90deg);
  transform: rotateZ(90deg);
}
.ztree :deep( .button.ico_loading) {
  margin-right: 2px;
  background: url("data:image/gif;base64,R0lGODlhEAAQAKIGAMLY8YSx5HOm4Mjc88/g9Ofw+v///wAAACH/C05FVFNDQVBFMi4wAwEAAAAh+QQFCgAGACwAAAAAEAAQAAADMGi6RbUwGjKIXCAA016PgRBElAVlG/RdLOO0X9nK61W39qvqiwz5Ls/rRqrggsdkAgAh+QQFCgAGACwCAAAABwAFAAADD2hqELAmiFBIYY4MAutdCQAh+QQFCgAGACwGAAAABwAFAAADD1hU1kaDOKMYCGAGEeYFCQAh+QQFCgAGACwKAAIABQAHAAADEFhUZjSkKdZqBQG0IELDQAIAIfkEBQoABgAsCgAGAAUABwAAAxBoVlRKgyjmlAIBqCDCzUoCACH5BAUKAAYALAYACgAHAAUAAAMPaGpFtYYMAgJgLogA610JACH5BAUKAAYALAIACgAHAAUAAAMPCAHWFiI4o1ghZZJB5i0JACH5BAUKAAYALAAABgAFAAcAAAMQCAFmIaEp1motpDQySMNFAgA7")
    0 center no-repeat;
}
.ztree :deep( .tmpTargetzTree) {
  opacity: 0.8;
  background-color: #2ea9df;
  filter: alpha(opacity=80);
}
.ztree :deep( .tmpzTreeMove_arrow) {
  position: absolute;
  width: 18px;
  height: 18px;
  color: #4fcbf0;
}
</style>
<style>
ul.ztree.zTreeDragUL {
  margin: 0;
  padding: 0;
  position: absolute;
  overflow: hidden;
  background-color: #dedede;
  border: 1px #4fcbf0 dotted;
  border-radius: 4px;
  opacity: 0.7;
}

.zTreeMask {
  position: absolute;
  z-index: 10000;
  opacity: 0;
  background-color: #cfcfcf;
}
</style>
