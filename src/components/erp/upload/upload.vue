<!-- 封装的上传组件，方便传权限值做判断 -->
<template>
  <a-modal v-model="uploadOpen" :title="upload.title" @cancel="clickClose" centered :maskClosable="false">
    <a-spin :spinning="loading">
      <div v-if="upload.type == '2' || upload.type == '1'" class="search">
        <!-- 年度 -->
        <span class="demonstration com-color">年：</span>
        <year-picker v-model="year" style="min-width: 120px;"/>
        <!-- 月份 -->
        <div v-if="upload.type == '2'" class="template-month">
          <span class="demonstration com-color" style="float: left;">月：&nbsp;</span>
          <a-checkbox-group style="float: left;" v-model="months">
            <a-checkbox v-for="(item,index) in monOptions" :key="index" :value="item.value">{{item.label}}</a-checkbox>
          </a-checkbox-group>
        </div>
      </div>
      <div align="center">
        <a-upload class="upload" ref="upload"
                  action="#" :accept="accept"
                  :beforeUpload="beforeFileUpload"
                  :multiple="true" :remove="remove"
                  :list-type="listType" :file-list="fileList">
          <p class="ant-upload-drag-icon">
            <a-icon type="plus"/>
          </p>
          <p class="ant-upload-text com-color">将文件拖到此处，或点击上传</p>
          <p v-if="upload.type != '3'" class="ant-upload-hint" style="color:red">提示：仅允许导入“xls”或“xlsx”格式文件！</p>
          <p v-if="upload.type == '3'" class="ant-upload-hint" style="color:red">提示：仅允许导入“xls”、“xlsx”、“ftl”、“pdf”、“doc”或“docx”格式文件！</p>
        </a-upload>
        <!--   模板下载  -->
        <slot name='formwork'></slot>
      </div>
    </a-spin>
    <template slot="footer">
      <div class="modal-footer">
        <a-button size="default" @click="clickClose">取消</a-button>
        <a-button size="default" type="primary" :loading="loading" :disabled="constFile == null" @click="submitFileForm">确定</a-button>
      </div>
    </template>
  </a-modal>
</template>

<script>
import { uploadFile } from '@/api/isolarErp/OSSUtil';
import { getSolargisList } from '@/api/isolarErp/config/solargisupload';

export default {
  name: 'upload',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    upload: {
      type: Object,
      default: () => {
        return {
          title: '文件导入',
          type: '', // 1：年度预算页面，2：月度费用，3:模板上传，其他就不传
          open: false,
          url: '',
          params: null// 自定义参数
        };
      }
    },
    // 外加参数
    params: {
      type: Object,
      default: () => {
        return {};
      }
    },
    // check是否存在文件（solargis上传使用）
    check: {
      type: Boolean,
      default: false
    },
    listType: {
      type: String,
      default: 'picture'
    },
    formworkUrl: {
      type: String,
      default: () => ''
    }
  },
  watch: {
    'upload.open' (val, old) {
      this.uploadOpen = val;
      if (val) {
        this.year = this.getNowTime('year');
        if (this.upload.type == '3') {
          this.accept = '.xlsx,.xls,.ftl,.pdf,.doc,.docx';
        }
      } else {
        this.constFile = null;
        this.accept = '.xlsx,.xls';
        this.fileList = [];
      }
    }
  },
  data () {
    return {
      monOptions: [{ label: '1月', value: '1' }, { label: '2月', value: '2' }, { label: '3月', value: '3' }, { label: '4月', value: '4' }, { label: '5月', value: '5' }, { label: '6月', value: '6' },
        { label: '7月', value: '7' }, { label: '8月', value: '8' }, { label: '9月', value: '9' }, { label: '10月', value: '10' }, { label: '11月', value: '11' }, { label: '12月', value: '12' }],
      months: [],
      loading: false,
      fileList: [],
      constFile: null,
      accept: '.xlsx,.xls',
      uploadOpen: false
    };
  },
  mounted () {

  },
  methods: {
    // 文件上传
    ossUpLoadFile (file, blob) {
      let self = this;
      let fileName = file.name;
      let map = {
        'file': blob,
        'params': self.upload.params,
        'fileName': fileName,
        ...self.params
      };
      if (self.upload.type == '1') {
        map.year = self.year;
      }
      if (self.upload.type == '2') {
        map.year = self.year;
        map.months = (self.months).join(',');
      }
      uploadFile(map, self.upload.url).then(res => {
        self.$emit('fileUpload', res.result_data || res.payload, map);
        self.clickClose();
      }).catch(() => {
        self.loading = false;
        self.$emit('change', false);
      });
    },
    // 上传文件前的判断
    beforeFileUpload (file) {
      let self = this;
      self.loading = true;
      if (self.upload.type && !self.year) {
        self.$message.warning('请选择年份');
        self.loading = false;
        return;
      }
      if (self.upload.type == '2' && self.months.length < 1) {
        self.$message.warning('请选择月份');
        self.loading = false;
        return;
      }
      self.fileName = file.name;
      var testmsg = file.name.substring(file.name.lastIndexOf('.') + 1);
      const extension = testmsg === 'xls';
      const extension2 = testmsg === 'xlsx';
      const extension3 = testmsg === 'ftl';
      const extension4 = testmsg === 'pdf';
      const extension5 = testmsg === 'doc';
      const extension6 = testmsg === 'docx';
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (self.upload.type == '3') {
        if (!extension && !extension2 && !extension3 && !extension4 && !extension5 && !extension6) {
          self.$message.warning('上传文件只能是 xlsx、xls、ftl、pdf、doc、docx格式!');
          self.loading = false;
          return false;
        }
      } else {
        if (!extension && !extension2) {
          self.$message.warning('上传文件只能是 xlsx、xls格式!');
          self.loading = false;
          return false;
        }
      }
      if (!isLt10M) {
        self.$message.warning('上传文件大小不能超过 10MB!');
        self.loading = false;
        return false;
      }
      self.constFile = file;
      self.loading = false;
      self.fileList = [file];
      return false;
    },
    // 移除
    remove (file) {
      this.constFile = null;
      this.fileList = [];
    },
    // 提交上传文件
    submitFileForm () {
      const self = this;
      self.loading = true;
      if (self.constFile == null) {
        self.$message.warning('请选择一个文件上传');
        self.loading = false;
        return;
      }
      if (self.upload.type && !self.year) {
        self.$message.warning('请选择年份');
        self.loading = false;
        return;
      }
      if (self.upload.type == '2' && self.months.length < 1) {
        self.$message.warning('请选择月份');
        self.loading = false;
        return;
      }
      let reader = new FileReader();
      reader.readAsDataURL(self.constFile);
      // 转base64
      reader.onload = function () {
        let blob = reader.result.split(',')[1];
        if (self.check) {
          self.checkIsExist(blob);
        } else {
          self.ossUpLoadFile(self.constFile, blob);
        }
      };
    },

    // solargis上传判断选择的年度是否已经上传文件
    checkIsExist (blob) {
      const self = this;
      let map = {
        'year': self.year
      };
      getSolargisList(map).then(res => {
        if (res.result_code == '1') {
          if (res.result_data.rows.length > 0) {
            self.$confirm({
              title: self.year + '年度的文件已经上传，是否覆盖？',
              okText: '是',
              cancelText: '否',
              onOk () {
                self.ossUpLoadFile(self.constFile, blob);
              },
              onCancel () {
                self.loading = false;
              }
            });
          } else {
            self.ossUpLoadFile(self.constFile, blob);
          }
        } else {
          this.$message.warning(res.result_msg);
        }
      });
    },

    clickClose () {
      this.constFile = null;
      this.fileList = [];
      this.months = [];
      this.loading = false;
      this.$emit('change', false);
    }
  }
};
</script>
<style lang="less" scoped>
  .search {
    width: 100%;
    padding-bottom: 10px;

    .template-month {
      width: 100%;
      display: flex;
      align-items: baseline;
      padding-top: 10px;
    }
  }
  :deep(.ant-upload-list-item-name) {
    width: auto;
  }

  :deep(.ant-upload-drag-icon i) {
    font-size: 32px;
    color: #999;
    cursor: pointer;
  }

  :deep(.ant-upload-text) {
    margin-top: 8px;
  }

  .dialog-footer {
    width: 100%;
    text-align: right;
    margin-top: 15px;
  }
  :deep(.ant-checkbox-wrapper) {
    margin-left: 0;
    margin-right: 8px;
  }
</style>
