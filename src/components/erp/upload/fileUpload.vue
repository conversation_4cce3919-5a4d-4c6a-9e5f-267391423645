<!-- 封装的上传组件，方便传权限值做判断 -->
<template>
  <div>
    <a-upload class="upload" ref="upload" action="#" :accept="accept" @change="sendfileList"
    :beforeUpload="beforeFileUpload" :multiple="multiple" @preview="handlePreview"
    :showUploadList="{ showRemoveIcon : isEdit }"
    :remove="handleRemove" list-type="picture" :file-list="fileList" :class="{'upload-inline': isInline}">
      <template v-if="multiple">
        <template v-if="iconType == 'iconbutton'&&isEdit">
          <p  class="ant-upload-drag-icon">
            <a-icon type="plus" />
          </p>
          <p class="ant-upload-text com-color">将文件拖到此处，或点击上传</p>
        </template>
        <template v-else-if="iconType == 'icon'&&isEdit">
          <a-button type="primary" icon="upload"></a-button>
        </template>

      </template>
      <template v-else>
        <template v-if="iconType == 'iconbutton'&&isEdit">
          <a-button>
          <a-icon type="upload" />上传</a-button>
        </template>
        <template v-else-if="iconType == 'button'&&isEdit">
          <a-button class="solar-eye-btn-primary">上传</a-button>
        </template>
      </template>
      <p v-if="showTip&&isEdit&&tip" class="ant-upload-hint" style="color:red">提示：{{tip}}</p>
    </a-upload>
    <pic-view :showDownload="showDownload" :noConvert="noConvert" v-model="file.open" :file="file" @change="handlePreview(null)" />
    <slot name="downloadExcelButton"></slot>
  </div>
</template>

<script>
import picView from '@/components/erp/picView';
import { downloadBizAnnex } from '@/api/isolarErp/employee/employee';
const review_type = ['png', 'jpg', 'jpeg', 'bmp', 'pdf', 'PNG'];
export default {
  name: 'upload',
  components: {
    picView
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: Array,
      default: () => {
        return [];
      }
    },
    accept: {
      type: String,
      default: '.xlsx, .xls'
    },
    // 是否编辑删除
    isEdit: {
      type: Boolean,
      default: true
    },
    // 是否允许拖入
    multiple: {
      type: Boolean,
      default: false
    },
    // 单个文件最大上限
    maxSize: {
      type: Number,
      default: 10
    },
    tip: {
      type: String,
      default: '上传文件只能是 xlsx、xls格式!'
    },
    single: {
      type: Boolean,
      default: false
    },
    open: {
      type: Boolean,
      default: false
    },
    review: {
      Boolean,
      default: true
    },
    iconType: {
      type: String,
      default: 'iconButton'
    },
    showTip: {
      Boolean,
      default: true
    },
    maxNum: {
      type: Number,
      default: null
    },
    // 预览是否展示下载按钮
    showDownload: {
      Boolean,
      default: false
    },
    // 判断预览不转换
    noConvert: {
      type: String,
      default: '0'
    },
    isInline: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    watchfiles (val, old) {
      this.fileList = val;
    }
  },
  computed: {
    watchfiles: function () {
      return [...this.value];
    }
  },
  data () {
    return {
      loading: false,
      defaultFileList: [],
      fileList: [],
      file: {
        open: false
      }
    };
  },
  mounted () {
    this.fileList = this.value;
  },
  methods: {
    // 传递文件值
    sendfileList () {
      this.$emit('getFileList', this.fileList);
    },
    // 上传文件前的判断
    beforeFileUpload (file) {
      let self = this;
      const ext = file.name.substring(file.name.lastIndexOf('.') + 1);
      let accept = (self.accept ? self.accept.toString() : '');
      if (accept && accept.indexOf(ext) == -1) {
        self.$message.warning('上传的文件格式不对，请确认!');
        return false;
      }
      if (!accept && (ext == 'exe' || ext == 'com')) {
        self.$message.warning('不允许上传安装包!');
        return false;
      }
      if (self.maxSize) {
        const maxSize = file.size / 1024 / 1024;
        if (maxSize > self.maxSize) {
          self.$message.warning('文件大小超出上传上限，请确认!');
          return false;
        }
      }
      if (self.maxNum) {
        const length = self.fileList.length;
        if (length >= self.maxNum) {
          self.$message.warning('文件数量超出上传上限，请确认!');
          return false;
        }
      }
      if (self.single) {
        self.fileList = [file];
      } else {
        self.fileList = [...self.fileList, file];
      }
      self.$emit('change', self.fileList);
      return false;
    },
    // 移除
    handleRemove (file) {
      const self = this;
      if (!self.isEdit) {
        return;
      }
      if (self.single) {
        self.fileList = [];
      } else {
        const index = self.fileList.indexOf(file);
        const newFileList = self.fileList.slice();
        newFileList.splice(index, 1);
        self.fileList = newFileList;
      }
      self.$emit('change', self.fileList);
    },
    // 图片类型文件支持预览
    handlePreview (file) {
      if (!this.review) {
        return;
      }
      if (file) {
        /* 追加判断非图片、PDF文件不可预览 */
        let is_pic_pdf = this.checkFileType(file);
        if (!is_pic_pdf) {
          this.downLoad(file);
          return;
        }
        this.file = file;
        this.file.open = true;
      } else {
        this.file = {
          open: false
        };
      }
    },
    /*
        不支持预览的文件点击时下载文件
      */
    downLoad (file) {
      let _this = this;
      _this.$message.info('正在下载请稍后!');
      if (file.id) {
        downloadBizAnnex({ 'fileId': file.id, 'noConvert': '0' }).then(res => {
          _this.$downloadFile(res.result_data);
        }).catch(() => {});
      } else {
        let reader = new FileReader();
        reader.readAsDataURL(file);
        // 转base64
        reader.onload = function () {
          _this.$downloadFile({ 'fileBase64Code': reader.result, 'fileName': file.name || file.annexName || file.pathName });
        };
      }
    },
    /*
        图片、PDF格式判断
      */
    checkFileType (file) {
      let extension0 = '';
      let extension1 = '';
      let extension2 = '';
      if (file.name) {
        extension0 = file.name.split('.')[file.name.split('.').length - 1];
      }
      if (file.annexName) {
        extension1 = file.annexName.split('.')[file.annexName.split('.').length - 1];
      }
      if (file.pathName) {
        extension2 = file.pathName.split('.')[file.pathName.split('.').length - 1];
      }
      return review_type.includes(extension0) || review_type.includes(extension1) || review_type.includes(extension2);
    }
  },
  beforeDestroy () {
    this.defaultFileList = [];
    this.fileList = [];
  }
};
</script>
<style lang="less" scoped>
  :deep(.ant-upload-drag-icon i) {
    font-size: 32px;
    color: #999;
    cursor: pointer;
  }
 :deep(.upload-inline) {
   display: flex;
   .ant-upload-list-picture {
     width: 48%;
     margin-left: 20px;
     margin-top: -10px;
   }
 }
  :deep(.ant-upload-text) {
    margin-top: 8px;
  }
</style>
