<template>
    <!-- 个统计页面顶部数据card -->
    <div class="count-card">
      <div class="left-icon">
        <img :src="path" />
      </div>
      <div class="right-data">
        <div class="data-and-title">
          <div class="amount-title" :title="label">{{ label }}</div>
          <template v-if="customize">
            <div class="amount-data" :title="count">{{count}}</div>
          </template>
          <template v-else>
            <customize>
              <slot name="customize"></slot>
            </customize>
          </template>
        </div>
      </div>
      <!-- <template v-if="type">
        <div class="amount-title" :title="label">{{ label }}</div>
        <div class="money" :title="count">{{count}}</div>
      </template>
      <template v-else>
        <div class="item-center">
          <span class="item-title" :title="label">{{label}}</span>
          <span class="title"  :title="count">{{count}}</span>
        </div>
        <div class="item-right">
          <span>总:&nbsp;{{total}}</span>
        </div>
      </template> -->
    </div>
</template>
<script>
export default {
  props: {
    // 图片路径
    src: {
      type: String,
      default: ''
    },
    // 统计数据
    num: {
      type: [String, Number],
      default: '0'
    },
    // 总计
    sum: {
      type: [String, Number],
      default: '0'
    },
    // 标题
    title: {
      type: String,
      default: ''
    },
    // 背景颜色
    color: {
      type: String,
      default: '#FFFFFF'
    },
    type: {
      type: Boolean,
      default: true
    },
    customize: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    path () {
      return this.src;
    },
    count () {
      return this.num;
    },
    label () {
      return this.title;
    },
    total () {
      return this.sum;
    }
  },
  data () {
    return {
    };
  }
};
</script>
<style lang="less" scoped>
  .count-card{
    width: 100%;
    padding: 0 19px;
    display: flex;
    align-items: center;
    border-radius: 5px;
    margin-bottom: 12px;
    .left-icon {
      max-height: 135px;
      max-width: 207px;
      text-align: center;
      display: flex;
      overflow: hidden;
      img {
        max-width: 100%;
        max-height: 100%;
        margin: auto;
        object-fit: contain;
        overflow: hidden;
      }

    }
    .right-data{
      min-width: calc(100% - 207px);
      padding-left: 53px;
      text-align: center;
      display: flex;
    }
    .data-and-title{
      margin: auto;
      width: 100%;
    }
    .amount-title, .amount-data {
      font-size: 16px;
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      text-align: left;
    }
    .amount-data{
      font-size: 36px;
      font-weight: bold;
    }
    .item-center {
      width: 45%;
      padding-left: 20px;
      text-align: center;
      line-height: 80px;
      font-weight: bold;
      display: flex;
      height: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      .item-title {
        font-size: 16px;
      }
      .title {
        margin-left: 40px;
        font-size: 26px;
      }
    }
    .item-right {
      width: calc(55% - 60px);
      text-align: right;
      font-size: 16px;
      padding-right: 10px;
      text-overflow: ellipsis;
      white-space: nowrap;
      height: 100%;
      overflow: hidden;
    }
  }
     @media screen and (max-width: 1440px) {
       .count-card{
         .left-icon {
            img{
              max-width: 50%;
              max-height: 50%;
         }
         }

         .right-data {
           padding-left: 0;
         }
       }

       }
</style>
