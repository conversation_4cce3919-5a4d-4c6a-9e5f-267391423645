<template>
  <!-- 上传文件预览 -->
  <a-modal title="预览" v-model="file.open" footer :maskClosable="false" centered @cancel="cancel" width="50%" v-bind:class="{'file-review-modal' : full_screen}">
    <a-spin :spinning="loading">
      <template v-if="imgSrc">
        <template v-if="documentType == 'image'">
          <img alt="example" :src="imgSrc" id="imgId"/>
        </template>
        <!-- 新增pdf预览功能 -->
        <template v-else-if="documentType == 'pdf'">
          <iframe :src="imgSrc" :style="{width: pdfWidth + 'px',height: pdfHeight + 'px'}" />
        </template>
      </template>
    </a-spin>
    <!-- 下载、最大最小化-->
    <div class="top-group-btn">
      <a-button v-show="showRotate && base64Code && documentType == 'image'" size="default" icon="redo" title="旋转" @click="rotate">
      </a-button>

      <a-button v-show="showDownload && base64Code" size="default" icon="download" title="下载" @click="download">
      </a-button>

      <a-button size="default" :icon="full_screen ? 'fullscreen-exit' : 'fullscreen'" @click="fullScreen()"></a-button>
    </div>
    <!-- 新增下载文件功能 -->
  </a-modal>
</template>

<script>
import { exportFile } from '@/api/common_gy/erp-manage.js';
import { downloadBizAnnex } from '@/api/isolarErp/employee/employee';
import { getComFilePreview } from '@api/common/common';
export default {
  props: {
    value: {
      type: Boolean,
      default: false
    },
    file: {
      type: Object,
      default: () => {
        return {
          open: false
        };
      }
    },
    // 是否显示下载按钮
    showDownload: {
      type: Boolean,
      default: false
    },
    // 是否显示旋转按钮
    showRotate: {
      type: Boolean,
      default: true
    },
    // 判断预览不转换
    noConvert: {
      type: String,
      default: '0'
    }
  },
  data () {
    return {
      imgSrc: '',
      numCar: 0,
      base64Code: null,
      loading: false,
      fileObj: null,
      documentType: 'image',
      pdfWidth: 800,
      pdfHeight: 600,
      full_screen: false,
      errorImg: 'data:image/jpg;base64,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'
    };
  },
  watch: {
    file (val, old) {
      this.fileObj = val;
    },
    'file.open' (val, old) {
      if (val) {
        this.init();
      } else {
        Object.assign(this.$data, this.$options.data());
      }
    }
  },

  methods: {
    init () {
      const self = this;
      self.loading = true;
      self.base64Code = null;
      /*
          此判断表明文件是直接上传oss的，并不是ERP里共同的附件表
        */
      if (self.fileObj.is_oss) {
        self.showOssFile(self.fileObj);
        return;
      }
      if (self.fileObj.hasOwnProperty('base64File')) {
        self.base64Code = self.imgSrc = `data:image/png;base64,${self.fileObj.base64File}`;
        self.loading = false;
        return;
      }
      if (self.fileObj.hasOwnProperty('id') && self.fileObj.id) {
        let map = {
          fileId: self.fileObj.id,
          noConvert: self.noConvert
        };
        // 新增文件类型判断
        self.getDocumentType(self.fileObj.fileName || self.fileObj.name || self.fileObj.annexName);
        let preview = self.fileObj.isEmployee ? downloadBizAnnex(map) : getComFilePreview(map); // 判断是否是员工
        preview.then(res => {
          let perfix = self.documentType == 'pdf' ? 'data:application/pdf;base64,' : 'data:image/png;base64,';
          self.base64Code = self.fileObj.isEmployee ? `${perfix}${res.result_data.fileBase64Code}` : res
            .result_data;
          self.imgSrc = self.documentType == 'pdf' ? self.pdfUrl(self.base64Code) : JSON.parse(JSON.stringify(self
            .base64Code));
          self.loading = false;
        }).catch(() => {
          self.imgSrc = self.errorImg;
          self.loading = false;
        });
      } else if (self.fileObj.hasOwnProperty('uid')) {
        // 新增文件类型判断
        self.getDocumentType(self.fileObj.name);
        let reader = new FileReader();
        reader.readAsDataURL(self.fileObj);
        // 转base64
        reader.onload = function () {
          self.loading = false;
          self.base64Code = reader.result;
          self.imgSrc = self.documentType == 'pdf' ? self.pdfUrl(reader.result) : JSON.parse(JSON.stringify(reader
            .result));
        };
      } else {
        self.loading = false;
        self.imgSrc = self.errorImg;
        self.$message.warning('加载失败');
      }
    },
    // 文件预览
    showOssFile (file) {
      let _this = this;
      _this.getDocumentType(file.fileName || file.pathName);
      let perfix = _this.documentType == 'pdf' ? 'data:application/pdf;base64,' : 'data:image/png;base64,';
      if (file.base64code && file.base64code != ' ') { // 有OSS的文件地址
        _this.base64Code = `${perfix}${file.base64code}`;
        _this.imgSrc = _this.documentType == 'pdf' ? _this.pdfUrl(_this.base64Code) : JSON.parse(JSON.stringify(_this.base64Code));
        _this.loading = false;
      } else {
        _this.getFileFromOss(file).then(values => {
          if (values.indexOf(',') != -1) {
            values = values.split(',')[1];
          }
          values = `${perfix}${values}`;
          _this.base64Code = values;
          _this.imgSrc = _this.documentType == 'pdf' ? _this.pdfUrl(values) : JSON.parse(JSON.stringify(values));
          _this.loading = false;
        }).catch(() => {
          _this.imgSrc = _this.errorImg;
          _this.$message.error('加载失败');
        });
      }
    },
    getFileFromOss (file) {
      return new Promise((resolve, reject) => {
        let params = {
          path: file.pathName,
          fileName: file.fileName
        };
        exportFile('/sys/oss/file/downLoad', params).then((data) => {
          let fileReader = new FileReader();
          fileReader.onload = function (e) {
            resolve(e.target.result);
          };
          fileReader.readAsDataURL(data);
        }).catch((err) => {
          reject(err);
        });
      });
    },
    getDocumentType (name) {
      if (name) {
        let arr = name.split('.');
        this.documentType = arr[arr.length - 1].toLowerCase() == 'pdf' ? 'pdf' : 'image';
        if (this.documentType == 'pdf') {
          this.pdfWidth = document.body.offsetWidth * 0.7;
          this.pdfHeight = document.body.offsetHeight * 0.6;
        }
      }
    },
    cancel () {
      this.$emit('change', false);
      Object.assign(this.$data, this.$options.data());
      var box = document.getElementById('imgId');
      box.style.transform = 'rotateZ(' + 0 + 'deg)';
    },
    /*
        base64转URL
      */
    pdfUrl (fileBase64Code) {
      let code = JSON.parse(JSON.stringify(fileBase64Code));
      let arr = code.split(',');
      let bstr = (arr.length == 2 ? window.atob(arr[1]) : window.atob(arr[0]));
      let l = bstr.length;
      let u8Arr = new Uint8Array(l);
      while (l--) {
        u8Arr[l] = bstr.charCodeAt(l);
      }
      const blob = new Blob([u8Arr], {
        type: 'application/pdf'
      });
      let url = null;
      if (window.createObjectURL != undefined) { // basic
        url = window.createObjectURL(blob) + '#toolbar=0';
      } else if (window.webkitURL != undefined) { // webkit or chrome
        url = window.webkitURL.createObjectURL(blob) + '#toolbar=0';
      } else if (window.URL != undefined) { // mozilla(firefox)
        url = window.URL.createObjectURL(blob) + '#toolbar=0';
      }
      return url;
    },
    download () {
      this.$downloadFile({
        fileBase64Code: this.base64Code,
        fileName: this.file.fileName ? this.file.fileName : this.file.name
      });
    },
    fullScreen () {
      this.full_screen = !this.full_screen;
    },
    rotate () {
      this.numCar = this.numCar + 1;
      var box = document.getElementById('imgId');
      box.style.transform = 'rotateZ(' + 90 * this.numCar + 'deg)';
    }
  }
};
</script>

<style lang="less" scoped>
  .file-review-modal{
    :deep(.ant-modal) {
      width: 100% !important;
      height: 100% !important;
      padding: 0 !important;
    }
    :deep(.ant-modal-content) {
      width: 100% !important;
      height: 100% !important;
    }
    :deep(.ant-modal-body) {
      max-height: unset !important;
      height: calc(100% - 55px) !important;
    }
    iframe{
      width: 100% !important;
    }
  }
  .top-group-btn {
    position: absolute;
    right: 50px;
    top: 0;
    background: transparent;
    display: flex;
    align-items: center;
    .ant-btn {
      border: none;
      height: 50px;
      width: 40px;
      line-height: 60px;
      background: transparent;
    }
    :deep(.ant-btn-icon-only > i) {
      color: inherit;
      vertical-align: unset !important;
    }
  }
  :deep(.ant-spin-nested-loading) {
    width: 100% !important;
    height: 100% !important;
  }
  :deep(.ant-spin-container) {
    display: flex;
    width: 100%;
    height: 100%;
    min-height: 70vh;
    overflow: hidden;
    img {
      max-width: 100%;
      max-height: 100%;
      margin: auto;
    }
    iframe{
      height: unset !important;
    }
  }
</style>
<style>
  .solar-eye-dark .top-group-btn .ant-btn{
    color: #fff;
  }
</style>
