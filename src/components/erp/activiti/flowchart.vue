<!-- 查看流程信息  -->
<template>
<a-modal :title="title" v-model="visible" :maskClosable="false" :footer="null" centered width="70%" @cancel="cancel">
    <a-spin :spinning="loading">
      <a-row :gutter="24">
        <a-col :sm="24" :md="6" style="height: 60vh;overflow: auto;">
          <a-timeline style="margin: auto;">
            <a-timeline-item v-for="(activity, index) in activities" :key="index" :color="activity.color">
              <p>{{activity.content}}</p>
              <p>{{activity.timestamp}}</p>
            </a-timeline-item>
          </a-timeline>
        </a-col>
        <a-col :sm="24" :md="18" style="height: 60vh;display: flex;">
          <img :src="workFlowImag64" style="margin: auto; width: 100%;" />
        </a-col>
      </a-row>
    </a-spin>
  </a-modal>
</template>
<script>
import { listAuditInfo, showProcessDiagram } from '@/api/isolarErp/com/flowchart';
export default {
  model: {
    prop: 'visible',
    event: 'change'
  },
  props: {
    title: {
      type: String,
      required: '流程查看'
    },
    visible: {
      type: Boolean,
      required: false
    },
    // 流程相关id
    processInstanceId: {
      type: String,
      required: ''
    },
    // 当前流程人员
    flowUser: {
      type: String,
      required: ''
    }
  },
  watch: {
    visible: function (val, old) {
      if (val) {
        this.showFlowChart();
      }
    }
  },
  data () {
    return {
      loading: false,
      // 流程信息
      activities: [],
      // 流程图
      workFlowImag64: ''
    };
  },
  mounted () {
    if (this.visible) {
      this.showFlowChart();
    }
  },
  methods: {
    // 获取流程图
    showFlowChart () {
      let self = this;
      self.loading = true;
      let requestMap = {
        pProcessInstanceId: self.processInstanceId,
        flowUser: self.flowUser
      };
      const promise = new Promise((resolve, reject) => {
        showProcessDiagram(requestMap).then(res => {
          resolve(res.result_data);
        }).catch(err => {
          reject(err);
        });
      });
      promise.then(function (value) {
        listAuditInfo(requestMap).then(res => {
          self.workFlowImag64 = value;
          res.result_data.forEach(item => {
            if (item.color) {
              if (item.color == 'limegreen') {
                item.color = 'green';
              }
            } else {
              item.color = 'blue';
            }
          });
          self.activities = res.result_data;
          self.loading = false;
        });
      }, function () {
        self.loading = false;
        self.$message.warning('流程信息获取失败请检查参数');
        self.cancel();
      });
    },
    // 弹窗关闭回调方法
    cancel () {
      this.$emit('change', false);
      this.activities = [];
      this.workFlowImag64 = '';
    }
  }
};
</script>

<style lang="less" scoped>
  :deep(.ant-timeline){
    padding-top: 5px;
  }
</style>
