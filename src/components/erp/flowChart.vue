<template>
  <div>
    <div @click="openFlowChart" class="flow-chart-btn">
      <svg-icon iconClass="flow"></svg-icon>
      流程图
    </div>
    <flow-chart-drawer
      v-if="showDiagram"
      ref="flowChartDrawer"
      :parentId="parentId"
      :processInstanceId="processInstanceId"
      :flowUser="flowUser"
      task-no=""
      v-bind='$attrs'
    />
  </div>
</template>

<script>
export default {
  name: 'FlowChart',
  props: {
    // 流程节点id
    processInstanceId: {
      type: String,
      required: true
    },
    // 当前流程人员
    flowUser: {
      type: String,
      required: true
    },
    /* 抽屉挂在的DOM节点ID，必要参数 */
    parentId: {
      type: String,
      required: true,
      default: ''
    }
  },
  data () {
    return {
      showDiagram: false
    };
  },
  methods: {
    openFlowChart () {
      this.showDiagram = true;
      const _self = this;
      this.$nextTick(() => {
        _self.$refs.flowChartDrawer.openView();
      });
    }
  }
};
</script>

<style scoped>

</style>
