<template>
  <a-spin class="file-link" :spinning="loading">
    <template v-if="contractFile != null && contractFile != ''">
      <a-row v-for="item in annexArr" :key="item.fileId">
        <a-col :span="24" :title="item.fileName" class="hide-col">
          <a-button type="link" @click.stop="download(item)" style="padding-left:0">{{item.fileName}}</a-button>
          <!-- <a @click.stop="download(item)">{{item.fileName}}</a> -->
        </a-col>
      </a-row>
    </template>
    <template v-else>
      <span>--</span>
    </template>
  </a-spin>
</template>

<script>
import {
  downloadBizAnnex
} from '@/api/isolarErp/contract/contractList.js';
export default {
  props: {
    contractFile: {
      type: String,
      default: null
    }
  },
  data () {
    return {
      annexArr: [],
      loading: false
    };
  },
  watch: {
    contractFile () {
      this.initAnnex();
    }
  },
  mounted () {
    this.initAnnex();
  },
  methods: {
    initAnnex () {
      if (this.contractFile) {
        this.annexArr = JSON.parse(this.contractFile);
      }
    },
    download (item) {
      this.loading = true;
      downloadBizAnnex({
        fileId: item.fileId
      }).then(res => {
        this.loading = false;
        this.$downloadFile(res.result_data);
      }).catch(() => {
        this.loading = false;
      });
    }
  }
};
</script>

<style lang="less" scoped>
  :deep(.file-link) {
    text-decoration: underline;
    color: blue;
    width: 100%;
  }

  .hide-col {
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
</style>
