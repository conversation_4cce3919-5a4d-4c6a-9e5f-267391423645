import FormContainer from './FormContainer.vue';
import TreeSelect from './TreeSelect.vue';
import Date from './Date.vue';
import Ellipsis from './Ellipsis.vue';
import Input from './Input.vue';

import SelectMultiple from './SelectMultiple.vue';
import Time from './Time.vue';

export default {
  install (Vue) {
    Vue.component('Date', Date);
    Vue.component('Ellipsis', Ellipsis);
    Vue.component('FormContainer', FormContainer);
    Vue.component('Input', Input);
    Vue.component('SelectMultiple', SelectMultiple);
    Vue.component('Time', Time);
    Vue.component('TreeSelect', TreeSelect);
  }
};
