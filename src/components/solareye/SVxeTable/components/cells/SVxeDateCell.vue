<template>
  <a-date-picker
    ref="datePicker"
    :value="innerDateValue"
    allowClear
    :format="dateFormat"
    :showTime="isDatetime"
    dropdownClassName="s-vxe-date-picker"
    style="min-width: 0;"
    v-bind="cellProps"
    @change="handleChange"
  />
</template>

<script>
import moment from 'moment';
import { SVxeTypes } from '@/components/solareye/SVxeTable/index';
import SVxeCellMixins, { dispatchEvent } from '@/components/solareye/SVxeTable/mixins/SVxeCellMixins';

export default {
  name: 'SVxeDateCell',
  mixins: [SVxeCellMixins],
  props: {},
  data () {
    return {
      innerDateValue: null
    };
  },
  computed: {
    isDatetime () {
      return this.$type === SVxeTypes.datetime;
    },
    dateFormat () {
      let format = this.originColumn.format;
      return format || (this.isDatetime ? 'YYYY-MM-DD HH:mm:ss' : 'YYYY-MM-DD');
    }
  },
  watch: {
    innerValue: {
      immediate: true,
      handler (val) {
        if (val == null || val === '') {
          this.innerDateValue = null;
        } else {
          this.innerDateValue = moment(val, this.dateFormat);
        }
      }
    }
  },
  methods: {
    handleChange (mom, dateStr) {
      this.handleChangeCommon(dateStr);
    }
  },
  // 【组件增强】注释详见：SVxeCellMixins.js
  enhanced: {
    aopEvents: {
      editActived: event => dispatchEvent(event, 'ant-calendar-picker', el => el.children[0].dispatchEvent(event.$event))
    }
  }
};
</script>

<style scoped>

</style>
