<template>
  <input-pop
    :value="innerValue"
    :width="300"
    :height="210"
    v-bind="cellProps"
    style="width: 100%;"
    @change="handleChangeCommon"
  />
</template>

<script>
import InputPop from '@/components/solareye/minipop/InputPop';
import SVxeCellMixins, { dispatchEvent } from '@/components/solareye/SVxeTable/mixins/SVxeCellMixins';

export default {
  name: 'SVxeTextareaCell',
  mixins: [SVxeCellMixins],
  components: { InputPop },
  // 【组件增强】注释详见：SVxeCellMixins.js
  enhanced: {
    installOptions: {
      autofocus: '.ant-input'
    },
    aopEvents: {
      editActived: event => dispatchEvent(event, 'anticon-fullscreen')
    }
  }
};
</script>

<style scoped>

</style>
