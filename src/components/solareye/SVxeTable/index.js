import { installCell, mapCell } from './install';
import SVxeTable from './components/SVxeTable';

import SVxeSlotCell from './components/cells/SVxeSlotCell';
import SVxeNormalCell from './components/cells/SVxeNormalCell';
import SVxeInputCell from './components/cells/SVxeInputCell';
import SVxeDateCell from './components/cells/SVxeDateCell';
import SVxeSelectCell from './components/cells/SVxeSelectCell';
import SVxeCheckboxCell from './components/cells/SVxeCheckboxCell';
import SVxeUploadCell from './components/cells/SVxeUploadCell';
import { TagsInputCell, TagsSpanCell } from './components/cells/SVxeTagsCell';
import SVxeProgressCell from './components/cells/SVxeProgressCell';
import SVxeTextareaCell from './components/cells/SVxeTextareaCell';
import SVxeDragSortCell from './components/cells/SVxeDragSortCell';

// 组件类型
export const SVxeTypes = {
  // 为了防止和 vxe 内置的类型冲突，所以加上一个前缀
  // 前缀是自动加的，代码中直接用就行（SVxeTypes.input）
  _prefix: 's-',

  // 行号列
  rowNumber: 'row-number',
  // 选择列
  rowCheckbox: 'row-checkbox',
  // 单选列
  rowRadio: 'row-radio',
  // 展开列
  rowExpand: 'row-expand',
  // 上下排序
  rowDragSort: 'row-drag-sort',

  input: 'input',
  inputNumber: 'inputNumber',
  textarea: 'textarea',
  select: 'select',
  date: 'date',
  datetime: 'datetime',
  checkbox: 'checkbox',
  upload: 'upload',
  // 下拉搜索
  selectSearch: 'select-search',
  // 下拉多选
  selectMultiple: 'select-multiple',
  // 进度条
  progress: 'progress',

  // 拖轮Tags（暂无用）
  tags: 'tags',

  slot: 'slot',
  normal: 'normal',
  hidden: 'hidden'
};

// 注册自定义组件
export const AllCells = {
  ...mapCell(SVxeTypes.normal, SVxeNormalCell),
  ...mapCell(SVxeTypes.input, SVxeInputCell),
  ...mapCell(SVxeTypes.inputNumber, SVxeInputCell),
  ...mapCell(SVxeTypes.checkbox, SVxeCheckboxCell),
  ...mapCell(SVxeTypes.select, SVxeSelectCell),
  ...mapCell(SVxeTypes.selectSearch, SVxeSelectCell), // 下拉搜索
  ...mapCell(SVxeTypes.selectMultiple, SVxeSelectCell), // 下拉多选
  ...mapCell(SVxeTypes.date, SVxeDateCell),
  ...mapCell(SVxeTypes.datetime, SVxeDateCell),
  ...mapCell(SVxeTypes.upload, SVxeUploadCell),
  ...mapCell(SVxeTypes.textarea, SVxeTextareaCell),

  ...mapCell(SVxeTypes.tags, TagsInputCell, TagsSpanCell),
  ...mapCell(SVxeTypes.progress, SVxeProgressCell),

  ...mapCell(SVxeTypes.rowDragSort, SVxeDragSortCell),
  ...mapCell(SVxeTypes.slot, SVxeSlotCell)

  /* hidden 是特殊的组件，不在这里注册 */
};

export { installCell, mapCell };

export default SVxeTable;
