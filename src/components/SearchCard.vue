<template>
  <div class="search_card">
    <template v-for="(item,index)  in itemList">
      <div class="search-tab" :key="index" @click="clickEvent(item.id,item.value,index)" :class="{'active': selectId == item.id}">
        <div class="image-box">
          <img :src="getPng(item.icon)"/>
        </div>
        <div>
          <div class="search-title">
            <span class="search-type">{{ item.name }}</span>
          </div>
          <div class="search-num">{{ item.num }}</div>
        </div>
      </div>
    </template>
    <div v-if="itemList.length > 0" class="high-bg" :style="{left: activeIndex * 20 + '%'}"></div>
    <div v-if="itemList.length > 0" class="high-border" :style="{left: (activeIndex * 20) + 4.3 + '%'}"></div>
  </div>
</template>

<script>
import { mixin } from '@/utils/mixin';

export default {
  mixins: [mixin],
  data () {
    return {
      selectId: null,
      activeIndex: 0
    };
  },
  props: {
    itemList: {
      type: Array,
      default: () => {
        return [];
      }
    }
  },
  created () {
    this.selectId = this.itemList[0].id;
  },
  methods: {
    clickEvent (id, value, index) {
      this.selectId = id;
      this.activeIndex = index;
      this.$emit('click', value);
    },
    getPng (name) {
      let navTheme = name == 'up' || name == 'down' ? '' : '_' + this.navTheme;
      return require('@/assets/images/taskCenter/task/' + name + navTheme + '.png');
    }
  }
};
</script>

<style lang="less" scoped>
.search_card {
  display: flex;
  height: 120px;
  background: #FFFFFF;
  box-shadow: 0px 2px 11px 0px rgba(0, 0, 0, 0.05);
  border-radius: 4px;

  .search-tab {
    width: 20%;
    padding: 20px 0 20px 20px;
    height: 100%;
    display: flex;
    cursor: pointer;
    position: relative;

    &:first-child {
      border-top-left-radius: 4px;
      border-bottom-left-radius: 4px;
    }

    .image-box {
      width: 80px;
      margin-right: 12px;
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        width: 72px;
      }
    }

    .search-type {
      color: #333333;
      font-size: 14px;
      margin-right: 8px;
      font-weight: 600;
    }

    .search-num {
      font-weight: 600;
      color: #333333;
      line-height: 42px;
      font-size: 34px;
      margin: 4px 0;
    }
  }

  .search-tab:hover {
    background: #F6F6F6;;
  }

  .active:hover {
    background: transparent;
  }

  .high-bg {
    height: 90px;
    width: 20%;
    background: linear-gradient(180deg, #FFFFFF 0%, #FF8F33 100%);
    opacity: 0.1;
    position: absolute;
    top: 30px;
    cursor: pointer;
  }

  .high-border {
    width: 11.4%;
    height: 3px;
    background: #FF8F33;
    border-radius: 2px;
    position: absolute;
    top: 117px;
    transition: left 0.2s;
    cursor: pointer;
  }

  .search-tab + .search-tab::after {
    height: 74px;
    width: 1px;
    margin: 28px 0px 28px -20px;
    border-left: 1px solid #EEEEEE;
    content: '';
    position: absolute;
    top: 0;
  }
}

.solar-eye-dark {
  .search_card {
    background: #17202F;
    box-shadow: 0px 2px 11px 0px rgba(0, 0, 0, 0.05);

    .search-tab {
      z-index: 1;

      .search-type, .search-num {
        color: #FFFFFF;
      }
    }
  }

  .search-tab:hover {
    background: #1A273B;
  }

  .active:hover {
    background: transparent;
  }

  .high-bg {
    background: linear-gradient(180deg, rgba(35, 51, 78, 0) 0%, #23334E 100%);
    opacity: 1;
  }

  .high-border {
    background: #60CAFE;
  }

  .search-tab + .search-tab::after {
    border-left: 1px solid #364457;
  }
}
</style>
