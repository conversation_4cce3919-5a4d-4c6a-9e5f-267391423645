<template>
  <a-modal
    :title="title"
    width="480"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @cancel="handleCancel"
    :maskClosable="false" wrapClassName="security_modal"
  >
    <template v-if="step==0 && visible">
      <get-code smsMode="2" @ok="getCode" ref="getCode"></get-code>
    </template>
    <a-spin :spinning="confirmLoading" v-if="step == 1 && visible">
      <a-form :form="form" style="width:480px">
        <!-- <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="旧密码">
           <a-input-password placeholder="请输入旧密码" v-decorator="[ 'oldpassword', validatorRules.oldpassword]" />
        </a-form-item> -->

        <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="新密码">
          <a-input-password placeholder="请输入新密码" autocomplete="new-password" v-decorator="['password', validatorRules.password]" />
        </a-form-item>

        <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="确认新密码">
          <a-input-password
            placeholder="请确认新密码"
            autocomplete="new-password"
            v-decorator="['confirmpassword', validatorRules.confirmpassword]"
          />
        </a-form-item>
      </a-form>
    </a-spin>
    <template slot="footer">
      <template v-if="step == 0">
        <a-button @click="handleCancel" class="solar-eye-btn-primary-cancel">关闭</a-button>
        <a-button type="primary" @click="nextStep" :loading='nextLoading'>下一步</a-button>
      </template>
      <template v-if="step == 1">
        <!-- <a-button @click="() => (step = 0)" class="solar-eye-btn-primary-cancel">上一步</a-button> -->
        <a-button type="primary" @click="handleOk">确定</a-button>
      </template>
    </template>

  </a-modal>
</template>

<script>
import { postAction } from '@/api/manage';
import GetCode from '@/components/com/GetCode';
import { aesEncrypt } from '@/utils/verify';
export default {
  name: 'UserPassword',
  components: { GetCode },
  data () {
    return {
      title: '修改密码',
      modalWidth: 800,
      visible: false,
      confirmLoading: false,
      nextLoading: false,
      validatorRules: {
        // oldpassword: {
        //   rules: [{
        //     required: true, message: '请输入旧密码!'
        //   }]
        // },
        password: {
          rules: [
            {
              required: true,
              message: '请输入新密码!'
            },
            {
              validator: this.validateToNextPassword
            }
          ]
        },
        confirmpassword: {
          rules: [
            {
              required: true,
              message: '请确认新密码!'
            },
            {
              validator: this.compareToFirstPassword
            }
          ]
        }
      },
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      defaultInfo: {},
      form: this.$form.createForm(this),
      url: 'sys/user/updatePassword',
      username: '',
      step: 1
    };
  },
  methods: {
    show (uname) {
      if (!uname) {
        this.$message.warning('当前系统无登录用户!');
      } else {
        this.username = uname;
        this.form.resetFields();
        this.visible = true;
      }
    },
    handleCancel () {
      this.close();
    },
    close () {
      this.$emit('close');
      this.visible = false;
      this.disableSubmit = false;
      this.selectedRole = [];
      this.nextLoading = false;
    },
    nextStep () {
      this.nextLoading = true;
      this.$refs.getCode.validateForm();
    },
    getCode (params) {
      if (params) {
        this.step = 1;
        this.defaultInfo = params;
      }
      this.nextLoading = false;
    },
    handleOk () {
      const that = this;
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true;
          let params = Object.assign({}, this.defaultInfo, values);
          params.password = aesEncrypt(params.password);
          params.confirmpassword = aesEncrypt(params.confirmpassword);
          params.mobile = this.$ls.get('Login_Userinfo').phone;
          params.username = this.$ls.get('Login_Userinfo').username;
          postAction('/sys/user/passwordChange', params)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message);
                that.close();
                that.$store.dispatch('Logout').then(() => {
                  if (this.$route.path !== '/user/login') {
                    this.$router.push({
                      path: '/user/login',
                      params: { 'username': this.defaultInfo.username }
                    }).catch(res => {
                      console.log(res);
                    });
                  }
                });
              } else {
                that.$message.warning(res.message);
              }
            })
            .finally(() => {
              that.confirmLoading = false;
            });
        }
      });
    },
    validateToNextPassword (rule, value, callback) {
      if (value) {
        let reg = /^(?=.*[a-zA-Z])(?=.*\d)(?=.*[\x21-\x2f\x3a-\x40\x5b-\x60\x7B-\x7F])[\da-zA-Z\x21-\x2f\x3a-\x40\x5b-\x60\x7B-\x7F]{8,14}$/;
        if (!reg.test(value)) {
          callback(new Error('密码必须包含数字、字母及特殊符号，长度8-14位，不允许有空格！'));
        } else {
          const form = this.form;
          const confirmpassword = form.getFieldValue('confirmpassword');
          if (confirmpassword && value !== confirmpassword) {
            callback(new Error('两次输入的密码不一致!'));
          } else if (value == confirmpassword) {
            form.validateFields(['confirmpassword'], { force: true });
          }
          callback();
        }
      }
      callback();
    },
    compareToFirstPassword (rule, value, callback) {
      const form = this.form;
      const password = form.getFieldValue('password');
      if (value && value !== password) {
        callback(new Error('两次输入的密码不一致!'));
      } else if (value && password && value == password) {
        form.validateFields(['password'], { force: true });
      }
      callback();
    }
  }
};
</script>

<style lang="less" scoped>
  :deep(.security_modal) {
    .ant-modal-header {
      border-bottom: none;
    }
    .ant-modal-footer {
      border-top: none;
      padding-bottom: 24px;
    }
    .ant-modal-title {
      font-size: 16px;
      font-weight: 600;
    }
    .ant-modal-header {
      padding: 32px 32px 0;
    }
    .ant-modal-close {
      top: 12px;
    }
    .ant-modal-body {
      padding: 16px 32px 0;
    }
  }
</style>
