<template>
  <div class="header-logo-drawer" :class="{ width: isExist(topList).length >= 1 || isExist(bottomList).length > 1 }">
    <div class="header-main" id="guide-step1">
      <div class="title">SolarEye</div>
      <div class="content">
        <div class="menu selected" @click="toNewPage()">
          <img :src="require(`@/assets/images/changMenu/workD${navTheme ? '' : '_dark'}.png`)" class="left" />
          <div class="right">
            <div class="right-top right-top-r">
              SolarEye
              <img :src="require(`@/assets/images/changMenu/D${navTheme ? '' : '_dark'}.png`)" />
            </div>
            <div class="right-bottom">数字运维系统</div>
          </div>
        </div>
        <div
          class="menu"
          @click="toNewPage(env + '.isolareye.com/houseHold', '/houseHold')"
          v-if="isExist('/houseHold')"
        >
          <img :src="require(`@/assets/images/changMenu/houseHold${navTheme ? '' : '_dark'}.png`)" class="left" />
          <div class="right">
            <div class="right-top right-top-r">
              SolarEye <img :src="require(`@/assets/images/changMenu/H${navTheme ? '' : '_dark'}.png`)" />
            </div>
            <div class="right-bottom">数字运维系统</div>
          </div>
        </div>
        <div
          class="menu"
          @click="toNewPage(env + '.isolarhealth.com', '/iSolarHealth')"
          v-if="isExist('/iSolarHealth')"
        >
          <img :src="require(`@/assets/images/changMenu/health${navTheme ? '' : '_dark'}.png`)" class="left" />
          <div class="right">
            <div class="right-top">iSolarHealth</div>
            <div class="right-bottom">健康评测系统</div>
          </div>
        </div>
        <div
          class="menu"
          @click="toNewPage(env + '.isolareye.com/zx', '/smartAnalyze')"
          v-if="isExist('/smartAnalyze')"
        >
          <img :src="require(`@/assets/images/changMenu/star${navTheme ? '' : '_dark'}.png`)" class="left" />
          <div class="right">
            <div class="right-top">iSolarStar</div>
            <div class="right-bottom">数据分析系统</div>
          </div>
        </div>
      </div>
      <div class="content" v-if="isExist(bottomList).length > 0">
        <div
          class="menu"
          @click="toNewPage(env + '.isolarhealth.com/newCare', '/solarCare')"
          v-if="isExist('/solarCare')"
        >
          <img :src="require(`@/assets/images/changMenu/angel${navTheme ? '' : '_dark'}.png`)" class="left" />
          <div class="right">
            <div class="right-top">iSolarAngel</div>
            <div class="right-bottom">智能巡检系统</div>
          </div>
        </div>
        <div
          class="menu"
          @click="toNewPage(env + '.isolareye.com/allInOne', '/integratedPlatform')"
          v-if="isExist('/integratedPlatform')"
        >
          <img :src="require(`@/assets/images/changMenu/allIn${navTheme ? '' : '_dark'}.png`)" class="left" />
          <div class="right">
            <div class="right-top">SolarEye</div>
            <div class="right-bottom">一体化平台</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { message } from 'ant-design-vue';
import { ACCESS_TOKEN, TENANT_ID } from '@/store/mutation-types';
const env = process.env.VUE_APP_ENV;
const topList = ['/houseHold', '/iSolarHealth', '/smartAnalyze'];
const bottomList = ['/solarCare', '/integratedPlatform'];
export default {
  name: 'NewChange',
  data () {
    return {
      env: env ? (env === 'pro' ? 'www' : env) : 'fat',
      topList: topList,
      bottomList: bottomList
    };
  },
  computed: {
    permissionMenuList () {
      return this.$store.state.user.firstMenu;
    },
    token () {
      return this.$store.state.user.token || Vue.ls.get(ACCESS_TOKEN);
    },
    tenantId () {
      return this.$store.state.user.tenantid || Vue.ls.get(TENANT_ID);
    },
    navTheme () {
      return this.$store.state.app.theme == 'light';
    }
  },
  methods: {
    /**
     * funcName isExist 判断是否存在
     * params {platform} String|| Array string，返回是否存在，array 返回数组
     */
    isExist (platform) {
      let menu = JSON.parse(sessionStorage.getItem('ALL_PERMISSION_MENU'));
      let isArray = Array.isArray(platform);
      return isArray ? platform.filter((item) => menu.indexOf(item) > -1) : menu.indexOf(platform) > -1;
    },
    toNewPage (path, platform) {
      if (path) {
        let isInclude = this.isExist(platform);
        if (!isInclude) {
          this.$message.error({
            content: (h) =>
              h('span', {}, [
                h('span', '暂无权限，请联系管理员！'),
                h(
                  'span',
                  {
                    style: {
                      paddingLeft: '40px',
                      cursor: 'pointer'
                    },
                    on: {
                      click: () => {
                        message.destroy();
                      }
                    }
                  },
                  'X'
                )
              ]),
            duration: 5,
            class: 'no-platform',
            style: {
              marginTop: '5vh'
            }
          });
          return;
        }
        window.open(`https://${path}/#/user/login?tenant_id=${this.tenantId}&token=${this.token}&deviceId=${localStorage.getItem('deviceId')}`);
      } else {
        this.$router.push(this.permissionMenuList.path);
      }
    }
  }
};
</script>
<style lang="less" scoped>
.header-logo-drawer {
  position: absolute;
  top: 40px;
  left: 24px;
  z-index: 9999;
  &.width {
    min-width: 524px;
  }
  .header-main {
    border-radius: 16px;
    background: linear-gradient(180deg, #fff6ef 0%, #ffffff 17%);
    box-sizing: border-box;
    border: 1.22px solid #e2e2e2;
    box-shadow: 0px 6.12px 24.46px 0px rgba(0, 0, 0, 0.14);
    height: auto;
    margin-top: 30px;
  }
  .title {
    height: 65px;
    line-height: 65px;
    font-family: 'NeoGram-DemiBold';
    font-size: 22px;
    font-weight: 540;
    letter-spacing: 0.02em;
    color: #191818;
    border-bottom: 1px solid #e2e2e2;
    padding-left: 32px;
  }
  .content {
    padding: 24px 32px 14px;
    display: flex;
    flex-flow: wrap;
    .menu {
      display: flex;
      flex-direction: row;
      width: 212px;
      height: 73px;
      background: rgba(243, 243, 243, 0.01);
      align-items: center;
      padding: 14px 11px 11px;
      cursor: pointer;
      margin-bottom: 10px;
      border-radius: 8px;
      border: 1px solid rgba(243, 243, 243, 0.01);
      &:hover {
        background: rgba(243, 243, 243, 0.7);
      }
    }
    .menu.selected {
      background: #f3f3f3;
    }
    .menu:nth-child(odd) {
      margin-right: 32px;
    }
    .menu:only-child {
      margin-right: 0;
    }
    .menu .left {
      width: 48px;
      height: 48px;
      object-fit: cover;
    }
    .menu .right {
      margin-left: 8px;
      .right-top {
        font-family: PingFangSC-Medium;
        font-size: 19px;
        font-weight: normal;
        line-height: 24px;
        font-style: oblique;
        font-weight: 540;
        color: #191818;
      }
      .right-bottom {
        font-size: 15px;
        margin-top: 2px;
        font-weight: 200;
        line-height: 14px;
      }
      .right-top-r {
        position: relative;
        img {
          position: absolute;
          top: -10px;
          right: -22px;
          width: 23px;
          height: 23px;
          object-fit: cover;
        }
      }
    }
  }
  .content + .content {
    border-top: 1px solid #e7e7e7;
    padding: 24px 0 14px;
    margin: 0 32px;
  }
}
.solar-eye-dark {
  .header-logo-drawer {
    .header-main {
      background: linear-gradient(180deg, #004F73 0%, #08233E 17%);
      box-sizing: border-box;
      border: 1.22px solid rgba(80, 115, 149, 0.33);
      box-shadow: 0px 6.12px 24.46px 0px rgba(137, 196, 255, 0.22);
    }
    .title {
      color: #fff;
      border-bottom-color: #325272;
    }
    .content {
      .menu .right .right-top {
        color: #fff;
      }
      .menu .right .right-bottom {
        color: #9da8b2;
      }
    }
    .content + .content {
      border-top-color: #325272;
    }
    .menu {
      &:hover {
        background: rgba(18, 51, 83, 0.6);
      }
    }
    .menu.selected {
      background: #123353;
    }
  }
}
</style>
