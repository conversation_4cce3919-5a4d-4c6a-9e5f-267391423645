<template>
  <div class="user-wrapper" :class="theme">
    <!--做头部菜单栏导航 -->
    <!-- 解决全局样式冲突的问题 -->
    <!-- 菜单搜索改为动态组件，在手机端呈现出弹出框 -->
    <component :is="searchMenuComp" :style="{marginRight: searchMenuVisible ? '-41px' : 0}"
      :visible="searchMenuVisible" title="搜索菜单" :footer="null" @cancel="searchMenuVisible=false">
      <a-select class="search-input" showSearch :showArrow="false" v-model="chosenMenu" placeholder="输入关键字搜索"
        optionFilterProp="children" :filterOption="filterOption" @select="searchMethods" @blur="hiddenClick"
        :dropdownClassName="searchMenuVisible ? 'search-menu': 'close-search-menu'"
        :style="{width: searchMenuVisible ? '240px' : '1px', opacity: searchMenuVisible ? 1 : 0,
          transition: searchMenuVisible ? 'width 0.2s' : 'none', visibility: searchMenuVisible ? 'visible' : 'hidden'}">
        <a-select-option v-for="(site,index) in searchMenuOptions" :key="index" :value="site.id">{{site.meta.title}}
        </a-select-option>
      </a-select>

    </component>

    <div class="align-center icon-div" v-show="searchMenuVisible">
      <svg-icon iconClass="menu_search" class="menu-icon"></svg-icon>
      <div class="split-line"></div>
      <span title="关闭搜索" class="close-icon" @click="closeMenu">
        <svg-icon iconClass="close_normal" class="menu-icon" title="关闭搜索"></svg-icon>
      </span>
    </div>
    <!-- 搜索 -->
    <a-dropdown placement="bottomCenter" v-if="!searchMenuVisible">
      <span class="action screen-action ant-dropdown-link" @click="showClick">
        <svg-icon iconClass="menu_search" class="nav-icon" ></svg-icon>
      </span>
      <a-menu slot="overlay" class="user-dropdown-menu-wrapper">
        <a-menu-item key="1" style="width:50px">
          <span>搜索</span>
        </a-menu-item>
      </a-menu>
    </a-dropdown>
    <!-- 配置中心 -->
    <template v-if="systemConfigShow || serviceConfigShow">
      <a-dropdown placement="bottomCenter" v-if="systemConfigShow && serviceConfigShow">
        <span class="action screen-action ant-dropdown-link user-dropdown-menu">
          <svg-icon iconClass="setting" class="nav-icon"></svg-icon>
        </span>
        <a-menu slot="overlay" class="user-dropdown-menu-wrapper nav-setting-icon">
          <a-menu-item key="1" @click="goSystemConfig" style="width:90px" v-show="systemConfigShow">
            <span>系统配置</span>
          </a-menu-item>
          <a-menu-item key="2" @click="goBusinessConfig" style="width:90px" v-show="serviceConfigShow">
            <span>业务配置</span>
          </a-menu-item>
        </a-menu>
      </a-dropdown>
      <span v-else class="action screen-action ant-dropdown-link user-dropdown-menu">
        <svg-icon :title="配置" @click="goSystemConfig" iconClass="setting" class="nav-icon" />
      </span>
    </template>
    <!-- 总经理信箱 -->
    <a-dropdown v-if="showMenu('/editMail')" placement="bottomCenter" v-model="showPopover">
      <span class="action screen-action">
        <img class="mail-icon" :src="mailImg" @click="editMail"/>
      </span>
      <a-menu slot="overlay" class="user-dropdown-menu-wrapper nav-mail-icon">
        <a-menu-item key="1">
          <span>戳我给总经理发悄悄话</span>
        </a-menu-item>
      </a-menu>
    </a-dropdown>
    <!-- 消息列表 -->
    <!-- <a-dropdown v-if="showMenu('/system/tidings')" placement="bottomCenter">
      <span class="action screen-action ant-dropdown-link">
        <a-badge :count="isNotReadNum">
          <a class="logout_title message" href="javascript:;" @click="handleTidings">
            <svg-icon iconClass="message" class="nav-icon"></svg-icon>
          </a>
        </a-badge>
      </span>
      <a-menu slot="overlay" class="user-dropdown-menu-wrapper">
        <a-menu-item key="1" style="width:50px">
          <span>消息</span>
        </a-menu-item>
      </a-menu>
    </a-dropdown> -->
    <!-- 大屏 -->
    <a-dropdown v-if="showTo" placement="bottomCenter">
      <span class="action screen-action ant-dropdown-link" @click="handleScreen">
        <svg-icon iconClass="change-screen" class="nav-icon"></svg-icon>
      </span>
      <a-menu slot="overlay" class="user-dropdown-menu-wrapper">
        <a-menu-item key="1" style="width:50px">
          <span>大屏</span>
        </a-menu-item>
      </a-menu>
    </a-dropdown>
    <!--吉电大屏-->
       <a-dropdown v-if="showJD" placement="bottomCenter">
      <span class="action screen-action ant-dropdown-link" @click="handleJDScreen('/#/screen/jd')">
        <svg-icon iconClass="JD" class="nav-icon"></svg-icon>
      </span>
      <a-menu slot="overlay" class="user-dropdown-menu-wrapper">
        <a-menu-item key="1" style="width:50px">
          <span>大屏</span>
        </a-menu-item>
      </a-menu>
    </a-dropdown>

    <!-- AIGC -->
    <div class="ai-icon-area cursor-pointer" @click="openAiModel" v-if='showAigc' >
      <a-tooltip placement="bottom" :visible="aigcTipVisiable">
        <template #title>
          <span>嗨！有问题试试来问我吧~</span>
        </template>
        <img class="ai-icon" :src="AigcGif + '?' + Math.random()" />
      </a-tooltip>
    </div>

    <!-- 用户操作 -->
    <a-dropdown placement="bottomCenter">
      <span class="action ant-dropdown-link user-dropdown-menu">
        <img class="person-img" src="@assets/images/public/personal.png">
        <span v-if="isDesktop()" class="nick-name">{{ nickname() }}</span>
      </span>
      <a-menu slot="overlay" class="user-dropdown-menu-wrapper">
        <!-- <a-menu-item key="3" @click="systemSetting" v-if="isHideSetting">
          <a-icon type="tool" />
          <span>系统设置</span>
        </a-menu-item> -->
        <a-menu-item key="4" @click="updatePassword">
          <a-icon type="setting" />
          <span>密码修改</span>
        </a-menu-item>
        <a-menu-item key="7" @click="linkTo('/isolarerp/employee/individual')">
          <a-icon type="cluster" />
          <span>个人主页</span>
        </a-menu-item>
        <!-- <a-menu-item key="8" @click="linkTo('/user/tickling/tickling')">
          <a-icon type="exception" />
          <span>意见反馈</span>
        </a-menu-item> -->
          <a-menu-item key="10" @click="linkTo('/personal/downBigExcel')">
          <a-icon type="download" />
          <span>我的下载</span>
        </a-menu-item>
        <a-menu-item key="10" @click="downloadApp">
          <svg-icon iconClass="mobile" class="mobile-icon"></svg-icon>
          <span>APP下载</span>
        </a-menu-item>
        <!-- <a-menu-item key="5" @click="updateCurrentDepart">
          <a-icon type="cluster"/>
          <span>切换部门</span>
        </a-menu-item> -->
        <a-menu-item v-if="isAdmin" key="6" @click="clearCache">
          <a-icon type="sync" />
          <span>清理缓存</span>
        </a-menu-item>
<!--        <a-menu-item key="9" @click="linkToRead">-->
<!--          <a-icon type="file-pdf" />-->
<!--          <span>操作手册</span>-->
<!--        </a-menu-item>-->
      </a-menu>
    </a-dropdown>
    <span class="action screen-action logout-icon">
      <a class="logout_title" href="javascript:;" @click="handleLogout">
        <svg-icon iconClass="logout" class="nav-icon" style="color: #B1BFC9;"></svg-icon>
      </a>
    </span>
    <user-password ref="userPassword"></user-password>
    <setting-drawer ref="settingDrawer" :closable="true" title="系统设置"></setting-drawer>

    <!--抽屉组件-->
    <drawer-view ref="editMailDrawerView"/>
    <download-app ref="downloadApp"/>
    <Aigc ref='AiModel' />
  </div>
</template>

<script>
// import HeaderNotice from './HeaderNotice'
import DownloadApp from './DownloadApp';
import UserPassword from './UserPassword';
import SettingDrawer from '@/components/setting/SettingDrawer';
// import DepartSelect from './DepartSelect'
import {
  mapActions,
  mapGetters,
  mapState
} from 'vuex';
import {
  mixin,
  mixinDevice
} from '@/utils/mixin.js';
import {
  getFileAccessHttpUrl,
  getAction
} from '@/api/manage';
import {
  downloadOperationManual
} from '@/api/isolarErp/config/templateupload';
// import { getPdcaMsgLabel } from '@/api/tidings';
// import Vue from 'vue'
import {
  UI_CACHE_DB_DICT_DATA,
  USER_NAME
} from '@/store/mutation-types';
import Avatar from '@/assets/user_logo.png';
import {
  updateTheme
} from '@/components/tools/setting';
import { isSupportWebp } from '@/utils/hasPermission';
import mail_gif from '../../assets/mail.gif';
import mail_end from '../../assets/mail-end.png';
import { getGuide } from '../../api/workPortal/workPortal';
import Aigc from './Aigc';
export default {
  name: 'UserMenu',
  mixins: [mixinDevice, mixin],
  data () {
    return {
      showPopover: false,
      aigcTipVisiable: false,
      mailImg: '',
      AigcGif: require('@/assets/images/aigc/animation.gif'),
      isNotReadNum: 0,
      hsaTidings: false, // 是否显示消息列表
      // 头部菜单搜索规范命名 --------------
      searchMenuOptions: [],
      searchMenuComp: 'span',
      searchMenuVisible: false,
      deveavatar: Avatar,
      isHideSetting: process.env.NODE_ENV == 'development',
      settingShow: false,
      skinShow: false,
      configShow: false,
      serviceConfigShow: false,
      systemConfigShow: false,
      chosenMenu: undefined,
      selectPosition: {
        position: 'absolute',
        top: 0,
        left: '30px',
        color: 'ff'
      }
    };
  },
  components: {
    // HeaderNotice,
    UserPassword,
    Aigc,
    // DepartSelect,
    SettingDrawer,
    DownloadApp
  },
  props: {
    theme: {
      type: String,
      required: false,
      default: 'dark'
    }
  },
  /* update_begin author:zhaoxin date:20191129 for: 做头部菜单栏导航 */
  created () {
    let lists = [];
    this.searchMenus(lists, this.permissionMenuList);
    this.searchMenuOptions = [...lists];

    if (document.getElementsByClassName('solar-eye-dark').length > 0) {
      this.selectBg('dark', '#267DCF');
    } else {
      this.selectBg(this.navTheme, this.primaryColor);
    }
  },
  mounted () {
    // 如果是单点登录模式
    if (process.env.VUE_APP_SSO == 'true') {
      let depart = this.userInfo().orgCode;
      if (!depart) {
        this.updateCurrentDepart();
      }
    }
    // 配置中心图标
    this.permissionMenuList.forEach((item) => {
      if (item.name == 'configuration') {
        // this.configShow = true;
        if (item.children && Array.isArray(item.children)) {
          item.children.forEach(child => {
            if (child.name == 'systemConfiguration') {
              this.systemConfigShow = true;
            }
            if (child.name == 'serviceConfiguration') {
              this.serviceConfigShow = true;
            }
          });
        }
      }
    });
    // getPdcaMsgLabel({}).then(res => {
    //   let isNotReadNum = res.result_data.isNotReadNum || 0;
    //   this.$store.dispatch('setNotReadNum', { num: isNotReadNum });
    // }).catch(() => {
    //   this.$store.dispatch('setNotReadNum', { num: 0 });
    // });
    /* 此处判断当前用户是否首次登录（不考虑cookie失效的问题） */
    let _this = this;
    let user_name = Vue.ls.get(USER_NAME);
    let cookies = document.cookie.split(';');
    let login_user = cookies.find(item => user_name == (item.trim()).split('=')[0]);
    if (login_user) {
      _this.mailImg = mail_end;
    } else {
      let expire = new Date((new Date()).getTime() + 24 * 3600000);
      expire = '; expires=' + expire.toGMTString();
      document.cookie = `${user_name}=${escape(user_name)}${expire}`;
      _this.mailImg = mail_gif + '?' + new Date().getTime();
      window.setTimeout(() => {
        _this.showPopover = true;
        _this.mailImg = mail_end;
      }, 1000 * 7);
      window.setTimeout(() => {
        _this.showPopover = false;
      }, 1000 * 10);
    }

    this.initAigc();
  },
  computed: {
    ...mapState({
      // 后台菜单
      permissionMenuList: state => state.user.permissionList,
      notReadNum: state => state.user.isNotReadNum
    }),
    showTo () {
      return localStorage.getItem('hasScreen') == 'true';
    },
    showAigc () {
      return localStorage.getItem('AIGC') == 'true';
    },
    isAdmin () {
      return Vue.ls.get(USER_NAME) == 'admin';
    },
    showJD () {
      return localStorage.getItem('JDScreen') == 'true';
    }
  },
  /* update_end author:zhaoxin date:20191129 for: 做头部菜单栏导航 */
  watch: {
    // update-begin author:sunjianlei date:20200219 for: 菜单搜索改为动态组件，在手机端呈现出弹出框
    device: {
      immediate: true,
      handler () {
        this.searchMenuVisible = false;
        // this.searchMenuComp = this.isMobile() ? 'a-modal' : 'span';
      }
    },
    notReadNum (val) {
      this.isNotReadNum = val || 0;
    }
    // update-end author:sunjianlei date:20200219 for: 菜单搜索改为动态组件，在手机端呈现出弹出框
  },
  methods: {
    showMenu (url) {
      let menuList = sessionStorage.getItem('menuList');
      menuList = (menuList ? JSON.parse(menuList) : []);
      let items = menuList.filter(item => (item.path == url));
      return items.length == 1;
    },
    async initAigc () {
      let res = await getGuide({
        businessType: '1',
        sourceType: 1
      });
      if (res.result_data.isFirstAigcNoviceGuide == 1) {
        this.aigcTipVisiable = true;
        let timer = setTimeout(() => {
          this.aigcTipVisiable = false;
          clearTimeout(timer);
          timer = null;
        }, 3000);
      }
    },
    openAiModel () {
      this.$refs.AiModel.open();
    },
    goSystemConfig () {
      this.$router.push({ path: '/systemConfiguration' });
    },
    goBusinessConfig () {
      this.$router.push({ path: '/serviceConfiguration' });
    },

    handleScreen () {
      this.$router.push({ path: '/screen/page' });
    },
    handleJDScreen (url) {
      // this.$router.push({ path: '/screen/jd' });
      window.open(window.location.origin + url);
    },
    handleTidings () {
      this.$router.push({ path: '/system/tidings' });
    },
    showSkin () {
      this.skinShow = !this.skinShow;
    },
    selectBg (theme, color) {
      this.selectPosition.color = color;
      if (theme == 'dark') {
        this.selectPosition.left = '40px';
      } else {
        this.selectPosition.left = '100px';
      }
      this.$store.dispatch('ToggleTheme', theme);
      let isEnglish = translate.language.getCurrent() == 'english' ? 'english' : '';
      document.getElementsByTagName('body')[0].classList = 'solar-eye-' + theme + ' ' + isEnglish + (isSupportWebp() ? ' webpa' : '');
      if (this.primaryColor !== color) {
        this.$store.dispatch('ToggleColor', color);
        updateTheme(color);
      }
    },
    /* update_begin author:zhaoxin date:20191129 for: 做头部菜单栏导航 */
    showClick () {
      this.$emit('change', true);
      this.searchMenuVisible = true;
    },
    hiddenClick () {
      this.shows = false;
    },
    /* update_end author:zhaoxin date:20191129 for: 做头部菜单栏导航 */
    ...mapActions(['Logout']),
    ...mapGetters(['nickname', 'avatar', 'userInfo']),
    getAvatar () {
      return getFileAccessHttpUrl(this.avatar());
    },
    handleLogout () {
      const that = this;
      this.$confirm({
        title: '提示',
        content: '真的要注销登录吗 ?',
        okText: '确定',
        cancelText: '取消',
        onOk () {
          return that.Logout({}).then(() => {
            // 中英文切换显示
            let translate = document.getElementById('translate');
            translate.style.display = 'block';
            localStorage.setItem('translate', 'block');
            // update-begin author:wangshuai date:20200601 for: 退出登录跳转登录页面
            that.$router.push({
              path: '/user/login'
            });
            let end = setInterval(function () {}, 5000);
            for (let i = 0; i <= end; i++) { // 关闭我的下载页面定时器
              clearInterval(i);
            }
            document.title = 'SolarEye登录页';
            let logingUserInfo = localStorage.getItem('logingUserInfo');
            let deviceId = localStorage.getItem('deviceId');
            let toInfo = window.localStorage.getItem('to');
            // update-end author:wangshuai date:20200601 for: 退出登录跳转登录页面
            // window.location.reload()
            // 清除浏览器localStorage、sessionStorage
            window.localStorage.clear();
            window.sessionStorage.clear();
            window.localStorage.setItem('logingUserInfo', logingUserInfo);
            window.localStorage.setItem('to', toInfo || 'chinese_simplified');
            window.localStorage.setItem('deviceId', deviceId);
          }).catch(err => {
            that.$message.error({
              title: '错误',
              description: err.message
            });
          });
        },
        onCancel () {}
      });
    },
    updatePassword () {
      let username = this.userInfo().username;
      this.$refs.userPassword.show(username);
    },
    /*
        写邮件
      */
    editMail () {
      this.isLoadedJs('dll/editor.js', () => {
        this.$refs.editMailDrawerView.init('1', null, '/config/mail/modules/editMail');
      });
    },
    // 跳转个人主页、意见反馈
    linkTo (url) {
      let menuList = sessionStorage.getItem('menuList');
      menuList = (menuList ? JSON.parse(menuList) : []);
      let items = menuList.filter(item => (item.path == url));
      if (items.length == 1) {
        this.$router.push(url);
      } else {
        this.$message.warning('暂无菜单权限！无法跳转！');
      }
    },
    /*
        新窗口打开操作手册
      */
    linkToRead () {
      this.$notification.success({
        message: '系统提示',
        description: '正在加载请稍后...',
        duration: 3
      });
      downloadOperationManual({}).then(res => {
        let arr = res.result_data.fileBase64Code.split(',');
        let bstr = (arr.length == 2 ? window.atob(arr[1]) : window.atob(arr[0]));
        let l = bstr.length;
        let u8Arr = new Uint8Array(l);
        while (l--) {
          u8Arr[l] = bstr.charCodeAt(l);
        }
        const blob = new Blob([u8Arr], {
          type: 'application/pdf'
        });
        let url = null;
        if (window.createObjectURL != undefined) { // basic
          url = window.createObjectURL(blob) + '#toolbar=1';
        } else if (window.webkitURL != undefined) { // webkit or chrome
          url = window.webkitURL.createObjectURL(blob) + '#toolbar=1';
        } else if (window.URL != undefined) { // mozilla(firefox)
          url = window.URL.createObjectURL(blob) + '#toolbar=1';
        }
        if (url) {
          window.open(url);
        } else {
          this.$notification.error({
            message: '系统提示',
            description: '文件加载失败...',
            duration: 3
          });
        }
      }).catch(() => {});
    },
    updateCurrentDepart () {
      this.$refs.departSelect.show();
    },
    systemSetting () {
      this.settingShow = true;
      this.$refs.settingDrawer.showDrawer();
    },
    /* update_begin author:zhaoxin date:20191129 for: 做头部菜单栏导航 */
    searchMenus (arr, menus) {
      for (let i of menus) {
        if (!i.hidden && i.component !== 'layouts/RouteView' && i.path.indexOf('solarCare') == -1) {
          arr.push(i);
        }
        if (i.children && i.children.length > 0) {
          this.searchMenus(arr, i.children);
        }
      }
    },
    filterOption (input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    },
    // update_begin author:sunjianlei date:20191230 for: 解决外部链接打开失败的问题
    searchMethods (value) {
      setTimeout(() => {
        this.chosenMenu = undefined;
      }, 100);
      let route = this.searchMenuOptions.filter(item => item.id === value)[0];
      if (route.meta.internalOrExternal === true || route.component.includes('layouts/IframePageView')) {
        this.$ls.set(this.getTitle(route.meta.title), route.meta.title);
        this.$router.push({
          path: this.getTitle(route.meta.title, true)
        }).catch(() => {});
      } else {
        this.$router.push({
          path: route.path
        });
      }
      this.$emit('change', false);
      this.searchMenuVisible = false;
    },
    getTitle (title, flag) {
      let storageKey = '';

      JSON.parse(sessionStorage.getItem('menuList')).map(item => {
        if (item.title == title) {
          storageKey = flag ? item.parent + '/' + item.path : 'route:title:' + item.path;
        }
      });

      return storageKey;
    },
    // update_end author:sunjianlei date:20191230 for: 解决外部链接打开失败的问题
    /* update_end author:zhaoxin date:20191129 for: 做头部菜单栏导航 */
    /* update_begin author:liushaoqian date:20200507 for: 刷新缓存 */
    clearCache () {
      getAction('sys/dict/refleshCache').then((res) => {
        if (res.success) {
          // 重新加载缓存
          getAction('sys/dict/queryAllDictItems').then((res) => {
            if (res.success) {
              Vue.ls.remove(UI_CACHE_DB_DICT_DATA);
              Vue.ls.set(UI_CACHE_DB_DICT_DATA, res.result);
            }
          });
          this.$message.success('刷新缓存完成！');
        }
      }).catch(e => {
        this.$message.warn('刷新缓存失败！');
        console.log('刷新失败', e);
      });
    },
    // 下载App
    downloadApp () {
      this.$refs.downloadApp.init();
    },
    closeMenu () {
      this.$emit('change', false);
      this.searchMenuVisible = false;
      this.chosenMenu = undefined;
    }
    /* update_end author:liushaoqian date:20200507 for: 刷新缓存 */
  },
  beforeDestroy () {
    this.mailImg = '';
    this.$store.dispatch('setNotReadNum', { num: 0 });
  }
};
</script>

<style lang="less" scoped>
.ai-icon-area{
  margin-right: 16px;
  display: flex;
  align-items: center;
  .ai-icon{
    width: 22px;
    height: 22px;
  }
}
.nav-icon {
  width: 20px !important;
  height: 20px !important;
}
.person-img {
  margin: 14px 8px 14px 0;
  width: 28px;
  height: 28px;
}
.nick-name {
  color: #fff;
  opacity: 0.7;
}
.logout-icon {
  border-left:none;
  margin: 0 8px 0 4px;
}
.mobile-icon {
  margin-right: 8px;
  color: inherit;
}
.icon-div {
  position: relative;
  right: 24px;
  z-index: 1;
  .menu-icon {
    width: 20px;
    height: 20px;
    cursor: pointer;
  }
  .close-icon {
    position: relative;
    top: 3px;
  }
  .split-line {
    width: 1px;
    height: 14px;
    background: #5D6067;
    margin: 0 8px;
  }
}

  :deep(.bell-icon > svg){
    width: 22px;
    height: 26px;
  }
  :deep(.ant-badge-count){
    zoom: 0.833;
    line-height: 16px;
    padding: 0 2px;
    height: 18px;
    box-shadow: unset;
  }
  .mail-icon{
    width: 20px;
  }
  .screen-action{
    position: relative;
    border-left: none !important;
  }
  .user-wrapper{
    display: inline-flex;
    align-items: center;
    padding-left: 8px;
    .action{
      display: flex !important;
      align-items: center;
      justify-content: center;
    }
  }
  /* update_begin author:zhaoxin date:20191129 for: 让搜索框颜色能随主题颜色变换*/
  /* update-begin author:sunjianlei date:20191220 for: 解决全局样式冲突问题 */
  .user-wrapper .search-input {
    color: white;
    :deep(.ant-select-search) {
      padding-right: 54px;
    }
    :deep(.ant-select-selection) {
      background: #484A4F;
      box-shadow: none !important;
      border: none !important;
      border-radius: 3px;
      &__placeholder,
      &__field__placeholder {
        color: rgba(255, 255, 255, 0.3);
        top: 16px;
      }
    }

  }

  .action-img {
    width: 20px;
    height: 20px;
  }

  /* update-end author:sunjianlei date:20191220 for: 解决全局样式冲突问题 */
  /* update_end author:zhaoxin date:20191129 for: 让搜索框颜色能随主题颜色变换*/
</style>

<style lang="less" scoped>
  .logout_title {
    color: inherit;
    text-decoration: none;
    width: 100%;
    height: 100%;
    display: block;
  }

  .message {
    :deep(.anticon) {
      font-size: 18px;
    }
  }
  .svg-setting {
    vertical-align: middle;
    :deep(.svg-icon) {
      width: 1.5em !important;
      height: 1.5em !important;
    }
  }
  :deep(.search-menu) {
    top: 42px !important;
  }
</style>
