<template>
  <global-layout @dynamicRouterShow="dynamicRouterShow">
    <!-- 提升右键菜单的层级 -->
    <contextmenu :itemList="menuItemList" :visible.sync="menuVisible" style="z-index: 9999;" @select="onMenuSelect"/>
    <a-tabs
      @contextmenu.native="e => onContextmenu(e)"
      v-if="multipage"
      :active-key="activePage"
      class="tab-layout-tabs"
      style="height:40px"
      :hide-add="true"
      type="editable-card"
      @change="changePage"
      @tabClick="tabCallBack"
      @edit="editPage" @dblclick="routeReload(true)">
      <template  v-for="page in pageList">
         <a-tab-pane :id="page.fullPath" :key="page.fullPath" :closable="!(page.meta.title == firstMenu.meta.title)" v-if="page.meta && firstMenu.meta">
        <span slot="tab" :pagekey="page.fullPath" >{{ page.meta.title }}</span>
      </a-tab-pane>
      </template>
    </a-tabs>
    <div class="main-parent">
       <keep-alive v-if="multipage" :max="5">
          <router-view v-if="reloadFlag"/>
        </keep-alive>
        <template v-else >
          <router-view v-if="reloadFlag"/>
        </template>
    </div>
    <modify-password ref="modifyPassword"></modify-password>
  </global-layout>
</template>

<script>
import GlobalLayout from '@/components/page/GlobalLayout';
import Contextmenu from '@/components/menu/Contextmenu';
import { mixin, mixinDevice } from '@/utils/mixin.js';
import { triggerWindowResizeEvent } from '@/utils/util';
import { mapState } from 'vuex';
/// / import Vue from 'vue'
import { CACHE_INCLUDED_ROUTES } from '@/store/mutation-types';
import { setWaterMark, removeWatermark } from '@/utils/addWaterMarker';
import ModifyPassword from '@/views/user/alteration/ModifyPassword.vue';
// const indexKey = '/dashboard/analysis';
export default {
  name: 'TabLayout',
  components: {
    GlobalLayout,
    Contextmenu,
    ModifyPassword
  },
  mixins: [mixin, mixinDevice],
  data () {
    return {
      pageList: [],
      linkList: [],
      activePage: '',
      menuVisible: false,
      menuItemList: [
        { key: '4', icon: 'reload', text: '刷 新' },
        { key: '1', icon: 'arrow-left', text: '关闭左侧' },
        { key: '2', icon: 'arrow-right', text: '关闭右侧' },
        { key: '3', icon: 'close', text: '关闭其它' }
      ],
      reloadFlag: true,
      activePath: ''
    };
  },
  /* update_begin author:wuxianquan date:20190828 for: 关闭当前tab页，供子页面调用 ->望菜单能配置外链，直接弹出新页面而不是嵌入iframe #428 */
  provide () {
    return {
      closeCurrent: this.closeCurrent
    };
  },
  /* update_end author:wuxianquan date:20190828 for: 关闭当前tab页，供子页面调用->望菜单能配置外链，直接弹出新页面而不是嵌入iframe #428 */
  computed: {
    multipage () {
      // 判断如果是手机模式，自动切换为单页面模式
      if (this.isMobile()) {
        return false;
      } else {
        return this.$store.state.app.multipage;
      }
    },
    ...mapState({
      // 后台菜单
      permissionMenuList: (state) => state.user.permissionList,
      firstMenu: (state) => state.user.firstMenu
    }),
    userInfo () {
      return this.$store.state.user.info;
    }
  },
  created () {
    document.body.style.overflow = 'hidden';
    if (this.$route.path != this.firstMenu.path) {
      this.addIndexToFirst();
    }

    // 复制一个route对象出来，不能影响原route
    let currentRoute = Object.assign({}, this.$route);
    currentRoute.meta = Object.assign({}, currentRoute.meta);
    // update-begin-author:sunjianlei date:20191223 for: 修复刷新后菜单Tab名字显示异常
    let storeKey = 'route:title:' + currentRoute.fullPath;
    let routeTitle = this.$ls.get(storeKey);
    if (routeTitle) {
      currentRoute.meta.title = routeTitle;
    }
    // update-end-author:sunjianlei date:20191223 for: 修复刷新后菜单Tab名字显示异常
    this.pageList.push(currentRoute);
    this.linkList.push(currentRoute.fullPath);
    this.activePage = currentRoute.fullPath;
  },
  mounted () {
    let userInfo = this.userInfo;
    if (userInfo && userInfo.realname) {
      let telp = userInfo.phone ? userInfo.phone.substr(-4) : '';
      setWaterMark('', userInfo.realname + ' ' + telp);
    } else {
      setWaterMark('');
    }
    if (this.$ls.get('firstTimeLogin') && localStorage.getItem('IS_FIRST_NOVICE_GUIDE') != '1') { this.$refs.modifyPassword.showModal(); }
  },
  watch: {
    '$route': function (newRoute, old) {
      this.activePage = newRoute.fullPath;
      if (!this.multipage) {
        this.linkList = [newRoute.fullPath];
        this.pageList = [Object.assign({}, newRoute)];
        // update-begin-author:taoyan date:20200211 for: TASK #3368 【路由缓存】首页的缓存设置有问题，需要根据后台的路由配置来实现是否缓存
      } else if (this.firstMenu.path == newRoute.fullPath) {
        // 首页时 判断是否缓存 没有缓存 刷新之
        if (newRoute.meta.keepAlive === false) {
          this.routeReload();
        }
        // update-end-author:taoyan date:20200211 for: TASK #3368 【路由缓存】首页的缓存设置有问题，需要根据后台的路由配置来实现是否缓存
      } else if (this.linkList.indexOf(newRoute.fullPath) < 0) {
        // 单独处理诊断中心及诊断中心相邻标签页关闭不清除缓存的问题
        let len = this.pageList.length;
        if (newRoute.fullPath == '/AlarmCenter' || (len > 0 && this.pageList[len - 1].fullPath == '/AlarmCenter')) {
          if (!window.comefromAlarmCenter) {
            this.routeReload();
          }
        }
        this.linkList.push(newRoute.fullPath);
        this.pageList.push(Object.assign({}, newRoute));

        /// / update-begin-author:sunjianlei date:20200103 for: 如果新增的页面配置了缓存路由，那么就强制刷新一遍 #842
        // if (newRoute.meta.keepAlive) {
        //   this.routeReload()
        //   }
      } else if (this.linkList.indexOf(newRoute.fullPath) >= 0) {
        let oldIndex = this.linkList.indexOf(newRoute.fullPath);
        let oldPositionRoute = this.pageList[oldIndex];
        this.pageList.splice(oldIndex, 1, Object.assign({}, newRoute, { meta: oldPositionRoute.meta }));
        // if (!newRoute.meta.keepAlive) {
        //   this.routeReload()
        // }
      }
    },
    'activePage': function (key) {
      let index = this.linkList.lastIndexOf(key);
      let waitRouter = this.pageList[index];
      // 【TESTA-523】修复：不允许重复跳转路由异常
      if (waitRouter.fullPath !== this.$route.fullPath) {
        this.$router.push(Object.assign({}, waitRouter));
      }
      this.changeTitle(waitRouter.meta.title);
    },
    'multipage': function (newVal) {
      if (this.reloadFlag) {
        if (!newVal) {
          this.linkList = [this.$route.fullPath];
          this.pageList = [this.$route];
        }
      }
    },
    // update-begin-author:sunjianlei date:20191223 for: 修复从单页模式切换回多页模式后首页不居第一位的 BUG
    device () {
      if (this.multipage && this.linkList.indexOf(this.firstMenu.path) === -1) {
        this.addIndexToFirst();
      }
    }
    // update-end-author:sunjianlei date:20191223 for: 修复从单页模式切换回多页模式后首页不居第一位的 BUG
  },
  methods: {

    dblckickEvent () {
    },
    // update-begin-author:sunjianlei date:20191223 for: 修复从单页模式切换回多页模式后首页不居第一位的 BUG
    // 将首页添加到第一位
    addIndexToFirst () {
      let firstMenu = this.firstMenu;
      this.pageList.splice(0, 0, {
        name: firstMenu.name,
        path: firstMenu.path,
        fullPath: firstMenu.path,
        meta: firstMenu.meta
      });
      this.linkList.splice(0, 0, firstMenu.path);
    },
    changeTitle (title) {
      // let firstMenu = this.firstMenu;
      let projectTitle = 'SolarEye';
      // 首页特殊处理
      // if (this.$route.path === firstMenu.path) {
      //   document.title = projectTitle;
      // } else {
      document.title = title + ' · ' + projectTitle;
      // }
    },

    changePage (key) {
      this.activePage = key;
    },
    tabCallBack () {
      this.$nextTick(() => {
        setTimeout(() => {
          // 省市区组件里面给window绑定了个resize事件 导致切换页面的时候触发了他的resize，但是切换页面，省市区组件还没被销毁前就触发了该事件，导致控制台报错，加个延迟
          triggerWindowResizeEvent();
        }, 20);
      });
    },
    editPage (key, action) {
      this[action](key);
    },
    remove (key) {
      let firstMenu = this.firstMenu;
      if (key == firstMenu.path) {
        this.$message.warning('首页不能关闭!');
        return;
      }
      if (this.pageList.length === 1) {
        this.$message.warning('这是最后一页，不能再关闭了啦');
        return;
      }
      if (key == '/personal/downBigExcel') {
        let that = this;
        this.$confirm({
          content: '关闭我的下载页面，如有准备中的文件，将不会自动下载',
          onOk () {
            that.closeActivePage(key);
            let end = setInterval(function () {}, 5000);
            for (let i = 0; i <= end; i++) { // 关闭我的下载页面定时器
              clearInterval(i);
            }
          },
          cancelText: '取消',
          okText: '确定',
          onCancel () {

          }
        });
        return;
      }
      this.closeActivePage(key);
    },
    closeActivePage (key) {
      let removeRoute = this.pageList.filter(item => item.fullPath == key);
      this.pageList = this.pageList.filter(item => item.fullPath !== key);
      let index = this.linkList.indexOf(key);
      this.linkList = this.linkList.filter(item => item !== key);
      index = index >= this.linkList.length ? this.linkList.length - 1 : index;
      this.activePage = this.linkList[index];

      // 关闭页面则从缓存cache_included_routes中删除路由，下次点击菜单会重新加载页面
      let cacheRouterArray = Vue.ls.get(CACHE_INCLUDED_ROUTES) || [];
      if (removeRoute && removeRoute[0]) {
        let componentName = removeRoute[0].meta.componentName;
        if (cacheRouterArray.includes(componentName)) {
          cacheRouterArray.splice(cacheRouterArray.findIndex(item => item === componentName), 1);
          Vue.ls.set(CACHE_INCLUDED_ROUTES, cacheRouterArray);
        }
      }
    },
    onContextmenu (e) {
      const pagekey = this.getPageKey(e.target);
      if (pagekey !== null) {
        e.preventDefault();
        if (pagekey != '/solarCare') {
          this.menuVisible = true;
        }
      }
    },
    getPageKey (target, depth) {
      depth = depth || 0;
      if (depth > 2) {
        return null;
      }
      let pageKey = target.getAttribute('pagekey');
      pageKey = pageKey || (target.previousElementSibling ? target.previousElementSibling.getAttribute('pagekey') : null);
      return pageKey || (target.firstElementChild ? this.getPageKey(target.firstElementChild, ++depth) : null);
    },
    onMenuSelect (key, target) {
      let pageKey = this.getPageKey(target);
      switch (key) {
        case '1':
          this.closeLeft(pageKey);
          break;
        case '2':
          this.closeRight(pageKey);
          break;
        case '3':
          this.closeOthers(pageKey);
          break;
        case '4':
          this.routeReload();
          break;
        default:
          break;
      }
    },
    /* for: 关闭当前tab页，供子页面调用->望菜单能配置外链，直接弹出新页面而不是嵌入iframe #428 */
    closeCurrent () {
      this.remove(this.activePage);
    },
    closeOthers (pageKey) {
      let firstMenu = this.firstMenu;
      let index = this.linkList.indexOf(pageKey);
      if (pageKey == firstMenu.path || pageKey.indexOf('?ticke=') >= 0) {
        this.linkList = this.linkList.slice(index, index + 1);
        this.pageList = this.pageList.slice(index, index + 1);
        this.activePage = this.linkList[0];
      } else {
        let indexContent = this.pageList.slice(0, 1)[0];
        this.linkList = this.linkList.slice(index, index + 1);
        this.pageList = this.pageList.slice(index, index + 1);
        this.linkList.unshift(indexContent.fullPath);
        this.pageList.unshift(indexContent);
        this.activePage = this.linkList[1];
      }
    },
    closeLeft (pageKey) {
      let firstMenu = this.firstMenu;
      if (pageKey == firstMenu.path) {
        return;
      }
      let tempList = [...this.pageList];
      let indexContent = tempList.slice(0, 1)[0];
      let index = this.linkList.indexOf(pageKey);
      this.linkList = this.linkList.slice(index);
      this.pageList = this.pageList.slice(index);
      this.linkList.unshift(indexContent.fullPath);
      this.pageList.unshift(indexContent);
      if (this.linkList.indexOf(this.activePage) < 0) {
        this.activePage = this.linkList[0];
      }
    },
    closeRight (pageKey) {
      let index = this.linkList.indexOf(pageKey);
      this.linkList = this.linkList.slice(0, index + 1);
      this.pageList = this.pageList.slice(0, index + 1);
      if (this.linkList.indexOf(this.activePage < 0)) {
        this.activePage = this.linkList[this.linkList.length - 1];
      }
    },
    // 动态路由title显示配置的菜单title而不是其对应路由的title
    dynamicRouterShow (key, title) {
      let keyIndex = this.linkList.indexOf(key);
      if (keyIndex >= 0) {
        let currRouter = this.pageList[keyIndex];
        let meta = Object.assign({}, currRouter.meta, { title: title });
        this.pageList.splice(keyIndex, 1, Object.assign({}, currRouter, { meta: meta }));
        if (key === this.activePage) {
          this.changeTitle(title);
        }
      }
    },

    // 路由刷新
    routeReload (isDB) {
      if (!(this.$route.path == '/solarCare' && isDB)) {
        this.reloadFlag = false;
        let ToggleMultipage = 'ToggleMultipage';
        let isSub = false;
        this.$store.dispatch(ToggleMultipage, false);
        this.$nextTick(() => {
          if (isSub) {
            this.$router.replace('/solarCare');
          }
          this.$store.dispatch(ToggleMultipage, true);
          this.reloadFlag = true;
        });
      }
    },
    // 新增一个返回方法
    excuteCallback (callback) {
      callback();
    }
  },
  destroyed () {
    removeWatermark();
  }
};
</script>

<style lang="less">
  /*
 * The following styles are auto-applied to elements with
 * transition="page-transition" when their visibility is toggled
 * by Vue.js.
 *
 * You can easily play with the page transition by editing
 * these styles.
 */
  .main-parent{
    margin: 12px 12px 0 24px;
    width: calc(100% - 36px);
     height: calc(100vh - 121px);
    overflow: hidden auto;
    position: absolute;
    padding-left: calc(100vw - 100%);
  }

  .page-transition-enter {
    opacity: 0;
  }

  .page-transition-leave-active {
    opacity: 0;
  }

  .page-transition-enter .page-transition-container,
  .page-transition-leave-active .page-transition-container {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }

  /*美化弹出Tab样式*/
  // .ant-tabs-nav-container {
  //   margin-top: 4px;
  // }

  /* 修改 ant-tabs 样式 */
  .tab-layout-tabs.ant-tabs {
    //border-bottom: 1px solid #ccc;
   // border-left: 1px solid #ccc;
    background-color: white;
    padding: 0 20px;

    .ant-tabs-bar {
      //margin: 4px 0 0;
      border: none;
      margin: 0;
    }

  }

  .tab-layout-tabs.ant-tabs {

    &.ant-tabs-card .ant-tabs-tab {

      padding: 0 24px !important;
      background-color: white !important;
      margin-right: 10px !important;

      .ant-tabs-close-x {
        width: 12px !important;
        height: 12px !important;
        opacity: 0 !important;
        cursor: pointer !important;
        font-size: 12px !important;
        margin: 0 !important;
        position: absolute;
        top: 36%;
        right: 6px;
      }

      &:hover .ant-tabs-close-x {
        opacity: 1 !important;
      }

    }

  }

  .tab-layout-tabs.ant-tabs.ant-tabs-card > .ant-tabs-bar {
    .ant-tabs-tab {
      border: none !important;
      border-bottom: 1px solid transparent !important;
    }
    // .ant-tabs-tab-active {
    //   border-color: @primary-color!important;
    // }
  }

</style>
