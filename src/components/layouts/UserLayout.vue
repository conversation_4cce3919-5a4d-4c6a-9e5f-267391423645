<template>
  <div id="userLayout" :class="['user-layout-wrapper', device]">
    <div class="container">
      <div class="top">
         <!-- logo 放开 -->
        <img alt="login logo" src="@/assets/jhlg/login_logo.png" class="t_left" />
        <div class="t_right">
          <!-- 中国站<a-divider type="vertical" />简体中文 -->
          <a-dropdown style="margin-left: 32px" placement="bottomRight" class="login-drop">
            <a-button style="color:#222"> <a-icon type="download" style="color:#222" />移动应用下载</a-button>
            <a-menu slot="overlay">
              <a-menu-item>
                <img :src="codeUrl" width="260" height="260" />
              </a-menu-item>
            </a-menu>
          </a-dropdown>
          <div></div>
        </div>
      </div>
      <div class="middle">
      <img src="https://staticres.isolareye.com/image/public/login_left.png" alt="login left" class="m_left"/>
      <div class="m_right">
      <route-view></route-view></div>
      </div>
      <!-- <div class="bottom">
        <span
          >©2019-{{ year }}&nbsp;&nbsp;Sungrow版权所有 &nbsp;&nbsp;&nbsp;&nbsp;备案号：<a
            href="https://beian.miit.gov.cn/"
            class="record_number"
            target="_blank"
            >皖ICP备2021001620号-1</a
          ></span
        >
      </div> -->
    </div>
  </div>
</template>

<script>
import RouteView from '@/components/layouts/RouteView';
import { mixinDevice } from '@/utils/mixin.js';
const codeUrl = process.env.VUE_APP_API_CODE_URL;
export default {
  name: 'UserLayout',
  components: { RouteView },
  mixins: [mixinDevice],
  data () {
    return {
      codeUrl,
      year: new Date().getFullYear()
    };
  },
  mounted () {
    document.body.classList.add('userLayout');
  },
  beforeDestroy () {
    document.body.classList.remove('userLayout');
  }
};
</script>

<style lang="less" scoped>
#userLayout.user-layout-wrapper {
  height: 100%;

  &.mobile {
    .container {
      background-image: linear-gradient(270deg, #d5ecef 0%, #efeae1 100%);
      .left {
        display: none;
      }
      .top {
        display: flex;
        flex-direction: column;
        .top-divider {
          display: none;
        }
        .top-title {
          margin-top: 10px;
        }
      }
    }
  }
  .t_right {
    color: #222;
  }
  .t_left {
    height: 100%;
  }
  .container {
    width: 100%;
    min-height: 100%;
    background: #fff;
    overflow: hidden;
    position: relative;
    background-image: url('https://staticres.isolareye.com/image/public/login_bg.png');
    background-size:cover;
    background-position: center;
    height: 100vh;

    .top {
      position: absolute;
      top: 45px;
      padding:0 55px;
      height: 40px;
      display: flex;
      justify-content: space-between;
      width: 100%;
      .qr-code {
        width: 260px;
        height: 260px;
      }
    }
    .middle {
     position: absolute;
     top: calc((100% - 520px)/2);
      width: 880px;
      left: calc((100% - 800px)/2);
      height:520px;
      display:flex;
      background: #fff;
      border-radius: 10px;
      box-shadow: 0px 12px 40px 0px rgba(0, 0, 0, 0.1);
      .m_left {
        width: 50%;
        height: 100%;
        object-fit:cover;
        border-radius: 10px 0 0 10px;

      }
      .m_right {
        width: 50%;
        display: flex;
        flex-direction:column;
        align-items: center;
        .main {
          width: 100%;
          height: 100%;
        }
      }
    }
     @media screen and (max-width: 1280px) {
        .middle {
          top: 25%;
          width: 50%;
          left: 25%;
          height:50%;
        }
    }
    .bottom {
      height: 16px;
      position: absolute;
      bottom: 32px;
      display: flex;
      justify-content: center;
      width: 100%;
      font-size: 12px;
      font-weight: normal;
      line-height: 16px;
      text-align: center;
      letter-spacing: 0px;
      color: #666666;
      .record_number {
        color: #666666;
          &:hover {
            color: #666666;
          }
        }
    }
  }
  :deep(.ant-divider) {
    background: #222;
    opacity: 0.3;
    &.ant-divider-vertical {
      margin: 0 16px;
     }
  }
}
:deep(.ant-dropdown-menu-item) {
  &:hover {
    background: transparent;
  }
}
</style>
