<template>
  <a-spin :spinning="loading" size="large">
    <iframe :id="id" :src="url" ref="testPage" frameborder="0" width="100%" height="800px" scrolling="auto"></iframe>
  </a-spin>
</template>

<script>
// import Vue from 'vue'
import { ACCESS_TOKEN } from '@/store/mutation-types';
import { postAction } from '@/api/manage';
import {
  mixin,
  mixinDevice
} from '@/utils/mixin.js';
export default {
  name: 'IframeView',
  inject: ['closeCurrent'],
  data () {
    return {
      url: '',
      id: '',
      loading: true,
      iframeWin: {},
      user: {},
      perms: []
    };
  },
  props: {
    path: {
      type: String,
      default: ''
    }
  },
  mixins: [mixin, mixinDevice],
  created () {
    this.goUrl();
  },
  updated () {
    // this.goUrl();
  },
  mounted () {
    let that = this;
    const { testPage } = this.$refs;

    this.$nextTick(() => {
      if (document.getElementsByClassName('iot-faullts').length > 0) {
        document.getElementsByClassName('iot-faullts')[0].style.opacity = 0;
      }
      testPage.onload = function () {
        that.loading = false;
      };
    });
  },
  watch: {
    $route (to, from) {
      this.goUrl();
    }
  },
  methods: {
    goUrl () {
      let url = ''; let internalOrExternal = '';
      if (!this.path) {
        url = this.$route.meta.url;
        this.id = this.$route.path;
        internalOrExternal = this.$route.meta.internalOrExternal;
      } else {
        url = this.path.meta.url;
        this.id = this.path.path;
        internalOrExternal = this.path.meta.internalOrExternal;
      }
      // 'https://gateway.isolarcloud.com/v1/userService/login'
      postAction('https://gateway.isolarcloud.com/v1/userService/login', {
        user_account: 'DataReport',
        user_password: 'Zw20200304!IsolarERP',
        login_type: 1,
        appkey: '7D233083E6C376543C36D329100BB473',
        sys_code: '901'

      }).then(res => {
        // https://portal.isolarcloud.com/#/senior/ivAnalyse?token=89329_2d4266c704454a078aef05e91f603b75&lang=zh_CN&microGateWayUrl=https://gateway.isolarcloud.com&noMenu=1&apiUrl=https://api.isolarcloud.com&baseUrl=https://www.isolarcloud.com&cloudId=1
        let token = res.result_data.token;
        let params = '&lang=zh_CN&microGateWayUrl=https://gateway.isolarcloud.com&noMenu=1&apiUrl=https://api.isolarcloud.com&baseUrl=https://www.isolarcloud.com&cloudId=1&versionNum=2022-08-15T17:27:42.791Z';
        url = this.$route.meta.url + '#/senior/ivAnalyse' + '?token=' + token + params;
        this.id = this.$route.path;
        if (url !== null && url !== undefined) {
          this.url = url;
          if (internalOrExternal != undefined && internalOrExternal == true) {
            if (!this.path) {
              this.closeCurrent();
            } else {
              this.$emit('close');
            }
            // 外部url加入token
            let tokenStr = `${token}`;
            if (url.indexOf(tokenStr) === -1) {
              let token = Vue.ls.get(ACCESS_TOKEN);
              this.url = url.replace(tokenStr, token);
            }
            window.open(this.url);
          }
        }
      });
    }
  }
};
</script>
