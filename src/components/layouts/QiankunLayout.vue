<template>
  <div>
 <router-view />
     <main id="subapp-container">
       <div id="subapp-viewport"></div>
     </main>
  </div>
</template>

<script>
export default {
  name: 'QiankunLayout',
  data () {
    return {

    };
  },
  computed: {

  },
  beforeRouteEnter (to, from, next) {
    next(vm => {
      vm.isLoadedJs('dll/jspdf.js');
    });
  },
  created () {

  },
  mounted () {
    setTimeout(() => {
      if (process.env.VUE_APP_QIANKUN == 'true') {
        // 注册子应用
        if (!window.qiankunStarted) {
          window.qiankunStarted = true;
          let start = require('@/qiankun');
          start.default();
        }
        // 注册子应用
      }
    }, 500);
  }
};
</script>

<style lang="less">

  /*
 * The following styles are auto-applied to elements with
 * transition="page-transition" when their visibility is toggled
 * by Vue.js.
 *
 * You can easily play with the page transition by editing
 * these styles.
 */

  .page-transition-enter {
    opacity: 0;
  }

  .page-transition-leave-active {
    opacity: 0;
  }

  .page-transition-enter .page-transition-container,
  .page-transition-leave-active .page-transition-container {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
</style>
