<?xml version="1.0" encoding="UTF-8"?>
<svg width="30px" height="3px" viewBox="0 0 30 3" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>标签选中</title>
    <g id="=" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="360监控" transform="translate(-32.000000, -98.000000)">
            <g id="路径" transform="translate(0.000000, 60.000000)">
                <polygon id="标签选中" points="34 38 62 38 60 41 32 41"></polygon>
            </g>
        </g>
    </g>
</svg>