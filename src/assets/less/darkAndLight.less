.colorize_bg(@color: @white, @alpha: 1) {
    background: hsla(hue(@color), saturation(@color), lightness(@color), @alpha) !important;
}

.white-color {
    color: white;
}

.grey-color {
    color: #D0D2D6;
}

.dark-grey-color {
    color: #333333;
}

.solar-eye-bg {
    background: linear-gradient(180deg, #122234 0%, #283C58 100%) !important;
    box-shadow: 5px 4px 9px 0px rgba(3, 18, 38, 0.5);
}

@solar-eye-blue: #267DCF;
.font-size(@fontSize: 14px) {
    font-size :@fontSize;
}
.solar-eye-dark:not(.userLayout) {
    @undo-bg: #F6504D;
    @doing-bg: #D49B6A;
    @done-bg: #00A4AC;
    @stop-bg: #7D7E84;
  .gantt-container {
    .gantt_grid_data .gantt_row.gantt_selected,
    .gantt_grid_data .gantt_row.odd.gantt_selected,
    .gantt_task_row.gantt_selected {
      background-color: #1a273b;
    }

    .gantt_task_row.gantt_selected .gantt_task_cell {
      border-right-color: #2d2d40;
    }
    .gantt_grid_data .gantt_row.odd:hover,
    .gantt_grid_data .gantt_row:hover {
      background-color: #1a273b;
    }
    .gantt_row[aria-level='0'] {
        background: #222A36;
        border-radius: 4px;
    }
    
    .gantt_row,
    .gantt_task_row {
      // border-bottom: 1px solid #7e7e7e;
      border-bottom: none;
      background-color: #172130;
    }
    .gantt_data_area,
    .gantt_grid_data {
      background-color: #172130;
    }
    .gantt_grid_data .gantt_cell {
      border-right: none;
      color: #fff;
    }
    .gantt_task_cell {
      display: inline-block;
      height: 100%;
      border-right: 1px solid #2d3747;
    }
    .gantt_layout_cell_border_right {
      border: none;
      border-right: 1px solid #2d3747;
      background-color: #2d3747;
    }
    .gantt_grid_scale,
    .gantt_task_scale,
    .gantt_task_vscroll {
      background-color: #0d1725;
    }
    .gantt_grid_scale .gantt_grid_head_cell {
      color: #ffffff;
      border-top: none !important;
      border-right: none !important;
    }
    .gantt_grid_scale,
    .gantt_task_scale {
      color: #fff;
      font-size: 14px;
      // border-bottom: 1px solid #ff0;
      box-sizing: border-box;
    }
    .wl-progress {
      color: #fff;
    }
    .milestone-doing {
      border: none;
      background-color: @doing-bg;
    }

    .milestone-done {
      border: none;
      background-color: @done-bg;
    }

   .milestone-undo {
      border: none;
      background-color: @undo-bg;
    }
    .milestone-stop {
        border: none;
        background-color: @stop-bg;
    }
    .gantt_tree_icon.gantt_folder_open {
      display: none;
    }

    .gantt_tree_icon.gantt_file {
      display: none;
    }

    .gantt_tree_icon.gantt_folder_closed {
      display: none;
    }

    .gantt_tree_icon.gantt_close {
      background-image: url('../images/workPortal/arrow_dark.png');
    }

    .gantt_tree_icon.gantt_open {
      background-image: url('../images/workPortal/down_dark.png');
    }

    .custom-project {
      // text-align: center;
      .project-content::before {
        content: '';
        position: absolute;
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        // border-top: 10px solid @doing-bg;
        border-top-width: 10px;
        border-top-style: solid;
        left: -10px;
        top: 0;
      }

      .project-content::after {
        content: '';
        position: absolute;
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        // border-top: 10px solid @doing-bg;
        border-top-width: 10px;
        border-top-style: solid;
        right: -10px;
        top: 0;
      }

      @set: {
        bg-undo: @undo-bg;
        bg-done: @done-bg;
        bg-doing: @doing-bg;
        bg-stop: @stop-bg;
      };

      each(@set, {
        .project-@{key}::before {
          border-top-color: @value;
        }
  
        .project-@{key}::after {
          border-top-color: @value;
        }
      });
    }
    .gantt_tree_content {
      overflow: hidden;
      text-overflow: ellipsis;
      align-items: center;
      display: flex;

      .progress-num {
        padding-left: 10px;
      }

    }
    .gantt_task .gantt_task_scale .gantt_scale_cell {
      color: #8493a1;
      // border-right: 1px solid #2d3747;
      border-right: none;
    }
    .today {
      background: transparent;
      border: 1px dashed #60cafe;
    }
    .gantt_grid_scale,
    .gantt_task_scale {
      border-bottom: none;
    }
    .gantt_scale_line {
      border: none;
    }
  }
        
    background-image: url(../images/public/bg.png);
    &.webpa { 
        background-image: url(../images/public/bg.webp);
    }
    background-size: cover;
    .white-color();

    .ant-layout {
        background: transparent;
    }

    :deep(.ant-calendar-menus) {
        background: rgba(124, 156, 200, 0.18);
        box-shadow: 0px 1px 0px 0px rgba(124, 156, 200, 0.35);

        .ant-cascader-menus-content {
            background: transparent;
        }

        .ant-cascader-menu-item:hover {
            background: #2A3E5B;
        }

    }

    .ant-alert-message {
        color: white
    }

    .tab-layout-tabs.ant-tabs {
        //border: 1px solid #33455F;
        background: rgba(124, 156, 200, 0.18);
        box-shadow: 0px 1px 0px 0px rgba(124, 156, 200, 0.35);

        .ant-tabs-bar {
            margin: 0;
        }
    }

    .ant-tabs.ant-tabs-card {

        &>.ant-tabs-bar .ant-tabs-tab,
        &>.ant-tabs-bar .ant-tabs-tab-active {
            background: transparent !important;
            .white-color();
            border-color: transparent !important;
        }

        .ant-tabs-card-bar {
            .ant-tabs-ink-bar {
                background: url(../images/public/tab-select.png) no-repeat;
                background-size: cover !important;
                bottom: 2px;
            }

            .ant-tabs-close-x {
                color: #efefef;

                &:hover {
                    .white-color();
                }
            }
        }
    }
    .ant-menu:not(.ant-menu-horizontal) {
        .ant-menu-item-selected {
            background-color: transparent;
        }
    }

    .menu-manage-menus {
        color: white;
        .ant-menu-item-selected {
            background-color: rgba(78, 121, 177, 0.2) !important;
        }
    }

    section.ant-layout .top-nav-header-index .header-index-wide .header-index-left {
        background: transparent;

        // .logo.top-nav-header {
        //     background: transparent;
        // }
    }

    .dark.header-index-right {
        background: transparent;
    }

    .ant-breadcrumb,
    .ant-breadcrumb span:last-child {
        .white-color();
    }

    // pagination start
    .ant-pagination-item:not(.ant-pagination-item-active) a,
    .ant-pagination-prev,
    .ant-pagination-total-text,
    .ant-pagination-next,
    .ant-pagination-options-quick-jumper,
    .ant-pagination-options-quick-jumper input,
    .ant-pagination-jump-prev .ant-pagination-item-container .ant-pagination-item-ellipsis,
    .ant-pagination-jump-next .ant-pagination-item-container .ant-pagination-item-ellipsis {
        .white-color();
    }

    .page-pagination,
    .ant-pagination-item,
    .ant-pagination-options-quick-jumper input {
        background: transparent;
    }

    // pagination end
    .ant-modal-header .ant-modal-title,
    .ant-modal-confirm-body .ant-modal-confirm-content,
    .ant-modal-confirm-title {
        .white-color();
    }

    .ant-form,
    .ant-form-item-label label,
    .ant-form-item-children,
    .ant-upload-list-item-name {
        .white-color();
    }
    .ant-input-number,
    .ant-input-affix-wrapper .ant-input-prefix,
    .ant-input-affix-wrapper .ant-input-suffix,
    .ant-input-number-disabled,
    .ant-input-suffix {
        .white-color();
    }

    .data-header,
    .data-header .info-text,
    .data-header .info-num,
    .ant-select-arrow,
    .chart-card-header-title,
    .solar-eye-seal,
    .search-item-label,
    .anticon,
    .ant-calendar-picker-icon,
    .ztree li a,
    .ant-select-dropdown-menu-item-group-title,
    .setting-drawer-index-title,
    .ant-list-item-meta-title div,
    .ant-cascader-picker,
    .ant-checkbox-wrapper,
    .ant-tabs-tab,
    .ant-drawer-title,
    .search-box .text,
    .ant-calendar-range-picker-separator,
    .budget-analysis-top-item .item-bottom,
    .ant-alert-info,
    .ant-table-placeholder .ant-empty-normal,
    .search-item,
    .solar-eye-btn-grey,
    .title-label label,
    .ant-timeline-item-content,
    .ant-form-item,
    .ant-select-auto-complete.ant-select .ant-input[disabled] {
        .white-color();
    }
    .ant-message-success .anticon{
      color: #52c41a;
    }
    .ant-message-warning .anticon{
      color: #faad14;
    }
    .ant-message-error .anticon{
      color: #f5222d;
    }
    .ant-message-info .anticon{
      color: #267DCF;
    }
    .ant-pagination-prev .ant-pagination-item-link, .ant-pagination-next .ant-pagination-item-link {
        background-color: transparent;
    }
    .dark-black-color {
      color: black;
    }

    .search-header,
    .inverter-list-search-header,
    .inverter-list-chart-area,
    .monitor-list-area,
    .ant-alert-info,
    .solar-eye-btn-grey,
    .solar-search,
    .menu-manage-menus {
        background: rgba(78, 121, 177, 0.1) !important;
    }

    .solar-eye-pure-bg,
    .solar-eye-main-content,
    .table-area {
        background: rgba(78, 121, 177, 0.2) !important;
    }

    .solar-eye-pure-bg-16 {
        background: rgba(82, 102, 129, 0.16) !important;
    }

    .solar-eye-pure-bg-30 {
        background: #51637C !important;
    }

    .ant-select-selection,
    .ant-input,
    .double-input,
    .ant-cascader-picker,
    .ant-time-picker-input,
    .ant-input-number {
        background: rgba(27, 34, 44, 0.3);
        border-radius: 2px;
        border: 1px solid #51637C !important;
        .white-color();
    }

    .ant-input-number.rang-input-right,
    .ant-input-number.rang-input-left {
        border: none !important;

    }

    .ant-select-disabled .ant-select-selection,
    .ant-input-disabled,
    .ant-input-number-disabled,
    .readonly-disable input:read-only:not(.ant-calendar-range-picker-input),
    .readonly-disable textarea:read-only,
    .ant-select-auto-complete.ant-select .ant-input[disabled] {
        background: #2f3d53;
    }

    .ant-select-disabled .ant-select-selection:hover,
    .ant-select-disabled .ant-select-selection:focus,
    .ant-select-disabled .ant-select-selection:active {
        border-color: #192c3e;
        box-shadow: none;
    }

    .ant-select-disabled .ant-select-selection--multiple .ant-select-selection__choice {
        background: #2f3d53;
        .white-color()
    }

    .solar-eye-btn-primary {
        &:not(.ant-btn-icon-only) {
            border: 1px solid #60CAFE;
            background: linear-gradient(90deg, #2F7EB6 0%, #20B0DF 100%);
        }

        &:hover {
            color: white !important;
        }

        .white-color();

        &:hover {
            .white-color();
        }
    }
    .ant-radio-disabled + span {
        color: white;
      }
    .solar-eye-btn-primary[disabled] {
        color: #cfcfcf;
    }
    .ant-steps-item-title {
        color:  white !important;
    }
    // ant table start
    .ant-table {
        color: white;
        background: transparent;

        .ant-table-fixed,
        .ant-table-tbody .ant-table-row-hover>td,
        .ant-table-content .ant-table-header,
        .ant-table-small>.ant-table-content .ant-table-header {
            background: rgba(66, 74, 85, 0.15) !important;
        }
        
        .ant-table-row:nth-child(even),
        .ant-table-thead>tr>th,
        .ant-table-fixed-right table,
        .ant-table-tbody>tr.ant-table-row-selected td,
        .ant-table-tbody>tr:hover td {
            background: rgba(66, 74, 85, 0.1) !important;
        }

        .ant-table-thead>tr>th {
            color: white !important;
        }
    }
    .ant-table-small.ant-table-bordered .ant-table-content {
        border-right: 1px solid transparent !important;
    }
    .ant-table-fixed-header .ant-table-scroll .ant-table-header {
        overflow: auto;
    }
    // vxe table start
    .vxe-table {
        color: white;
        background: transparent;

        .vxe-table--header-wrapper,
        .vxe-body--row:hover,
        .vxe-body--row.row--current,
        .vxe-body--row.row--hover,
        .vxe-body--column.col-grey {
            background: rgba(66, 74, 85, 0.15) !important;
        }

        .vxe-body--row:nth-child(even),
        .vxe-header--row {
            background: rgba(66, 74, 85, 0.1) !important;
        }

        .vxe-table--fixed-left-wrapper,
        .vxe-table--fixed-right-wrapper {
            background: #1B2536 !important;
        }
    }

    .vxe-table.border--default .vxe-table--header-wrapper,
    .vxe-table.border--full .vxe-table--header-wrapper,
    .vxe-table.border--outer .vxe-table--header-wrapper {
        background-color: none !important;
    }

    .vxe-table.border--full .vxe-body--column,
    .vxe-table.border--full .vxe-footer--column,
    .vxe-table.border--default .vxe-table--header-wrapper .vxe-header--row:last-child .vxe-header--gutter,
    .vxe-table.border--full .vxe-table--header-wrapper .vxe-header--row:last-child .vxe-header--gutter,
    .vxe-table.border--inner .vxe-table--header-wrapper .vxe-header--row:last-child .vxe-header--gutter,
    .vxe-table.border--outer .vxe-table--header-wrapper .vxe-header--row:last-child .vxe-header--gutter {
        background-image: none !important;
    }

    .vxe-table.border--full .vxe-header--column {
        background-image: linear-gradient(#3D506D, #3D506D) !important;
    }

    // vxe table end
    .run-monitor {

        .main-content-chart .chart-card {
            .chart-card-header-title-read {
                color: #ffffff80 !important;
            }
        }

        .search-label,
        .rang-input-label {
            .white-color()
        }


        .chart-data-text {
            .white-color();

            .left {
                background: url('../../assets/images/monitor/indicators_bg.png');
            }
        }

        .warning-info-item {
            background: linear-gradient(270deg, rgba(58, 74, 95, 0) 0%, #3A4A5F 98%);
            color: #D0D2D6;

            span {
                .white-color();
            }
        }
    }

    // 监控中心和洞察工具共用的排序
    .sort-header-item {
        border: 1px solid rgba(147, 169, 199, 0.28);

        .sort-header-item-text,
        .sort-header-item-icon {
            color: #ABB9C4;
        }
    }

    .sort-header-item-active,
    .sort-header-item:hover {
        border: 1px solid @solar-eye-blue  !important;
        background: rgba(38, 125, 207, 0.2) !important;
    }

    .sort-header-item-active,
    .sort-header-item:hover {

        .sort-header-item-text,
        .sort-header-item-icon {
            .white-color()
        }
    }

    // dropdown start 
    .ant-dropdown-menu,
    .ant-select-dropdown,
    .ant-cascader-menus-content {
        .solar-eye-bg();
        border: 1px solid #51637C;

        &::before {
            border-bottom: 10px solid #51637C;
        }

        &::after {

            border-bottom: 10px solid #122234;
        }

        .ant-dropdown-menu-item,
        .ant-select-dropdown-menu-item,
        .ant-dropdown-menu-item>a,
        .ant-dropdown-menu-submenu-title>a {
            color: white !important;

        }

        .ant-select-dropdown-menu-item-selected,
        .ant-select-dropdown-menu-item:hover,
        .ant-dropdown-menu-item:hover,
        .ant-select-dropdown-menu-item-active,
        .ant-cascader-menu-item:hover,
        .ant-cascader-menu-item-active {
            background: #2A3E5B;
        }
    }

    .ant-cascader-menu {
        border-right-color: #3A4A5F;
    }

    .ant-drawer-content .btn {
        border-top: 1px solid #3A4A5F;
    }

    .save-btns {
        border-bottom: 1px solid #3A4A5F;
    }

    .ant-drawer-content,
    .ant-modal-content,
    .ant-table-placeholder,
    .ant-upload.ant-upload-select-picture-card {
        .solar-eye-bg()
    }

    .ant-drawer-content .drawer-form-com {
        background: transparent;
    }

    .point5-line4 {
        .point-out {

            background: url('../../assets/images/monitor/device_work.png');

        }

        .point-out-active {
            background: url('../../assets/images/monitor/device_not_work.png');
        }
    }

    input {
        caret-color: #fff;
    }

    .data-header {
        .info-data {
            background: rgba(78, 121, 177, 0.15);
        }
    }

    .solar-eye-seal-div {
        .svg-icon {
            color: #E60012
        }

        .solar-eye-seal-name {
            color: #883737;
        }
    }

    .warning-drawer-header {
        background: transparent;
        border-bottom: none;

        .right-icon-border,
        .monitor-footer-icon-border,
        .realtime-warn,
        .record-info,
        .right-info,
        .right-icon,
        .svg-icon {
            color: white;
        }

        .record-num {
            color: #267DCF;
        }

    }

    .monitor-footer {
        background: #0D1625;

        .monitor-footer-icon {
            color: white;
        }
    }

    .solayeye__table-search-select--item {
        background: #93A9C7;
        color: white;
        border: 1px solid rgba(147, 169, 199, 0.28);
    }

    .solayeye__table-search-select--item-active {
        background: rgba(38, 125, 207, 0.2);
        .white-color();
        border: 1px solid #267DCF;
    }

    .solareye-color-primary+.svg-icon {
        color: white !important;
    }



    .screen-action {
        // background: #263851;
        color: #ACBAC5;
    }

    .solar-eye-search-header {
        background: #1B2536;
        .tab-item span {
            .white-color()
        }
    }
    .item_box .text {
        color: white;
    }
    .car-table,
    .footer-table,
    .tab-content .ant-tabs-bar,
    .search-model,
    .homePageManage,
    .ant-tabs-top-bar:not(.ant-tabs-card-bar),
    .count-card,
    .alarmEvents .input_box,
    .radiusBox,
    .drawer-form-com {
        background: rgba(78, 121, 177, 0.2);
    }
    .solar-eye-search-model {
        background: #1B2536;
        .solar-eye-search-content {
            background: rgba(78, 121, 177, 0.2);
        }
    }
    

    .drawer-box {
        .ant-drawer-body {
            background: transparent;
        }

        .drawer-foot {
            border-top: 1px solid transparent;
        }
    }

    .solar-eye-drawer-body {
        background: transparent;
        border: '1px solid #e9e9e9';
    }

    .ant-drawer-header,
    .drawer-form-foot,
    .ant-modal-header {
        background: transparent;
    }

    .ant-drawer-header {
        border-bottom: 1px solid #354661;
    }

    .drawer-form-foot {
        border-top: 1px solid #354661;
    }

    .manage-left-menu {
        .right-icon {
            background: transparent;
        }

        .menu-manage-icon {
            background: #267DCF;
        }
    }

    .search-card {
        background: rgba(74, 94, 121, 0.1) !important;
    }

    .run-monitor .main-content-chart .chart-card,
    .power-station-card {
        background: rgba(78, 121, 177, 0.15);
        border: 1px solid #51637C;

        &:hover {
            border: 1px solid #00B7EE;
            box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.5), 0px 0px 17px 6px rgba(13, 137, 255, 0.06) inset;
        }

        .chart-card-header-title-read {
            color: #ffffff80 !important;
        }
    }

    .ant-card {
        background: rgba(78, 121, 177, 0.2) !important;
        border-color: transparent !important;
    }
  // 列表操作按钮悬停效果
  .operation-btn-hover:hover{
    .anticon {
      color: #FF8F33 !important;
    }
  }
    .power-station-card {
        &-top {
            background: rgba(78, 121, 177, 0.16);
            ;
            .white-color();
        }

        .power-station-mark {
            color: white;
        }

        &-bottom {
            .devide-line {
                border: 1px dashed #D6D6D6;

                &-left {
                    color: #9B9B9B;
                }

                .num {
                    color: rgba(0, 0, 0, 0.85);
                }
            }
        }
    }

    .chart-eg-item {
        span {
            color: white;
        }
    }
    .work-portal-lists{
      .list-item-time {
        color: #fff;
      }
      .list-item-border{
        border-color: #364457 !important;
      }
      .list-item-title{
        color: #fff;
      }
      .work-portal-list-filter{
        background: rgba(22, 30, 45, 1);
      }
    }
    .work-portal-handle-head {
      background: #293a54;

      .more {
        color: white;
      }
    }
    .work-portal{
      background: rgba(22, 30, 45, 1);

      .dragge-place, .homepage-main-link    {
        color: #FFFFFF !important;
      }
    }
    .solar-eye-search-wrapper {
        background: rgba(78, 121, 177, 0.16);

        .search-wrapper-left {
            a {
                color: white;

                &.active {
                    background: url(../images/public/tab-select.png) no-repeat bottom;
                }

            }

            .space-item {
                .white-color();
                margin-left: 32px;
            }
        }

        .sort-header-item {
            color: #ABB9C4;
        }
    }
    .sort-header {
        .desc {
            color: white;
            .desc-color {
                color: #AB4C2D;
            }
        }
    }
    .echart-group .left-chart,
    .echart-group .right-chart,
    .budget-analysis-top-item,
    .ant-input-group-addon {
        background: rgba(78, 121, 177, 0.1)
    }

    .echart-group {
        .right-chart {
            .left-space {
                .ant-space-item {
                    color: white !important;
                }
            }
        }

        .full-screen {
            background: rgba(78, 121, 177, 0.25);
        }
    }

    .anticon-fullscreen,
    .anticon-fullscreen-exit,
    .anticon-vertical-align-bottom {
        // background: rgba(78, 121, 177, 0.25);
        color: #cfcfcf;
    }

    .echart-table {
        background: transparent;
    }

    .big-show {
        background: rgba(24, 34, 48);
    }

    .solareye-drawer-left {
        border: 1px solid #354661;
    }

    .drawer-right-content {
        .ant-btn {
            background: rgba(78, 121, 177, 0.1) !important;
        }
    }

    .budget-analysis-top-item {
        .item-top {
            border-bottom: 1px solid #354661;
        }

        .solar-eye-space {
            .ant-space-item {
                .white-color()
            }
        }

        .right-content {
            background: transparent !important;
            box-shadow: none !important;

            .title,
            .data {
                .white-color();
            }
        }
    }

    .ant-input-number-handler-wrap:hover .ant-input-number-handler {
        background: #0D1625;
        color: white;
    }

    .ant-radio-button-wrapper {
        background: rgba(78, 121, 177, 0.1);
        color: white;
        border: 1px solid rgba(147, 169, 199, 0.28);

        &.ant-radio-button-wrapper-checked {
            border-left: none;
            background: rgba(38, 125, 207, 0.2);
            border: 1px solid #267DCF;
            box-shadow: none;
            color: white;
        }
    }

    .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
        background: rgba(38, 125, 207, 0.2);
        border: 1px solid #267DCF;
        box-shadow: none;
        color: white;

    }

    .ant-radio-button-wrapper:not(:first-child)::before {
        background-color: #267DCF;
    }

    .anomaly-model {
        .anomaly-table {
            background: rgba(24, 34, 48);
        }

        .anomaly-icon {
            background: url('../images/budget_analysis/abnormal_analysis_bg_dark.png');

            &::before {
                content: url('../images/budget_analysis/abnormal_dark.png');
            }
        }

        .anomaly-table-top,
        .anomaly-icon {
            color: white;
        }
    }

    .flow-chart-btn {
        height: 130px;
        background: url('../images/budget_analysis/abnormal_analysis_bg_dark.png') no-repeat;
    }

    .solaryeye-menu {
        .menu-title {
            .white-color();

            .solar-eye-arrow-down {
                .white-color();
            }
        }

        .menu-content {
            border-bottom: 2px dashed #2A3E5B;
        }
    }

    .drawer-bootom-button {
        border-top: 1px solid #354661;
        background: transparent;
    }
  
    ul.icon-ul {
        background-color: transparent;
    }

    .protal-tabs {
        .ant-tabs-bar {
            .solar-eye-bg();
        }
    }

    :deep(.ant-tree) {
        &:not(.main-tree) {
            background: rgba(78, 121, 177, 0.1);
        }

        li .ant-tree-node-content-wrapper {
            .white-color();
        }

        .ant-tree-treenode-switcher-close:hover,
        .ant-tree-node-content-wrapper:hover,
        li .ant-tree-node-content-wrapper.ant-tree-node-selected {
            background: rgba(78, 121, 177, 0.20) !important;
        }
    }

    .com-project-tree {
        background: rgba(78, 121, 177, 0.1);

        .solar-eye-tree {
            background: transparent;
        }
    }

    .isolar-layout {
        .search-box {
            background: transparent;
        }
    }

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
        color: white !important;
    }

    .solar-eye-btn-grey {
        background: #f0f0f0;
    }

    .solar-eye-tooptip {
        background-color: #1f334e !important;
        border-color: #51637C !important;
        color: white !important;

        div,
        span {
            color: white !important;
        }
        

    }

    .solar-eye-deep-tool,
    .materials-management-page {
        .middle.middle-btn-expand {
          cursor: pointer;
          background: #1C2A40;
            box-shadow: 0px 1px 5px 0px rgba(0, 0, 0, 0.07);
            border: 1px solid rgba(255, 255, 255, 0.65);
            border-left: none;
            .middle-btn {
                transform: rotate(180deg);
            }
        }

        .middle-btn {
            //  background: linear-gradient(270deg, #48637D 0%, rgba(78, 121, 177, 0.09) 100%);
            color: white;
        }
    }

    .ant-tree-checkbox:not(.ant-tree-checkbox-checked) .ant-tree-checkbox-inner,
    .ztree .button.checkbox_false_full, .button.checkbox_false_part,
    .vxe-table .vxe-cell--checkbox .vxe-checkbox--unchecked-icon:before,
    .ant-checkbox:not(.ant-checkbox-checked) .ant-checkbox-inner,
    .ant-checkbox.ant-checkbox-indeterminate .ant-checkbox-inner {
        border: 1px solid #51637C !important;
        background: rgba(27, 34, 44, 0.3) !important;
    }

    .ant-select-selection--multiple .ant-select-selection__choice {
        background: rgba(38, 125, 207, 0.2);
        border: 1px solid #267DCF;
        color: white;
    }

    .vxe-table.border--full .vxe-table--fixed-left-wrapper {
        border-right: none;
    }

    .ant-select-selection__clear {
        background: rgba(38, 125, 207, 0.2) !important;
        color: white;
    }

    ::-webkit-scrollbar-thumb {
        background: rgba(78, 121, 177, 0.2);
    }

    ::-webkit-scrollbar-thumb:hover {
        background: rgba(78, 121, 177, 0.3);
    }

    ::-webkit-scrollbar-corner {
        background: rgba(78, 121, 177, 0.1);
    }

    .share-content-title {
        background: transparent;
        color: white;
    }

    .ant-checkbox-disabled+span {
        color: white !important;
    }

    .share-content {
        border: 1px solid #2a4360;
    }

    .icon-1 {
        background: #F56C6C;
    }

    .icon-2 {
        background: #E39756;
    }

    .icon-3 {
        background: #44BE9B;
    }

    .icon-4 {
        background: #6399F9;
    }

    /* zhanbinbin 主题检查追加 */
    .isolar-layout .right {
        background: transparent;
    }

    .show-elli {
        color: #f0f0f0;
    }

    .ant-tree-treenode-selected .show-elli {
        color: #f0f0f0;
    }

    .show-elli:hover {
        color: #fff;
    }

    .isolar-page .ant-btn {
        background: #0D1625;
        border: 1px solid #51637C !important;
    }

    .ant-card-head-title,
    .ant-card {
        .white-color() !important;
    }

    .ant-tree-title {
        .white-color();
    }
    .has-footer-method .vxe-table--body-wrapper::-webkit-scrollbar {
        height: 0;
    }

    .ant-table-hide-scrollbar::-webkit-scrollbar {
        height: 0;
    }

    .color_bg {
        background: #0D1625;
    }

    .com-color {
        .white-color();
    }

    .com_transparent {
        background: transparent;
    }

    .ant-radio-wrapper {
        .white-color();
    }

    .ant-select-tree li .ant-select-tree-node-content-wrapper {
        .white-color();
    }

    .ant-divider-inner-text {
        .white-color();
    }

    .ant-collapse {
        border: 1px solid #354661 !important;
    }

    .ant-collapse-item {
        border-bottom: 1px solid #354661 !important;
    }

    .ant-collapse-header {
        .com-color() !important;
        border-bottom: 1px solid #354661 !important;
        background: #273a56;
    }

    .ant-collapse-content {
        border-top: 1px solid #354661 !important;
        background: #273a56;
    }

    // .health-base-param {
    //     background-color: transparent;

    //     :deep(.form-item-inject) {
    //         label {
    //             color: white;
    //         }
    //     }
    // }

    .ant-card-head {
        border-bottom: 1px solid #2a4360 !important;
    }

    .no-data-text,
    .no-more-data,
    .solar-eye-active {
        .white-color()
    }

    .operation-icon {
        color: #C2C2C2;
        cursor: pointer;
        .font-size(20px)
    }
    .operation-icon:hover {
        color: #1890FF;
    }

    .solar-eye-drawer-bg {
        .ant-drawer-content {
            background: #122234;
        }
    }

    .point-content,
    .device-content {
        background: rgba(78, 121, 177, 0.2) !important;
        .point-title {
            .white-color()
        }
    }

    .device-content {
        border-top: 1px solid rgba(53, 70, 97, 0.6);
    }

    .chart-content {
        color: white;

        .weather-info {
            background: rgba(78, 121, 177, 0.24);
        }
    }

    .solar-eye-btn-primary-cancel {
        background: rgba(27, 34, 44, 0.3) !important;
        border: 1px solid #51637C !important;
        color: white;

        &:hover {
            color: white;
        }
    }

    .ant-timeline-item-tail {
        border-left-color: #343B48;
    }

    .solar-eye-flow-bg {
        background-image: linear-gradient(#EAF0F7, #DFE6EF) !important;
        ;
    }

    .ant-popover-inner {
        background: #22354e !important;

        .ant-popover-inner-content,
        .ant-popover-message {
            color: white;
        }

    }

    .ant-table-bordered .ant-table-header>table,
    .ant-table-bordered .ant-table-body>table,
    .ant-table-bordered .ant-table-fixed-left table,
    .ant-table-bordered .ant-table-fixed-right table {
        border: none;
    }

    .solar-eye-border-bottom {
        border-bottom: 1px solid #354661;
    }

    .solar-eye-border-top {
        border-top: 1px solid #354661;
    }

    .solar-eye-border {
        border: 1px solid #354661;
    }

    .tree-model .ant-form-item-control-wrapper {
        border: 1px solid #354661;
    }

    .ant-input-number-handler-wrap {
        background-color: transparent;
        border-left: 1px solid #354661;
    }

    .has-error .ant-input,
    .has-error .ant-input:hover {
        background-color: transparent;
    }

    .el-dialog-body .ant-btn {
        background-color: transparent;
        border: 1px solid #354661;
    }

    /* 日期控件清除图标 */
    .ant-calendar-picker-clear {
        background: #1e2a3c !important;
    }

    /* 表格单选 */
    .vxe-table .vxe-body--row.row--checked,
    .vxe-table .vxe-body--row.row--radio {
        background-color: transparent;
    }

    // 级联的clear
    .ant-cascader-picker-clear {
        background: #192c3e;
        color: #9b9b9b;
    }

    .ant-cascader-picker-clear:hover,.ant-anchor-link-title {
        color: white;
    }

    /* 下拉树 */
    .ant-select-tree-dropdown .ant-select-not-found .ant-empty-small,
    .ant-select-tree-dropdown .ant-select-dropdown-search .ant-select-search__field,
    .ant-empty-small {
        color: white !important;
    }

    .ant-select-tree-dropdown .ant-select-dropdown-search,
    .ant-select-tree li .ant-select-tree-node-content-wrapper:hover,
    .ant-select-tree-dropdown .ant-select-dropdown-search .ant-select-search__field,
    .ant-select-tree li .ant-select-tree-node-content-wrapper.ant-select-tree-node-selected {
        background: rgb(34, 53, 78) !important;
        background-color: rgb(34, 53, 78) !important;
    }

    .ant-select-tree-dropdown .ant-select-dropdown-search .ant-select-search__field {
        border: 1px solid #3D506D;
    }

    .ant-tree li .ant-tree-node-content-wrapper.ant-tree-node-selected,
    .ant-tree li .ant-tree-node-content-wrapper:hover {
        background-color: #22354e;
    }

    /* 下拉树节点不可选 */
    li.ant-select-tree-treenode-disabled>span:not(.ant-select-tree-switcher),
    li.ant-select-tree-treenode-disabled>.ant-select-tree-node-content-wrapper,
    li.ant-select-tree-treenode-disabled>.ant-select-tree-node-content-wrapper span {
        color: white !important;
    }

    /* 表格border */
    .ant-table-body>table,
    .ant-table-small,
    .ant-table-thead>tr,
    .ant-table-tbody>tr>td,
    .ant-table-thead>tr>th,
    .vxe-table--footer-wrapper {
        border: 0px !important;
    }

    // 主题按钮禁用的样式
    .ant-btn-primary.disabled,
    .ant-btn-primary[disabled],
    .ant-btn-primary.disabled:hover,
    .ant-btn-primary[disabled]:hover,
    .ant-btn-primary.disabled:focus,
    .ant-btn-primary[disabled]:focus,
    .ant-btn-primary.disabled:active,
    .ant-btn-primary[disabled]:active,
    .ant-btn-primary.disabled.active,
    .ant-btn-primary[disabled].active {
        color: white;
        background-color: #44556d;
        border-color: #354661;
        box-shadow: none;
    }

    .homepage-block-info-show {

        .info-num,
        .info-text {
            color: white;
        }

        .divider {
            border: 1px dashed #354661;
        }
    }

    .drawer-form-com .blank-area {
        border: 2px dashed #354661;
    }

    .drawer-form-com .preview-box .block-component {
        border: 2px dashed #354661
    }

    .drawer-form-com {
        .divider {
            border-right: 1px solid #354661;
        }
    }

    // 右键菜单
    .ant-menu.contextmenu {
        background: #002140;
        color: white !important;
    }

    .ant-upload-list-item-info .anticon-loading,
    .ant-upload-list-item-info .anticon-paper-clip {
        color: white;
    }

    .ant-upload-list-item-info:hover {
        background: #002140 !important;
    }

    .ant-tabs-nav .ant-tabs-tab-disabled {
        color: #9b9b9b;
    }

    .block-manage {
        .left-content {
            background: rgba(78, 121, 177, 0.1) !important;
            ;
            border-right: 1px solid #2A3E5B;
        }
    }

    .solar-eye-menu {
        background: rgba(78, 121, 177, 0.1) !important;
        color: white !important;

        .ant-menu-item-selected {
            background: rgba(78, 121, 177, 0.2) !important;
        }
    }

    .block-name {
        .white-color()
    }
   
    
    .ant-radio-inner:after {
        background-color: #267DCF !important;
    }

    .ant-popover-title {
        color: white !important;
    }

    .anticon-search.ant-input-search-icon:hover,
    .cancel-icon {
        color: white;
    }

    a[disabled] {
        color: #9b9b9b !important
    }

    .ant-popover-arrow {
        background: #22354e;
        border-bottom-color: #22354e;
        border-left-color: #22354e;
    }

    .ant-popover-placement-bottom>.ant-popover-content>.ant-popover-arrow,
    .ant-popover-placement-bottomLeft>.ant-popover-content>.ant-popover-arrow,
    .ant-popover-placement-bottomRight>.ant-popover-content>.ant-popover-arrow {
        border-top-color: #22354e;
        border-left-color: #22354e;
        box-shadow: -2px -2px 5px #22354e;

    }

    .ant-popover-placement-top>.ant-popover-content>.ant-popover-arrow,
    .ant-popover-placement-topLeft>.ant-popover-content>.ant-popover-arrow,
    .ant-popover-placement-topRight>.ant-popover-content>.ant-popover-arrow {
        border-right-color: #22354e;
        border-bottom-color: #22354e;
        box-shadow: 6px 3px 7px #22354e;
    }

    .ant-btn-icon-only.solar-eye-btn-primary:hover {
        background: transparent !important;


    }

    .ant-btn-icon-only.solar-eye-btn-primary:hover .anticon {
        color: @solar-eye-blue;
    }

    .ztree .curSelectedNode .node_name {
        color: white !important;
        background-color: #22354e !important;
    }

    .ztree .node_name:hover {
        background-color: #22354e !important;
    }

    // 通知公告
    .notice-list {
        .announce-item {
            border-bottom: 1px solid #3D506D;
        }
    }
    .homepage {
        .block-item {
            background: transparent;
        }
        .ghostClass,.dragClass {
            background-color: #22354e !important;
        }
        .chosenClass {
            background-color: #3D506D !important;
        }
    }
    .solar-eye-search-model .solareye-status .status-content .status-box {
        .status-btn {
            background:rgba(255, 255, 255, 0.1);
            color: #fff;
            .status-count {
                background: rgba(255, 255, 255, 0.29);;
            }
            .count-active {
                color: #FFFFFF;
                background: #267DCF;
            }
        }
        .active {
            color: #fff;
            background: rgba(38, 125, 207, 0.33);
            .status-count {
              background: #267DCF;
            }
  
        }
    }
    .solar-eye-gap {
        background: transparent;
    }
   
    .isSinglePS {
        background: #284772;
      }
      .solar-eye-search-model {
          .search-title {
              border-bottom: 1px solid #3D506D;
          }
      }
  .el-checkbox {
    .el-checkbox__input{
      .el-checkbox__inner {
        border: 1px solid #51637c;
        background: rgba(27,34,44,.3);
      }
    }
    .el-checkbox__input.is-checked {
      .el-checkbox__inner {
        background-color: #267DCF;
        border-color: #267DCF;
      }
    }

    .el-checkbox__input.is-disabled {
      .el-checkbox__inner::after {
        border-color: #C0C4CC;
      }

      .el-checkbox__inner {
        background-color: #edf2fc;
        border-color: #DCDFE6;
      }
    }
    .el-checkbox__input.is-indeterminate{
      .el-checkbox__inner {
        background-color: #267DCF;
        border-color: #267DCF;
      }
    }
  }

  .el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content,.el-tree-node__content:hover {
    background-color: #2A3E5B;
  }

  .node-disabled{
    color: rgba(255, 255, 255, 0.5) !important;
    cursor: not-allowed;
  }

  .el-tree{
    color: #fff;
    .el-tree-node.is-current{
      .custom-tree-node{
        background-color: #22354e;
      }
    }
    .tree-node-loading{
      color:#267DCF !important;
    }
    .health-row-clicked {
        color: hsla(0,0%,100%,.5019607843137255);
    }
  }
  .order-dispose {
    .ant-steps-item-icon {
        border-color: #60CAFE !important;
        border: 4px solid #60CAFE !important;
        background: none;
    }
    .ant-steps-item-tail::after {
        border-left-color: #4E6079;
    }
    .ant-steps-item-finish > .ant-steps-item-container > .ant-steps-item-tail::after {
        border-left-color: #60CAFE !important;
    }
    .ant-steps-item-process > .ant-steps-item-container > .ant-steps-item-tail::after {
        background-color: transparent;
    }
    .example-btn {
        background: #111D2C !important;
        border: 1px solid #15395B !important;
        color: #177DDC !important;
    }
    .title-box {
        color: #fff;
        .before {
          border-left-color: #60CAFE;
        }
    }
  }
  .alarm-events-tooptips {
    .data-content {
      background: linear-gradient(90deg, #753E4D 0%, #1F334E 100%) !important;
    }
  }
  .gbutton-group {
    .ant-dropdown-trigger:hover, .ant-dropdown-trigger:hover svg, .ant-btn:hover svg, .gbtn-svg-icon:hover {
      color: #1890FF !important;
    }
    .ant-btn, .ant-dropdown-trigger, .gbtn-svg-icon {
      color: #FFF;
    }
  }
  .operation-btn-hover:hover{
    .anticon {
      color: #1890FF !important;
    }
  }
  .my-table {
      .vxe-header--column .vxe-resizable.is--line:before {
          background-color: transparent;
      }
      .vxe-body--row:hover {
          background: transparent !important;
      }
      .vxe-body--row:nth-child(even) {
          background: transparent !important;
      }
      .vxe-body--column {
          border-bottom: 1px solid rgba(53, 70, 97, 0.2) !important;
      }
      .vxe-body--row.row--hover {
          background: rgba(66, 74, 85, 0.1) !important;
      }  
  }
}


.solar-eye-light {
    background: #f0f0f0;
    .homepage { 
        .block-item,.ghostClass,.dragClass {
            background: #fff !important;
        }
        .chosenClass {
            background-color: #d3d3d3 !important;
        }
    }
    .inverter-list-search-header {
        background: #fff;
    }
    .solar-eye-search-model {
        .search-title {
            border-bottom:1px solid #e8e8e8;
        }
    }
    .notice-list {
        .announce-item {
            border-bottom: 1px solid #eee;
        }
    }

    .vxe-cell {
        .ant-btn,
        .ant-btn.ant-btn-primary:hover,
        .ant-btn:focus {
            color: #1890FF !important;
        }
    }

    .no-data-text,
    .solar-eye-active,
    .title-label label,
    .block-name,
    .cancel-icon {
        color: #333333;
    }

    .operation-icon {
        cursor: pointer;
        color: #1890FF;
        .font-size(20px)
    }
    .operation-icon:hover {
        color: #FF8F33;
    }

    .no-more-data {
        color: #707070;
    }

    .solar-eye-drawer-bg {
        .ant-drawer-content {
            background: #f0f0f0;
        }
    }

    .chart-content {
        .weather-info {
            background: #f0f0f0
        }
    }

    .ant-drawer-content .btn {
        border-top: 1px solid #e9e9e9;
    }

    .save-btns {
        border-bottom: 1px solid #ccc;
    }

    .homepage-block-info-show {

        .info-num,
        .info-text {
            color: #333333;
        }
    }

    .drawer-form-com .preview-box .block-component,
    .homepage-block-info-show .divider,
    .drawer-form-com .blank-area {
        border: 2px dashed #d6d6d6
    }

    .ant-menu.ant-menu-root>.ant-menu-item-selected {
        background-color: #fff9f0;
    }

    .drawer-form-foot {
        border-top: 1px solid #e9e9e9;
    }

    .solar-eye-pure-bg,
    .solar-eye-main-content,
    .solar-eye-search-model, 
    .solar-eye-pure-bg-30,
    .table-area {
        background: white
    }

    .point-content,
    .device-content {
        background: white;
        .point-title {
            color: #333;
        }
    }

    .drawer-form-com {
        .divider {
            border-right: 1px dashed #d6d6d6;
        }
    }

    .device-content {
        border-top: 1px solid rgba(53, 70, 97, 0.6);
    }

    // 主操作按钮
    .solar-eye-btn-primary {
        &:not(.ant-btn-icon-only) {
            .colorize_bg(@primary-color, 0.1) !important;
            border-color: @primary-color;
        }

        color: @primary-color !important;
        text-shadow: none !important;
        &.ant-btn[disabled] {
            color: rgba(0, 0, 0, 0.25) !important;
            background-color: #f5f5f5 !important;
            border-color: #d9d9d9 ;
        }
    }

    .solar-eye-btn-primary-cancel.solar-eye-btn-primary {
        background: transparent !important;
        border: 1px solid #D6D6D6 !important;
        color: #5b5b5b !important;
    }

    .right-icon-border {
        color: @primary-color;
    }

    .info-text,
    .info-num,
    .chart-card-header-title {
        .dark-grey-color()
    }

    .ant-tabs-card-bar {
        .ant-tabs-ink-bar {
            background: url(../images/public/light_tab_select.png);
            //bottom: 3px;
        }
    }

    .ant-alert-message {
        color: rgba(0, 0, 0, 0.85)
    }

    .run-monitor {

        .chart-card,
        .chart-card-header {
            background: #FEFEFE;
        }

        .search-label,
        .rang-input-label {
            color: #707070;
        }

        .chart-card-header-title-read {
            color: #ADADAD !important;
        }

        .chart-data-text-num {
            .dark-grey-color()
        }

        .chart-data-text-label {
            color: #707070;
        }

        .chart-data-text {
            .left {
                background: url('../../assets/images/monitor/light_indicators_bg.png');

                .svg-icon {
                    color: #9B9B9B;
                }
            }
        }

        .warning-info-item {
            background: #f0f0f0;
            color: #9B9B9B;

            span {
                color: #5B5B5B;
            }
        }
    }

    .sort-header-item {
        border: 1px solid #d6d6d6;
        background: #fff;

        .sort-header-item-text {
            color: #707070;
        }
    }

    .sort-header-item-active,
    .sort-header-item:hover {
        border: 1px solid @primary-color;
        color: @primary-color;

        .sort-header-item-text {
            color: @primary-color;
        }
    }

    .point5-line4 {
        .point-out {
            padding-right: 4px;
            background: url('../../assets/images/monitor/light_device_work.png');

        }

        .point-out-active {
            background: url('../../assets/images/monitor/light_device_not_work.png');
        }
    }

    .solar-eye-seal {
        color: #9B9B9B;

        &:hover {
            color: #FF8F33;
        }
    }

    .ztree li a {
        color: #555555;
    }

    .ant-dropdown-menu,
    .ant-select-dropdown {
        box-shadow: 5px 4px 9px 0px rgba(3, 18, 38, 0.5);
        border: 1px solid #efefef;

        &::before {
            border-bottom: 10px solid #efefef;
        }

        &::after {

            border-bottom: 10px solid #fff;
        }
    }

    .page-pagination {
        background: #fff;
    }

    .data-header {
        .info-data {
            background: #FEFEFE;
            box-shadow: 0px 4px 6px 0px rgba(21, 60, 104, 0.09);
            border-radius: 4px;
        }
    }

    .solar-eye-seal-div {
        .svg-icon {
            color: #FF8787;
        }

        .solar-eye-seal-name {
            color: #FFB8B8;
        }
    }

    .monitor-footer {
        background: #fff;
    }

    .warning-drawer-header {
        background: #fff;
        border-bottom: 1px solid rgba(214, 214, 214, 1);

        .right-icon-border,
        .monitor-footer-icon-border,
        .realtime-warn,
        .record-info,
        .right-info,
        .right-icon {
            color: #707070;
        }

        .record-num {
            color: @primary-color;
        }

    }

    .solayeye__table-search-select--item {
        background: #f0f0f0;
    }

    .solayeye__table-search-select--item-active {
        background-color: fade(@primary-color, 10);
        color: @primary-color;
    }

    .record-num {
        color: @primary-color;
    }

    .vxe-table {
        .vxe-table--header-wrapper {
            background: #f0f0f0;
        }

        .vxe-body--row:hover,
        .vxe-body--row:nth-child(even) {
            background: #f0f0f099;
        }
    }

    .vxe-table.border--full .vxe-header--column {
        background-image: linear-gradient(#e8eaec, #e8eaec) !important;
    }

    .menu-manage-menus {
        .ant-menu-item-selected {
            background-color: #fff9f0 !important;
        }
    }

    .right-content,
    .radiusBox,
    ul.icon-ul {
        background: #fff;
    }
    .solar-eye-search-header {
        background: rgba(240, 240, 240, 0.59);
        .tab-item span {
            color: #9b9b9b;
        }
    }
    .car-table,
    .footer-table,
    .com-table,
    .tab-content .ant-tabs-bar,
    .search-model,
    .homePageManage,
    .count-card {
        background: #fff;
    }

    .drawer-box .drawer-foot {
        border-top: 1px solid #e9e9e9;
    }

    .manage-left-menu {
        .right-icon {
            background: #fff;
        }

        .menu-manage-icon {
            background: #FF8F33;
        }
    }

    // 数据质量分析
    .power-station-card {
        background: #FEFEFE;
        border: none;
        box-shadow: 0px 4px 6px 0px rgba(21, 60, 104, 0.09);

        &-top {
            background: #e6e6e6;
            border: 1px solid #e6e6e6;
            color: #5B5B5B;
        }

        .power-station-mark {
            color: white;
        }

        &-bottom {
            .devide-line {
                border: 1px dashed #D6D6D6;

                &-left {
                    color: #9B9B9B;
                }

                .num {
                    color: rgba(0, 0, 0, 0.85);
                }
            }
        }

        &:hover {
            box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.08);
            border: 1px solid @primary-color;
        }
    }

    .budget-analysis-top-item {
        background: #fff;

        .item-top {
            border-bottom: 1px solid #d6d6d6;
        }

        .solar-eye-space {
            .solar-eye-space-left {
                color: #5b5b5b;
            }

            .solar-eye-space-right {
                color: #333;
            }
        }

        .right-content {
            .title {
                color: #9b9b9b;
            }
        }
    }

    .solar-eye-search-wrapper {
        background: #fff;

        .search-wrapper-left {
            a {
                color: #5b5b5b;

                &.active {
                    background: url(../images/public/light_tab_select.png) no-repeat bottom;
                }

            }
        }
    }

    .echart-group {
        .left-chart {
            background: white;
        }
        .right-chart {
            background: white;
        }
    }

    .anticon-fullscreen,
    .anticon-fullscreen-exit,
    .anticon-vertical-align-bottom {
        color: #9B9B9B;
    }

    .echart-table,
    .big-show,
    .anomaly-model .anomaly-table {
        background: #fff;
    }

    .anomaly-model {
        .anomaly-icon {
            background: url('../images/budget_analysis/abnormal_analysis_bg_light.png');

            &::before {
                content: url('../images/budget_analysis/abnormal_light.png');
            }
        }
    }

    .flow-chart-btn {
        height: 130px;
        background: url('../images/budget_analysis/abnormal_analysis_bg_light.png') no-repeat;
        color: #FF8F33;
    }

    .solaryeye-menu {
        .menu-title {
            color: #5b5b5b;

            .solar-eye-arrow-down {
                color: #9b9b9b;
            }
        }

        .menu-content {
            border-bottom: 2px dashed #ccc;
        }

    }

    .solar-eye-drawer-body {
        background: #fff;
        border: '1px solid #e9e9e9';
    }

   :deep(.ant-tree) {
        background: #fff;

        .ant-tree-treenode-switcher-close,
        .ant-tree-node-content-wrapper {
            &:hover {
                background: #f0f0f0;
            }
        }
    }

    .com-project-tree {
        background: #fff;
    }

    .solareye-transfer {
        .ant-transfer-list-header {
            background: #f0f0f0;
        }
    }

    .solareye-drawer-left {
        border: 1px solid #d6d6d6;
    }

    .drawer-bootom-button {
        border-top: 1px solid #e8e8e8;
        background: #fff;
    }

    .block-manage {
        .left-content {
            background: #fff;
            border-right: 1px solid #e8e8e8;
        }
    }

    ::-webkit-scrollbar-track {
        background: #f6f6f6;
    }

    ::-webkit-scrollbar-thumb {
        background: #cdcdcd;
    }

    ::-webkit-scrollbar-thumb:hover {
        background: #747474;
    }

    ::-webkit-scrollbar-corner {
        background: #f6f6f6;
    }

    .health-base-param {
        background-color: #fff;

        :deep(.form-item-inject) {
            label {
                color: #606266;
            }
        }
    }

    .share-content-title {
        background: #f0f0f0;
        color: #333;
    }

    .share-content {
        border: 1px solid #f0f0f0;
    }

    .solar-eye-deep-tool,
    .manage-left-menu {
        .middle.middle-btn-expand {
            cursor: pointer;
            background: #FFFFFF;
            box-shadow: 0px 1px 5px 0px rgba(0, 0, 0, 0.07);
            border: 1px solid #D6D6D6;
            .middle-btn {
                transform: rotate(180deg);
            }
        }
        .middle-btn {
            color: @primary-color;
        }
    }

    .icon-1 {
        background: #F56C6C;
        opacity: 0.4;
    }

    .icon-2 {
        background: #E6A23A;
        opacity: 0.4;
    }

    .icon-3 {
        background: #64B1FF;
        opacity: 0.2;
    }

    .icon-4 {
        background: #883AE6;
        opacity: 0.23;
    }

    /* zhanbinbin 主题检查追加  */
    .vxe-table--fixed-left-wrapper,
    .vxe-table--fixed-right-wrapper {
        background: white !important;
    }

    .has-footer-method .vxe-table--body-wrapper::-webkit-scrollbar {
        height: 0;
    }

    .ant-table-hide-scrollbar::-webkit-scrollbar {
        height: 0;
    }

    .color_bg {
        background: #fff;
    }

    .com-color {
        color: #0D1625;
    }

    .solar-eye-flow-bg {
        background: #f0f0f0;
    }

    .solar-eye-border-bottom {
        border-bottom: 1px solid #e9e9e9;
    }

    .solar-eye-border-top {
        border-top: 1px solid #e9e9e9
    }

    .solar-eye-border {
        border: 1px solid #e9e9e9;
    }

    .tree-model .ant-form-item-control-wrapper {
        border: 1px solid #e9e9e9;
    }

    .vxe-table .vxe-table--footer-wrapper {
        border-top: 0 !important;
    }
    .sort-header {
        .desc {
            color: #555;
            .desc-color {
                color: @primary-color;
            }
        }
    }
    .isSinglePS {
        background: #ccc;
        color: white;
      }
    .health-row-clicked {
        color: #adadad;
    }
    .is--checked.vxe-custom--option .vxe-checkbox--icon:before,
    .is--checked.vxe-export--panel-column-option .vxe-checkbox--icon:before,
    .is--checked.vxe-table--filter-option .vxe-checkbox--icon:before,
    .is--indeterminate.vxe-custom--option .vxe-checkbox--icon:before,
    .is--indeterminate.vxe-export--panel-column-option .vxe-checkbox--icon:before,
    .is--indeterminate.vxe-table--filter-option .vxe-checkbox--icon:before,
    .vxe-table .is--checked.vxe-cell--checkbox .vxe-checkbox--icon:before,
    .vxe-table .is--indeterminate.vxe-cell--checkbox .vxe-checkbox--icon:before,
    .vxe-table .is--checked.vxe-cell--checkbox:hover .vxe-checkbox--icon:before,
  	.vxe-table .is--indeterminate.vxe-cell--checkbox:hover .vxe-checkbox--icon:before,
  	.vxe-table .is--checked.vxe-cell--radio .vxe-radio--icon:before,
  	.vxe-table .is--indeterminate.vxe-cell--radio .vxe-radio--icon:before,
  	.vxe-table .is--checked.vxe-cell--radio:hover .vxe-radio--icon:before,
  	.vxe-table .is--indeterminate.vxe-cell--radio:hover .vxe-radio--icon:before {
    	border-color: #ff8f33;
    	background-color: #ff8f33;
  	}
  	.vxe-table .vxe-cell--radio:hover .vxe-radio--unchecked-icon:before {
    	border-color: #ff8f33;
  	}
    .gbutton-group {
      .ant-dropdown-trigger:hover, .ant-btn:hover, .gbtn-svg-icon:hover, .svg-icon:hover {
        color: #FF8F33 !important;
      }
      .ant-btn, .ant-dropdown-trigger, .gbtn-svg-icon {
        color: #1890FF;
      }
    }
    .my-table {
        .vxe-body--column {
            border-bottom: 1px solid #E8E8E8 !important;
        }
        .vxe-body--row {
            background: transparent !important;
        }
        .vxe-header--column .vxe-resizable.is--line:before {
            background-color: transparent;
        }
        .vxe-body--row.row--hover {
            background: #F8F8F8 !important;
        }
    }
}

// 公用样式
.ant-menu.ant-menu-root .isParent.ant-menu-item-selected {
   
    background-size: 209px 73px !important;
    border-bottom: transparent !important;
    background-position: center !important;
}
.solar-eye-seal-name {
    color: #883737;
    position: absolute;
    left: 28px;
    top: 33px;
    transform: rotate(-45deg);
    .font-size(18px)
}

.nav-setting-icon {
    &::before,
    &::after {
        left: 40% !important;
    }
}
.nav-mail-icon {
    &::before,
    &::after {
        left: 45% !important;
    }
}
.nav-screen-icon {
    &::before,
    &::after {
        left: 45% !important;
    }
}
.search-menu {
    top: 42px !important;
}

.ant-dropdown .ant-dropdown-menu,
.ant-select-dropdown-placement-bottomLeft {
    margin-top: 16px;

    &::before {
        content: '';
        display: block;
        position: absolute;
        top: -10px;
        left: 30%;
        border-left: 10px solid transparent;
        border-right: 10px solid transparent;
    }

    &::after {
        content: '';
        display: block;
        position: absolute;
        top: -7.6px;
        left: 30%;
        border-left: 10px solid transparent;
        border-right: 10px solid transparent;
    }

}
.ant-dropdown.ant-dropdown-placement-bottomRight .ant-dropdown-menu {
    &::before {
        left: 70%;
    }
    &::after {
        left: 70%;
    }
}
.ant-tabs-card-bar {

    .ant-tabs-nav-container,
    .ant-tabs-tab {
        height: 40px !important;
        line-height: 40px !important;
        span {
            display: inline-block;
            height: 40px !important;
            line-height: 40px !important;
        }
    }
    .ant-tabs-ink-bar {
        visibility: visible !important;
        width: 30px !important;
        background-size: contain;
        height: 3px;
        left: 38px;

    }
}

.right-icon-border,
.monitor-footer-icon-border {
    background: .colorize_bg(@primary-color, 0.1) !important;
    border: 1px solid @primary-color;
}

.screen-action {
    width: 32px;
    margin-right: 16px;
    @media screen and(max-width: 1366px) {
        width: 42px;
    }
    height: 32px !important;
    border-radius: 4px;
    line-height: 36px !important;
    .font-size(16px);
    padding: 0 !important;
    color: #ACBAC5;
    border-left: 1px solid rgba(255, 255, 255, 0.23);
    border-radius: 0;
    text-align: center;

}

.ant-menu.ant-menu-root .isParent.ant-menu-item-selected,
.ant-menu.ant-menu-root .ant-menu-submenu-horizontal.ant-menu-submenu-selected {
    background: url('../images/public/menu-select.png')  no-repeat !important;
    background-size: 66px 14px !important;
    border-bottom: transparent !important;
    background-position: center 30px !important;
    background-color: transparent !important;
    animation: none;
    transition: none;
}

.top-nav-header-index .header-index-wide .ant-menu.ant-menu-horizontal {
    line-height: 56px !important;
    background: #24262B;
    // .isParent, .ant-menu-submenu {
    //     margin-right: 8px;
    // }
    .ant-menu-submenu:not(.ant-menu-submenu-selected) :hover,
    .isParent:hover,
    .ant-menu-submenu-active {
        background: #484A4F;
        border-radius: 3px;
    }
    .ant-menu-item > a, .ant-menu-submenu-title{
        color: #fff !important;
        opacity: 0.9 !important;
    }
    .ant-menu-submenu-selected > a, .ant-menu-submenu-selected > .ant-menu-submenu-title,
    .ant-menu-item-selected > a, .ant-menu-item-selected  > .ant-menu-submenu-title{
        color: #fff !important;
        opacity: 1 !important;
    }
    .submenu-title-wrapper:hover {
        background: none !important;
    }
    .submenu-title-wrapper {
        span:hover {
            background: none !important;
        }
    }
}

.vxe-table {

    .vxe-table--body-wrapper,
    .vxe-table--footer-wrapper {
        background: transparent !important;
    }

    .vxe-table--border-line,
    .vxe-table--header-border-line {
        border-color: transparent !important;
    }
}

.vxe-table.border--default .vxe-body--column,
.vxe-table.border--default .vxe-footer--column,
.vxe-table.border--default .vxe-header--column,
.vxe-table.border--inner .vxe-body--column,
.vxe-table.border--inner .vxe-footer--column,
.vxe-table.border--inner .vxe-header--column {
    background: transparent !important;
}

.vxe-table.border--full .vxe-header--column {
    background-size: 1px 26px !important;
    background-position: right !important;

}

.vxe-table.border--default .vxe-body--column,
.vxe-table.border--default .vxe-footer--column,
.vxe-table.border--inner .vxe-body--column,
.vxe-table.border--inner .vxe-footer--column {
    background-image: none !important;
}

.chart-eg-item {
    border: 1px solid rgba(147, 169, 199, 0.28);
}

.card-area.ant-card-bordered {
    border: none !important;
    background: transparent !important;
    box-shadow: none !important;

    .ant-card-body {
        padding: 0 !important;
    }
}
.sort-header {
    display: flex;
    justify-content: flex-end;
    flex: 1;
    align-items: center;
    .desc {
        flex: 1;
        font-family: PingFangSC-Regular, PingFang SC;
      }
    .sort-header-item {
        height: 28px;
        padding: 0 16px;
        text-align: center;
        border-radius: 14px;
        box-sizing: border-box;
        .font-size(16px);
        margin-left: 10px;
    }
    .sort-header-item-text,.sort-header-item-icon {
        .font-size(14px);
    }
    .sort-header-item-active, .sort-header-item:hover {
        cursor: pointer;
    }
}
.solar-eye-search-wrapper {
    display: flex;
    flex-direction: row;
    width: 100%;

    margin: 4px 0 16px;

    .search-wrapper-left {
        width: 70%;
        line-height: 54px;
        height: 54px;
        padding: 0 16px;
        display: flex;
        align-items: center;

        a {
            display: inline-block;
            width: 80px;
            text-align: center;

            +a {
                margin-left: 16px;
            }
        }
    }
    .sort-header {
        margin-right: 50px;
    }

  
}

.echart-group {
    .left-chart {
        width: 100%;
        height: calc((100% - 16px) / 2);

        &.left-chart {
            margin-bottom: 16px;
        }
    }

    .right-chart {
        height: 100%;
        position: relative;
    }

    .full-screen {
        width: 36px;
        height: 36px;
        border-radius: 36px;
    }
}

.anticon-fullscreen,
.anticon-fullscreen-exit,
.anticon-vertical-align-bottom {
    width: 36px;
    height: 36px;
    border-radius: 36px;
    line-height: 36px;
    text-align: center;
}

.solaryeye-menu {
    .menu-title {
        .solar-eye-arrow-up {
            color: @primary-color;
        }
    }

    .menu-title-before {
        border-left: 4px solid @primary-color;
    }
}

.solar-login-form {
    input {
        caret-color: rgba(0, 0, 0, .65) !important;
    }

    .ant-input {
        background: white !important;
        border: 1px solid #d9d9d9 !important;
        color: rgba(0, 0, 0, .65) !important;
    }

    .ant-input-affix-wrapper .ant-input-prefix,
    .ant-input-affix-wrapper .ant-input-suffix {
        color: rgba(0, 0, 0, .65) !important;
    }
}

.solar-eye-tooptip {
    max-width: 455px !important;
    max-height: 212px !important;
    overflow: auto;
}

.deep-anly-tooptip {
    max-width:1000px !important;
    max-height: 500px !important;
    overflow: auto;
}

.ant-menu-inline,
.ant-menu-vertical,
.ant-menu-vertical-left {
    border-right: none;
}

.chart-content {
    .chart-title {
        border-left: 3px solid @primary-color;
    }
}

:deep(.ant-pagination-item-link) {
    border: 0px;
}

:deep(.ant-pagination-item) {
    border: 0px;
}

.solar-eye-linkage-text {
    margin-right: 40px;
    margin-left: 10px
}

.solar-eye-linkage-icon {
    display: inline-block;
    height: 12px;
    width: 12px;
}

.solar-eye-flow-bg {
    height: 65vh;
    display: flex;

    img {
        margin: auto;
        max-width: 100%;
    }
}

.homepage-block-info-show.block-component[position='right'] {
    .left {
        img {
            width: 40px;
            height: 40px;
        }
    }

    .right {
        margin-left: 8px;
    }

    .info-num {
        font-size: 16px !important;
    }
}

.homepage-main-link:hover {
    color: @primary-color  !important;
}

.alarm-events-tooptips {
    max-width: 800px !important;
    max-height: 400px !important;
}
// 由于ai诊断要求悬浮窗超出不隐藏  所以tooltip加了appendToBody: true,  导致样式在诊断文件中不生效， 通过全局搜索  这个类只有在诊断中使用 所以放在这里
.alarm-events-tooptips {
  padding: 12px 16px !important;
  font-size:12px !important;
  .time {
    margin-right: 12px;
    font-size: 14px;
    font-weight: 600;
  }
  .station-starus {
    background: linear-gradient(135deg, #F87373 0%, #F6504D 100%);
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.15);
    border-radius: 10px 2px 10px 2px;
    height: 20px;
    font-size: 12px;
    padding: 0 8px;
    color: #FFFFFF;
    font-weight: 600;
    line-height: 20px;
  }
  .alarm-data {
    display: flex;
    .data-content {
      background: linear-gradient(90deg, #FFEEED 0%, #FFFFFF 100%);
      border-radius: 2px;
      padding: 4px 8px;
      margin-bottom: 12px;
      font-weight: 400;
      font-size: 12px;
      color: #F56C6C;
      display: flex;
      align-items: center;
      .split-line {
        width: 1px;
        height: 12px;
        background: #F7E2E2;
        margin: 0 12px;
      }
    }
  }
}
.order-dispose {
    .title-box {
        font-size: 16px;
        font-weight: 500;
        color: #5b5b5b;
        padding-bottom: 24px;
        .before {
            width: 4px;
            height: 15px;
            border-left: 4px solid #FF8F33;
            margin-right: 16px;
            border-radius: 0px 2px 2px 0px;
        }
    }
}
.solareye-menu-group + .solareye-menu-group {
    margin-left: 22px;
}
.solareye-menu-group + .solareye-menu-group::after {
    height: calc(100% - 80px);
    margin: 60px 0px 60px -12px;
    border: 1px solid #fff;
    opacity: .15;
    content: '';
    position: absolute;
    top: 0; 
}
.has-colon {
    .ant-form-item-label > label::after {
        content: '\FF1A' !important;
        margin: 0;
        padding-right: 8px;
    }
}
.div-center {
    display: flex;
    align-items: center;
    justify-content: center;
}
.align-center {
    display: flex;
    align-items: center;
}
.justify-center {
    display: flex;
    justify-content: center;
}
.close-search-menu {
    opacity: 0 !important;
}