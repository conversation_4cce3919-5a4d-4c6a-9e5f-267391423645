
span.demonstration{
	font-size: 12px;
	padding-right: 5px;
}
.btn-group{
	width: 100%;
	text-align: right;
	overflow: hidden;
  .ant-btn{
    margin-left: 10px;
    margin-bottom: 10px;
    margin-right: 0 !important;
  }
}
.search-model{
	text-align: left;
	height: auto;
  min-height: 42px;
  overflow: auto;
  position: relative;
  z-index: 8;
  padding: 0 15px;
  border-radius: 4px;
}
.padding-10{
	padding-top: 10px;
}
.padding-12{
	padding-top: 12px;
}
.margin-10{
  margin-top: 10px;
}
.search-item{
	float: left;
	display: flex;
	align-items: center;
	margin-right: 10px;
	padding-bottom: 10px;
  height: 42px;
	span.space{
		font-size: 12px;
		margin: 0 2px;
	}
  .width-180{
  	width: 180px;
  }
  .width-160{
  	width: 160px;
  }
  .width-150{
  	width: 150px;
  }
	.width-140{
		width: 140px;
	}
  .width-120{
  	width: 140px;
  }
  .width-100{
  	width: 100px;
  }
	.width-90{
		width: 90px;
	}
  .ant-calendar-picker{
    min-width: auto !important;
  }
  .ant-btn{
    margin-bottom: 0 !important;
  }
}
.ant-modal-footer div{
  text-align: center;
}
.balck-footer {
	text-align: right !important;
}
.model-footer-position-center{
  text-align: center;
  position: absolute;
  bottom: 5px;
  width: 100%;
  .ant-btn{
    margin-left: 10px;
  }
}
.model-footer-center{
  margin-top: 20px;
  text-align: center;
  .ant-btn{
    margin-left: 10px;
  }
}
.vxe-cell .ant-btn{
  border: 0;
  background-color: unset !important;
  color: #1890FF;
  min-width: 28px;
  height: 28px;
  box-shadow: 0 0 black;
}
.ant-modal-body{
  max-height: calc(100vh - 218px);
  // min-height: 30vh;
  overflow: auto;
}
.ant-modal-confirm .ant-modal-body{
  min-height: auto;
}
.modal-footer{
  width: 100%;
  text-align: center;
}
.ant-modal-footer-center{
  text-align: center;
}
.drop-dropdown{
  max-height: 60vh;
  overflow: auto;
}
.drop-down {
  display: block !important;
  line-height: 30px !important;
  margin-left: 0 !important;
  padding: 5px 15px !important;
	min-width: 160px;
}
.ant-dropdown .ant-dropdown-menu .ant-checkbox-group{
  max-height: 40vh;
  overflow: auto;
}
.is--visible--hide{
  display: none !important;
}
// 弹窗内-表格tip不显示问题
.vxe-table--tooltip-wrapper{
  z-index: 1001 !important;
}
.ant-modal-wrap{
	display: flex;
  overflow: hidden;
}
.ant-modal{
	margin: auto !important;
  top: 0;
}
.emp-table .vxe-cell--valid{
  display: none !important;
}
.ant-tabs-nav-container{
	margin-top: 0;
}
.com-table{
  width: 100%;
  padding: 15px;
  margin-top: 12px;
  border-radius: 4px;
}