@import './health.less';
@import './darkAndLight.less';
@blue: #1890FF;

@font-face {
  font-family: 'NeoGram-DemiBold';
  src:url('../fonts/NeoGram-DemiBold.ttf')
}
.colorize_bg(@color: @white, @alpha: 1) {
  background: hsla(hue(@color), saturation(@color), lightness(@color), @alpha);
}

/*列表td的padding设置 可以控制列表大小*/
.ant-table-tbody .ant-table-row td {
  padding-top: 15px;
  padding-bottom: 15px;
}

/*列表页面弹出modal*/
.ant-modal-cust-warp {
  height: 100%
}

/*弹出modal Y轴滚动条*/
.ant-modal-cust-warp .ant-modal-body {
  height: calc(100% - 110px) !important;
  overflow-y: auto
}

/*弹出modal 先有content后有body 故滚动条控制在body上*/
.ant-modal-cust-warp .ant-modal-content {
  height: 90% !important;
  overflow-y: hidden
}

/*列表中有图片的加这个样式 参考用户管理*/
.anty-img-wrap {
  height: 25px;
  position: relative;
}

.anty-img-wrap>img {
  max-height: 100%;
}
/* 内嵌子表背景颜色 */
.j-inner-table-wrapper :deep(.ant-table-expanded-row .ant-table-wrapper .ant-table-tbody .ant-table-row) {
  background-color: #FFFFFF;
}

/**隐藏样式-modal确定按钮 */
.solareye-hidden {
  display: none
}

.user-layout-login {
  .ant-tabs-tab {
    opacity: 0.43;
    font-family: PingFangSC-Semibold;
    font-size: 34px;
    color: #333333;

    &.ant-tabs-tab-active {
      font-family: PingFangSC-Semibold;
      font-size: 36px;
      color: #333333;
    }
  }
}

.ant-modal {
  top: 0 !important
}
.ant-card {
  border-radius: 4px !important;
  box-shadow: 2px 4px 10px rgba(51, 51, 51, 0.1);

  &:hover {
    box-shadow: 4px 4px 10px rgba(51, 51, 51, 0.2)
  }

  .ant-card-extra {
    padding: 0;
  }

  .ant-card-head-title {
    padding: 13px 0;
    font-weight: bolder;
    color: #333333;
    font-family: PingFangSC-Medium, PingFang SC;
  }
}

/*滚动条样式*/
::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 8px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 8px;
}

::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  background: #D6D6D6;
  border-radius: 4px;
}

::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  background: transparent;
}

.ant-card+.ant-card {
  margin-top: 16px;
}
.ant-card.solareye-card {

  // 
  .ant-card-body {
    padding-top: 4px;
  }

  .ant-card-head {
    border-radius: 4px 4px 0 0;
    padding: 0px 24px;
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.07);
    .ant-card-extra {
      .detail {
        color: #333;
        padding-right: 16px;
        vertical-align: -webkit-baseline-middle;
      }
    }

    .ant-card-extra,
    .ant-card-head-title {
      padding: 10px 0;
    }
  }
}
.solar-eye-card-title-40.ant-card {
  .ant-card-head {
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.07);
    min-height: 40px;
    .ant-card-extra,
    .ant-card-head-title {
      padding: 8px 0;
    } 
  }
  .ant-card-body {
    padding-bottom: 0px !important;
  }
}
// 列表上方的操作按钮
.table-operator {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 16px;

  .ant-btn {
    margin: 0 16px 0 0;
    height: 32px;
  }
}

.table-operator .ant-btn-group .ant-btn {
  margin: 0;
}

.table-operator .ant-btn-group .ant-btn:last-child {
  margin: 0 8px 8px 0;
}



.solar-eye-btn-primary-line {
  color: @primary-color;
  border-color: @primary-color;
}
.solar-eye-btn-grey {
  border-radius: 4px;
  margin-left: 16px;
  &:hover{
    color: @primary-color;
    border-color: @primary-color;
  }
}
/* solarery 的tab checkbox start */
.se-tab-checbox {
  cursor: pointer;
  padding: 0 20px;
  height: 28px;
  line-height: 28px;
  background: #f7f7f7;
  border-radius: 30px;

  span:first-child {
    padding-left: 16px;
  }

  span+span {
    margin-left: 16px;
  }

  .a-row-span {
    height: 16px;
    color: white;
    border-radius: 9px;
    font-size: 12px;
    line-height: 14px;
    padding: 0.2px 5px;
    margin-left: 5px;
  }
}

.se-tab-checbox+.se-tab-checbox {
  margin-left: 16px;
}

.se-tab-checbox.btn-selected {
  .colorize_bg(@primary-color, 0.1) !important;
  color: @primary-color;
}

/* solarery 的tab checkbox end */
.a-row-span {
  border: 1px solid @primary-color;
  background-color: @primary-color;
}

.solareye-tag {
  // tag 按钮
  background: @primary-color;
  border-color: @primary-color;
  color: white;
}

// 分页居中
.ant-table-pagination.ant-pagination {
  float: none;
  margin: 16px 0;
  text-align: center;
}

.ant-table-small {
  border: none;

  .ant-table-content .ant-table-header {
    background: #F0F0F0;
  }

  .ant-table-thead {
    background: #F0F0F0;
  }
}

.anticon.ant-tree-switcher-icon {
  color: rgba(0, 0, 0, .6)
}

.ant-table-thead>tr>th.ant-table-column-sort,
.ant-table-tbody>tr>td.ant-table-column-sort {
  background: transparent;
}

// 列表操作列的a标签颜色
.blue {
  color: @blue;

  &:hover {
    color: @primary-color;
  }
}

.flex-action-button {
  a {
    color: @blue;

    &:hover {
      color: @blue;
    }
  }

  a+a {
    margin-left: 10px;
  }
}

// 多选框最小宽度
.multiple_select_com {
  min-width: 140px;
}

// 字体/图标颜色
.solareye-color-danger{
  color:#F56C6C !important;
}

.solareye-color-warning{
  color:#E6A23A !important;
}


.solareye-color-success{
  color:#67C239 !important;
}


.solareye-color-off-line{
  color:#9D9997 !important;
}


.solareye-color-link{
  color:#64B1FF !important;
}

.solareye-color-primary{
  color:@primary-color !important;
}

.cursor-pointer{
  cursor:pointer;
}

.flex-space-between{
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.flex-start{
  display: flex;
  justify-content: start;
  align-items: center;
}
.flex-column{
  flex-direction: column;
}
.flex-center{
  display: flex;
  justify-content: center;
  align-items: center;
}
.flex-end{
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.flex-start{
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

flex-column{
  flex-direction: column;
}

.width-100-height-100{
  width: 100%;
  height: 100%;
}

.text-blod{
  font-weight: bold;
}

// 边距
@arr: 0, 4, 8, 10 , 12, 16, 20, 24, 28, 30, 32, 36, 40, 44,48,52,56;

each(@arr, {
  @num:extract(@arr, @index);
  .margin-t-@{value} {
    margin-top:~"@{num}px";
  }
  .margin-r-@{value} {
    margin-right:~"@{num}px";
  }
  .margin-b-@{value} {
    margin-bottom:~"@{num}px";
  }
  .margin-l-@{value} {
    margin-left:~"@{num}px";
  }
  .padding-t-@{value} {
    padding-top:~"@{num}px";
  }
  .padding-r-@{value} {
    padding-right:~"@{num}px";
  }
  .padding-b-@{value} {
    padding-bottom:~"@{num}px";
  }
  .padding-l-@{value} {
    padding-left:~"@{num}px";
  }
});

//vex-table的checkbox 等颜色根据primary-color 保持一致 start

// .is--checked.vxe-custom--option .vxe-checkbox--icon:before,
// .is--checked.vxe-export--panel-column-option .vxe-checkbox--icon:before,
// .is--checked.vxe-table--filter-option .vxe-checkbox--icon:before,
// .is--indeterminate.vxe-custom--option .vxe-checkbox--icon:before,
// .is--indeterminate.vxe-export--panel-column-option .vxe-checkbox--icon:before,
// .is--indeterminate.vxe-table--filter-option .vxe-checkbox--icon:before,
// .vxe-table .is--checked.vxe-cell--checkbox .vxe-checkbox--icon:before,
// .vxe-table .is--indeterminate.vxe-cell--checkbox .vxe-checkbox--icon:before,
// .vxe-table .is--checked.vxe-cell--checkbox:hover .vxe-checkbox--icon:before,
// .vxe-table .is--indeterminate.vxe-cell--checkbox:hover .vxe-checkbox--icon:before {
//   border-color: @primary-color;
//   background-color: @primary-color;
// }

.vxe-custom--option:hover .vxe-checkbox--icon:before,
.vxe-export--panel-column-option:hover .vxe-checkbox--icon:before,
.vxe-table--filter-option:hover .vxe-checkbox--icon:before,
.vxe-table .vxe-cell--checkbox:hover .vxe-checkbox--icon:before {
  border-color: @primary-color  !important;
}
//vex-table的checkbox 等颜色根据primary-color 保持一致 end

.main-parent .main{
	width: 100%;
	height: 100%;
	overflow: hidden;
}
.full-modal .ant-modal{
  height: calc(100vh - 88px);
  top: 111px !important;
  position: absolute;
}
.full-modal .ant-tabs-bar{
  position: fixed;
  width: 100%;
  background: #fff;
  z-index: 9;
  margin: 0;
}
.full-modal .ant-modal .ant-tabs-content{
  position: relative;
  margin-top: 60px;
}
.full-modal .ant-modal .ant-modal-content{
  height: 100%;
}
// .full-modal .ant-modal .flow-detail{
//   margin-top: 60px;
// }
.full-modal .ant-modal .ant-modal-body{
  padding: 0px 24px 24px 24px;
}
.full-modal .ant-modal .ant-modal-footer{
  position: absolute;
  bottom: 0;
  width: 100%;
}
.drawer-box {
  .ant-drawer-body {
    padding: 0;
    height: calc(100% - 55px) !important;
    overflow: auto;
  }
  .drawer-content {
    padding: 0px 24px;
  }
  .drawer-content-12 {
    padding: 12px 24px;
  }
  .drawer-foot {
    position: absolute;
    bottom: 0;
    width: 98%;
    text-align: center;
    padding: 10px 0;
    z-index: 999;
    button + button {
      margin-bottom: 0;
      margin-left: 16px;
    }
  }
}
.drawer-form-com{
	width: 100%;
	height: 100%;
	padding: 12px 12px 0;
}
.drawer-form-content{
  width: 100%;
  height: calc(100% - 55px);
  padding-bottom: 12px;
  overflow-y: auto;
  overflow-x: hidden;
  padding-right: 28px;
}
.drawer-form-foot{
	text-align: center;
	padding: 10px 0;
	button + button {
	  margin-bottom: 0;
	  margin-left: 16px;
	}
}
// 统一去除 form 表单label 后的：
.ant-form-item-label > label::after {
  content: '' !important;
}
.margin-16 {
  margin: 16px;
}
.margin-16-bottom {
  margin-bottom: 16px;
}
.margin-16-top {
  margin-top: 16px;
}
.solar-eye-popover {
  .ant-popover-title{
    &::before{
      content: "|";
      font-size: 14px;
      width: 3px;
      height: 15px;
      background: @primary-color;
      color: @primary-color;
      border-radius: 0px 2px 2px 0px;
      margin-right: 8px;
    }
    margin-left: -15px;
    border-bottom: transparent;
  }
}
.solareye-transfer {
  .ant-transfer-list-header {
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    padding: 21px 12px;
  }
  .ant-transfer-list-body-search-wrapper {
    top: 24px;
  }
  .ant-transfer-list-body-with-search{
    padding-top: 78px;
  }
  .ant-transfer-operation {
    display: inline-flex;
    margin: 0 46px;
    .ant-btn {
      width: 141px;
      height: 64px;
      background: @primary-color;
      border-radius: 4px;
      padding: 0;
      color: white;
    }
    .ant-btn[disabled] {
      opacity: 0.4;
      
    }
    .ant-btn+.ant-btn {
      margin-left: 24px;
    }
  }
}
.flow-chart-btn {
  position: absolute;
  top: calc(50% - 40px);
  right: 4px;
  width: 24px;
  color: white;
  text-align: center;
  cursor: pointer;
  border-radius: 4px;
  padding: 16px 0;
}
  @media screen and (max-width: 1680px) {
    .page-header-index-wide,.app-container{
      height: 100%;
      overflow: hidden auto;
    }
  }
video::-webkit-media-controls{ 
    display:none !important;
}
.solar-eye-hover-primary:hover {
  color:@primary-color!important;
  transition:.3s;
}
.solar-eye-screen-bar {
  width: 100%;
  height: 100%;
  text-align: center;
}
.solar-eye-screen-title {
  font-size: 16px;
  font-weight: 500;
  text-align: center;
  color: white;
  font-family: PingFangSC-Medium, PingFang SC;
  background: url('../images/screen/title_bg.png') no-repeat;
  background-position: bottom;
  line-height: 38px;
} 
.solar-eye-deep-tool {
  display: flex;
  align-items: center;
  margin-top: 16px;
  position: relative;
  .left {
    width: 300px;
   
  }
  .middle {
    width: 32px;
    height: 38px;
    position: fixed;
    text-align: center;
    line-height: 38px;
    z-index: 2;
    border-radius: 0px 4px 4px 0px;
    .middle-btn {
          border: none;
          font-size: 16px;
    }
  }
  .right {
    margin-left: 16px;
  }
}
.solar-eye-legend {
  text-align: left;
}
.legend-marker {
  width: 10px;
  height: 10px;
  border-radius: 10px;
  display: inline-block;
  vertical-align: middle;
  margin-right: 8px;
}
//平台的整体布局
.solar-eye-content {
  display: flex;
  width: 100%;
  height: 100%;
  flex-direction: row;
  .left{
    width: 200px;
  }
  .right{
    flex: 1;
    height: 100%;
    overflow: hidden;
    :deep(.ant-tabs-line){
      height: 100%;
    }
    :deep(.ant-tabs-bar){
      margin: 0;
    }
    :deep(.ant-tabs-content){
      height: calc(100% - 44px);
    }
    :deep(.ant-tabs-tabpane){
      height: 100%;
      width: 100%;
    }
  }
}
// 查询区域 start
.solar-eye-search-model {
  width: 100%;
  border-radius: 4px;
  &.no-radius {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
  }
  .ant-tabs-bar {
    margin: 0;
  }
  .solar-eye-search-header {
    height: 64px;
    border-radius: 4px 4px 0 0
  }
  .search-title { // 实现类似a-card header 的效果
    
    font-size:18px;
    font-weight:bold;
    line-height:40px;
    margin-bottom: 12px;
    padding-left: 12px;
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.07);
  }
  .solar-eye-search-content {
		margin: 0 !important;
    padding:0 12px 16px;
    .solar-eye-search-content-tab {
      padding-top: 12px;
    }
    // 兼容查询条件是 form 表单的情况
    .ant-form-item {
      display: flex;
      margin: 16px 16px 0;
    }
    .ant-form-item-control-wrapper {
      flex: 1;
    }
    .com-color {
      margin-left: 8px;
      cursor: pointer;
    }
  }
  .ant-form-item {
    display: flex;
    margin: 16px 16px 0;
  }
  .ant-form-item-control-wrapper {
    flex: 1;
  }
  .search-title {
    border-bottom: 1px solid #e8e8e8;
    font-size: 16px;
    font-weight: bold;
    line-height: 40px;
  }
  .search-item {
    margin: 24px 0 0 0;
    height: 32px;
    display: inline-flex;
    width: 100%;
    .search-label {
      width: auto;
      text-align: right;
      padding-right: 8px;
      align-items: center;
      white-space: nowrap;
    }
    .ant-select,.ant-input,.ant-cascader-picker,.ant-calendar-picker {
      width: 100%;
			overflow: hidden;
    }
    .com-project-select {
      width: 83.5% !important;
      @media screen and (min-width: 2000px) { 
        width: 87% !important;
      }
      @media screen and (min-width: 3200px) { 
        width: 87% !important;
      }
      @media screen and (max-width: 1366px) { 
        width: 80% !important;
      }
			overflow: hidden;
    }
    .ant-btn+.ant-btn {
      margin-left: 8px;
    }
  }
   // 诊断中心处理状态
  .solareye-status {
    padding: 8px 12px 12px;
    width: 25%;
    .status-content {
      width:100%;
      position:relative;
      .status-box {
        position:absolute;
        top:-14px;
        width:600px;
        padding: 0;
        .status-btn {
          padding: 0 20px;
          height: 28px;
          background: rgb(247, 247, 247);
          border-radius: 30px;
          display: inline-block;
          margin-right: 16px;
          cursor: pointer;
          color: #333;
          .div-center {
            display: flex;
            align-items: center;
            padding-top: 3px;
            justify-content: center; 
          }
          .status-count {
            background: #C2C2C2;;
            border-radius: 8px;
            display: flex;
            padding: 2px 4px 2px;
            height: 16px;
            align-items: center;
            color: #FFFFFF;
            justify-content: center;
            margin-left: 8px;
            
          }
          .count-active {
            color: #FFFFFF;
            background: #FF8F33;
          }
        }
        .active {
          color: #FF8F33;
          background: rgba(255, 143, 51, 0.1);
          .status-count {
            background: #FF8F33;
          }

        }
        
      }
    }
  }
}
// 查询区域 end

// 诊断中心等的自定义tab start
.solar-custom-tab {
  display: inline-flex;
  align-items: center;
  line-height: 28px;
  padding: 18px 0;
  width: 100%;
  justify-content: center;
  border-radius: 4px 4px 0 0;
  background-size: 100% 100%;
  img {
    max-width: 27px;
  }
  span {
    font-size: 18px;
    margin: 0px 12px;
  }
  .data-count {
    border-radius: 4px;
    line-height: 28px;
    padding: 0 20px;
    color: #FFFFFF;
    background: #E6A23A;
    clip-path: polygon(5% 0, 100% 0, 100% 100%,5% 100%, 5% 65%,0 50%,5% 35%);;
  }
}
.solar-eye-gap {
  height: 16px;
  background: rgb(240, 240, 240)
}

// 诊断中心等的自定义tab end
.solar-eye-main-content {
  border-radius: 4px;
  width: 100%;
  flex: 1;
  padding:16px;

  .operation{
    margin-bottom: 16px;
    .operation-btn {
      width: 100%;
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }
  }
  .operation-has-text{
    margin-bottom: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    height: 32px;

    .operation-text{
      color: red;
      text-align: left;
    }
    .operation-btn {
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }
  }

  .operation-btn {
    text-align: right;
    margin-bottom: 16px;
		.ant-btn{
			margin-left: 10px;
      cursor: pointer;
		}
    .ant-btn[disabled] {
      cursor: not-allowed;
    }
  }
  .vxe-cell .svg-icon{
    cursor: pointer;
    margin-right: 6px;
    &:hover {
      color: #267DCF;
    }
  }
  .ant-table-row a {
    color: #267DCF;
    &:hover {
      color: #267DCF;
    }
  }
  
}
.solar-eye-main-content.no-radius {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  padding-top: 0; 
    .operation {
      padding-top: 16px;
      margin-bottom: 32px;
      border-top: 1px solid #eee;
    }
}
.ant-menu-inline .ant-menu-item {
  width: calc(100%);
  &:after {
    right: 3px !important;
  } 
  
}
.ant-btn-icon-only+.ant-btn-icon-only {
  margin-left: 8px;
}
.solar-eye-primary-color {
  color: @primary-color;
}
.ztree {
  .button.chk+[target=_blank] {
    cursor: default;
    pointer-events: none;
  }
}
.tab-content {
  .ant-tabs-bar {
    margin: 0 !important;
  }
}

.bottom-auto-height {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 124px);
  .top {
    flex-shrink: 0;
  }
  .bottom {
    flex: 1;
    overflow: hidden;
  }
}
.search-item {
  .ps-tree-input {
    margin-bottom: 0 !important;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    display: inline-flex;
    vertical-align: middle;
    &.ant-input-disabled {
      pointer-events: none;
      cursor: not-allowed;
    }
    .ellipsis {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
.drawer-position {
  position: relative;
}

// 虚拟树需要固定高度和宽度
.ps-tree-select {
  min-width: 500px!important;
}
.ant-select-selection--multiple :not(.ant-select-selection__choice__disabled) {
  .ant-select-selection__choice:first-child {
    max-width:  70%;
  }
}
.ant-select-selection--multiple li:first-child:nth-last-child(2) {
   max-width:  90% !important;
}
  .title-fontSize {
    font-size: 18px;
  }
  .title-fontSize-small {
    font-size: 16px;
  }
.fixed-right-column-base(){
  float: right;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.fixed-right-column{
  .fixed-right-column-base();
  width: 80px;
}
.fixed-right-column-120{
  .fixed-right-column-base();
  width: 120px;
}
.fixed-right-column-160{
  .fixed-right-column-base();
  width: 160px;
}
.no-platform {
  .ant-message-notice-content {
    background: #FFF1F0;
    border: 1px solid #FFA39E;
  }
}

// 列表操作按钮悬停效果
.ant-btn.operation-btn-hover{
  &:hover{
     color: @primary-color !important;
  }
}
// 列表状态颜色公共样式
@noSubmit-bg: #A441D5;
@waitBegin-bg: #D54941;
@undo-bg: #FF8100;
@processing-bg: #1366EC;
@check-bg: #E2AA11;
@finish-bg: #2BA471;
@stop-bg: #C5C5C5;
.status-info{
  display: flex;
  justify-content: center;
  align-items: center;
}
.status-info-left{
  display: flex;
  align-items: center;
}
.statusCol {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin-right: 8px;
}

.color-noSubmit {
  border: none;
  background: @noSubmit-bg;
}

.color-waitBegin {
  border: none;
  background: @waitBegin-bg;
}

.color-undo {
  border: none;
  background: @undo-bg;
}

.color-processing {
  border: none;
  background: @processing-bg;
}

.color-check {
  border: none;
  background: @check-bg;
}

.color-finish {
  border: none;
  background: @finish-bg;
}

.color-stop {
  border: none;
  background: @stop-bg;
}
.ant-dropdown-trigger.ant-dropdown-open > .anticon.anticon-down, .ant-dropdown-link > .anticon.anticon-down {
  transform: rotate(180deg);
}
.editor-detail {
  table {
    border-collapse: collapse;
    width: 100%
  }
  table td {
    border: 1px solid #ccc;
    margin: 0;
    padding:4px 12px;
  }
}
.header-line {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 8px;
  justify-content: space-between;
  [class*=left], [class*=right] {
    cursor: pointer;
  }
  [class*=left] {
    padding-right: 8px;
  }
  [class*=right] {
    padding-left: 8px;
  }
}
.drawer-content-title {
  font-family: PingFangSC-Medium;
font-size: 16px;
margin-bottom: 16px;
font-weight: normal;
&::before {
  content: ' ';
  width: 4px;
  height: 21px;
  border-radius: 0px 2px 2px 0px;
  opacity: 1;
  background: #FF8F33;
  display: inline-block;
    margin-right: 16px;
    vertical-align: middle;
}
}
.solar-eye-dark {
  .drawer-content-title {
    &::before {
      background: #60CAFE;
    }
  }
}
.vxe-noBorder.border--default {
  .vxe-body--column {
    border-bottom: 1px solid  #D6D6D6;
  }
  .vxe-header--column {
    .vxe-resizable{
      opacity: 0;
    }
    &:hover {
      .vxe-resizable{
        opacity: 1;
      }
    }
  }
  .table-operator-btn,.delete {
    margin-right: 16px;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    font-weight: normal;
  }
  .delete {
    color: #F6504D;
  }
  .table-operator-btn {
    color:  #1366EC;
  }
} 
// 自定义tooltip显示宽度调整
.custom-tooltip-title-style {
  .ant-tooltip-content {
    width: 350px;
  }
}

.solar-eye-dark {
  .vxe-noBorder.border--default {
    .vxe-body--column {
      border-bottom: 1px solid #3f526e96;
    }
  }
}
.template-download {
  text-decoration: underline;
  vertical-align: bottom;
  padding-left: 16px;
}