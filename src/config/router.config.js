import { UserLayout, TabLayout } from '@/components/layouts';

/**
 * 走菜单，走权限控制
 * @type {[null,null]}
 */
export const asyncRouterMap = [

  {
    path: '/',
    name: 'dashboard',
    component: TabLayout,
    meta: { title: '首页' },
    redirect: '/dashboard/analysis',
    children: []
  },
  {
    path: '*',
    redirect: '/404',
    hidden: true
  }
];

/**
 * 基础路由
 * @type { *[] }
 */
export const constantRouterMap = [{
  path: '/user',
  component: UserLayout,
  redirect: '/user/login',
  hidden: true,
  children: [{
    path: 'login',
    name: 'login',
    component: () =>
                    import(/* webpackChunkName: "user" */ '@/views/user/Login')
  },
  {
    path: 'alteration',
    name: 'alteration',
    component: () =>
                    import(/* webpackChunkName: "user" */ '@/views/user/alteration/Alteration')
  }
  ]
},
// 大屏
{
  path: '/screen/page',
  component: () =>
            import(/* webpackChunkName: "user" */ '@/views/screen/index.vue')
            // path: '/',
            // component: BlankLayout,
            // redirect: '/screen/page',
            // hidden: true,
            // children: [{
            //     path: '/screen/page',
            //     name: 'screen',
            //     hidden: true,
            //     component: () =>
            //         import ('@/views/screen/index.vue')
            //     }
            // ]
}, // 大屏
{
  path: '/screen/jd',
  component: () =>
            import(/* webpackChunkName: "user" */ '@/views/screen/JD.vue')
}, {
  path: '/404',
  component: () =>
            import(/* webpackChunkName: "fail" */ '@/views/exception/404')
},
{
  path: '/preview',
  component: () =>
            import(/* webpackChunkName: "fail" */ '@/views/isolarerp/certificate/modules/PDFPrview')
}
];
