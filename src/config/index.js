/** init domain config */
// import Vue from 'vue'
// 设置全局API_BASE_URL
Vue.prototype.API_BASE_URL = process.env.VUE_APP_API_BASE_URL;
window._CONFIG['domianURL'] = Vue.prototype.API_BASE_URL;
// 单点登录地址
window._CONFIG['casPrefixUrl'] = process.env.VUE_APP_CAS_BASE_URL;
window._CONFIG['staticDomainURL'] = Vue.prototype.API_BASE_URL + '/sys/common/static';
window._CONFIG['pdfDomainURL'] = Vue.prototype.API_BASE_URL + '/sys/common/pdf/pdfPreviewIframe';
window._CONFIG['system'] = '/work';
const colorList = [{
  title: '嫣红',
  name: 'red',
  color: '#e54d42'
},
{
  title: '明黄',
  name: 'yellow',
  color: '#fbbd08'
},
{
  title: '橄榄',
  name: 'olive',
  color: '#8dc63f'
},
{
  title: '森绿',
  name: 'green',
  color: '#39b54a'
},
{
  title: '天青',
  name: 'cyan',
  color: '#1cbbb4'
},
{
  title: '海蓝',
  name: 'blue',
  color: '#0081ff'
},
{
  title: '姹紫',
  name: 'purple',
  color: '#6739b6'
},
{
  title: '木槿',
  name: 'mauve',
  color: '#9c26b0'
},
{
  title: '桃粉',
  name: 'pink',
  color: '#e03997'
},
{
  title: '棕褐',
  name: 'brown',
  color: '#a5673f'
},
{
  title: '玄灰',
  name: 'grey',
  color: '#8799a3'
},
{
  title: '草灰',
  name: 'gray',
  color: '#aaaaaa'
},
{
  title: '墨黑',
  name: 'black',
  color: '#333333'
}, {
  title: '桔橙',
  name: 'orange',
  color: '#f37b1d'
}
];
export default colorList;
