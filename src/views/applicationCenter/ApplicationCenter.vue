<template>
  <div class="bottom-auto-height">
    <a-card  class="bottom" style="overflow: auto" :bodyStyle="{ padding: '24px 0' }">
      <div v-if="menus && menus.length > 0">
        <div v-for="(item, index) in menus" :key="item.path" class="solaryeye-menu">
          <div class="menu-title">
            <div class="menu-title-before"></div>
            {{ item.meta.title }}
            <a-icon
              type="double-right"
              @click="handleClick(index)"
              :class="item.isShow ? 'solar-eye-arrow-down' : 'solar-eye-arrow-up'"
            />
          </div>
          <div class="menu-content">
            <div
              v-for="(child, childIndex) in item.children"
              :key="child.path"
              class="menu-child bg"
              :class="[isIncludeRouteName(child.meta.title, index), 'bg' + childIndex % 7]"
              v-show="item.isShow && !child.hidden"
              @click="openNewPath(item, child)"
            >
            <!-- {{item.hidden}}
              <a-icon
                :type="child.meta.icon"
                v-if="!isSolareyeCustom(child.meta.icon)"
                style="font-size: 48px; color: white;margin-top: -18px"
              ></a-icon>
              <svg-icon
                v-else
                :iconClass="isSolareyeCustom(child.meta.icon, true)"
                style="font-size: 48px; margin-top: -18px"
              ></svg-icon>
              <div class="child-div"
                >{{ child.meta.title }}</div
              > -->
              <img class="img-top" :src="require(`@/assets/images/application/${getIcon(child.meta.icon)}.png`)" />
              <div class="child-div"
                >{{ child.meta.title }}</div
              >
              <img class="img-bottom" :src="require(`@/assets/images/application/${getIcon(child.meta.icon)}_bottom.png`)"/>
            </div>
          </div>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script>
const innerHeight = window.innerHeight - 60 - 40 - 24;
export default {
  name: 'ApplicationCenter',
  data () {
    return {
      menus: [],
      routeName: '',
      height: innerHeight - 96
    };
  },
  created () {
    this.secondMenu();
  },
  watch: {},
  computed: {
    permissionMenuList () {
      return this.$store.state.user.permissionList;
    }
  },

  methods: {
    secondMenu () {
      this.permissionMenuList.forEach((item) => {
        if (item.path === '/application') {
          this.menus = item.children;
          this.menus = this.menus.filter((child) => {
            return child.path != '/application';
          });
          this.fillMenuList();
        }
      });
    },
    handleClick (index) {
      this.menus[index].isShow = !this.menus[index].isShow;
    },
    fillMenuList () {
      this.menus.map((item, index) => {
        this.$set(this.menus[index], 'isShow', true);
        // if (item.children && item.children.length > 0) {
        //   item.children.map((child, childIndex) => {
        //     child.imgSrc = require('@/assets/images/application/bg' + (childIndex % 7) + '.png')
        //   })
        // } else {
        //   item.imgSrc = require('@/assets/images/application/bg' + (index % 7) + '.png')
        // }
      });
    },
    /**
     *  判断是否是自定义的svg 图标
     *  parmas {string} icon 图标名称
     *  iconName {Boolean} true return svg 的name，false ，返回是否是自定义的svg 图标
     */
    isSolareyeCustom (icon, iconName) {
      if (!icon) {
        return null;
      }
      if (iconName) {
        return icon.split('solareye-custom-')[1];
      } else {
        return icon.indexOf('solareye-custom-') > -1;
      }
    },
    getIcon (iconName) {
      return iconName || 'data';
    },
    isIncludeRouteName (name, index) {
      let isIndexOf = this.routeName && name.indexOf(this.routeName) != -1;
      if (isIndexOf) {
        this.menus[index].isShow = true;
      }
      return isIndexOf ? 'menu-selected' : '';
    },
    /**
     *  打开新的页面
     * params {item}
     * params child
     */
    openNewPath (item, child) {
      let isIframe = child.meta.componentName === 'IframePageView' || child.meta.componentName === 'IframeView';
      let path = '';
      if (isIframe) {
        path = item.path + '/' + child.path;
      } else {
        path = child.path;
      }
      this.$router.push(path);
    }
  }
};
</script>

<style lang="less" scoped>

  .search-input {
    width: 260px;
  }
  .bgColor(@r,@g,@b) {
    background:rgba(@r,@g,@b, 0.3)
  }
  .border-color(@r,@g,@b) {
    border: 1px solid rgba(@r,@g,@b,.3)
  }
  .solaryeye-menu {
    // padding: 0 0 24px;
    &:last-child {
      .menu-content {
        border-bottom: transparent;
      }
    }
    .menu-title {
      padding: 16px 24px;
      line-height: 22px;
      font-weight: 500;
      font-size: 16px;
      display: flex;
      align-items: center;
      border-bottom: 1px solid #e8e8e8;
      font-weight: 600;
      i {
        margin: 0px 16px;
      }
      .solar-eye-arrow-down {
        transform: rotate(90deg);
      }
      .solar-eye-arrow-up {
        transform: rotate(-90deg);
      }
    }
    .menu-title-before {
      margin-right: 16px;
      height: 16px;
      border-radius:  0px 2px 2px 0px;
    }
    .menu-content {
     // padding: 0 8px 4px;
      display: flex;
      flex-direction: row;
      flex-flow: wrap;
      margin-top: 24px;
    }

    &:not(:first-child) {
      .menu-title {
        padding-top: 16px;
      }
    }

    .menu-child {
      display: flex;
      white-space: pre-wrap;
     width: 347px;
      height: 211px;
      border-radius: 4px;
      box-sizing: border-box;
      margin: 0 0 24px 24px;
      cursor: pointer;
      align-items: center;
       flex-direction: column;
      align-items: center;
      justify-content: center;
      position:relative;
      .img-top {
        width:90px;
        height: 90px;
       object-fit: cover;
      }
      .child-div {
        font-weight: 600;
        font-family: PingFangSC-Medium;
        font-size: 24px;
        color: #3D3D3D;
      }
       .img-bottom {
        position: absolute;
        right: 0;
        bottom: 0;

       }
    }
    .bg {
      // background-image: url("../../assets/images/application/bg.png");

      &.bg0 {
        .bgColor(214,244,255);
        .border-color(114,173,196);
      }
      &.bg1 {
       border: 1px solid rgba(202,149,158,0.3);
       .bgColor(255,216,221);
      }
      &.bg2 {
        .border-color(206,168,136);
        .bgColor(255,231,221);
      }
      &.bg3 {
        .bgColor(204,247,220);
        .border-color(140,205,165);
      }
      &.bg4 {
         .bgColor(218,227,255);
         .border-color(150,170,223);
      }
      &.bg5 {
         .bgColor(238,228,255);
         .border-color(167,160,218);
      }
      &.bg6 {
        .bgColor(252, 255, 228);
        .border-color(152, 172, 17);
      }
    }

    .menu-child:hover {
      transform: translateY(-5px);
    }

    .menu-child.menu-selected {
      transform: translateY(-5px);
    }
  }
  .solar-eye-dark {
    .menu-child {
      .child-div {
        color: #fff;
      }
    }
    .menu-title {
      border-bottom-color: #51637C;
    }
   }
  </style>
