<!-- 车辆登记 -->
<template>
  <div class="page-header-index-wide" id="carList">
    <a-spin :spinning="pageloading">
      <a-row :gutter="24">
        <a-col :xs="24" :lg=" 24">
          <div id="car_list" class="solar-eye-search-model">
            <a-row :gutter="24" class="solar-eye-search-content">
              <a-col :span="24">
                <div class="search-item" style="height: unset;">
                  <span class="search-label">快速查询</span>
                  <a-checkbox-group v-model="allStatusList"  @change="statusClick">
                    <a-checkbox value="1">保险即将到期({{ deadStatus1}})</a-checkbox>
                    <a-checkbox value="2">保险已到期({{ deadStatus2}})</a-checkbox>
                    <a-checkbox value="3" >租车即将到期({{ deadStatus3}})</a-checkbox>
                    <a-checkbox value="4" >租车已到期({{ deadStatus4}})</a-checkbox>
                    <a-checkbox value="5">未保养里程超7500公里({{ deadStatus5}})</a-checkbox>
                  </a-checkbox-group>
                </div>
              </a-col>
              <a-col :xxl="8" :xl="12" :md="12">
                <role-tree-select :isCarTree='true' @change="roleTreeChange" ref="planRoleTree" :excludeHy="excludeHy"></role-tree-select>
              </a-col>
              <a-col :xxl="4" :xl="6" :md="12">
                <div class="search-item">
                  <span class="search-label">使用归属</span>
                  <a-select v-model="carUseBelong" allowClear placeholder="请选择" size="default" style="width: 100%;">
                    <a-select-option v-for="item in dictMap.car_user_belong" :key="item.dataValue" :value="item.dataValue">
                      {{item.dataLable}}
                    </a-select-option>
                  </a-select>
                </div>
              </a-col>
              <a-col :xxl="4" :xl="6" :md="12">
                <div class="search-item">
                  <span class="search-label">车牌号码</span>
                  <a-input v-model="carNum" @blur="carNum = $trim($event)" allowClear size="default"
                           placeholder="请输入车牌号码" style="width: 100%;"></a-input>
                </div>
              </a-col>
              <a-col :xxl="4" :xl="6" :md="12">
                <div class="search-item">
                  <span class="search-label">车辆状态</span>
                  <a-select v-model="carStatus" class="multiple_select_com" mode='multiple' :maxTagCount="1"
                            allowClear placeholder="请选择" size="default" style="width: 100%;">
                    <a-select-option v-for="item in dictMap.car_status" :key="item.dataValue" :value="item.dataValue">
                      {{item.dataLable}}
                    </a-select-option>
                  </a-select>
                </div>
              </a-col>
              <template v-if="toggleSearchStatus">
                <a-col :xxl="4" :xl="6" :md="12">
                  <div class="search-item">
                    <span class="search-label">品牌/型号</span>
                    <a-input v-model="makeModel" @blur="makeModel = $trim($event)" allowClear size="default"
                             placeholder="请输入品牌/型号" style="width: 100%;"></a-input>
                  </div>
                </a-col>
                <a-col :xxl="4" :xl="6" :md="12">
                  <div class="search-item">
                    <span class="search-label">车辆所有</span>
                    <a-select v-model="carBelong" class="multiple_select_com" mode='multiple' :maxTagCount="1" allowClear
                              placeholder="请选择" size="default" style="width: 100%;">
                      <a-select-option v-for="item in dictMap.car_belong" :key="item.dataValue" :value="item.dataValue">
                        {{item.dataLable}}
                      </a-select-option>
                    </a-select>
                  </div>
                </a-col>
                <a-col :xxl="4" :xl="6" :md="12">
                  <div class="search-item">
                    <span class="search-label">车型</span>
                    <a-select v-model="carModel" class="multiple_select_com" mode='multiple' :maxTagCount="1" allowClear
                              placeholder="请选择" size="default" style="width: 100%;">
                     <a-select-option v-for="(option,index) in dictMap.car_model" :key="index" :value="option.dataValue">
                      {{option.dataLable}}
                    </a-select-option>
                    </a-select>
                  </div>
                </a-col>
                 <a-col :xxl="4" :xl="6" :md="12">
                  <div class="search-item">
                    <span class="search-label">动力类型</span>
                    <a-select v-model="powerType" class="multiple_select_com" mode='multiple' :maxTagCount="1" allowClear
                              placeholder="请选择" size="default" style="width: 100%;">
                      <a-select-option v-for="item in dictMap.car_power_type" :key="item.dataValue" :value="item.dataValue">
                        {{item.dataLable}}
                      </a-select-option>
                    </a-select>
                  </div>
                </a-col>
              </template>
              <a-col :xxl="4" :xl="6" :md="8">
                <div class="search-item">
                  <throttle-button label="查询" perms="92010610410101" @click="pageChange(1)" />
                  <span class="com-color" @click="handleToggleSearch">
                    {{ toggleSearchStatus ? "收起" : "展开" }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                  </span>
                </div>
              </a-col>
            </a-row>
          </div>
          <div class="solar-eye-gap"></div>
          <div class="solar-eye-main-content">
            <div class="operation-btn">
              <a-dropdown>
                <a class="ant-dropdown-link" @click="e => e.preventDefault()">筛选列
                  <a-icon type="form" />
                </a>
                <a-menu slot="overlay">
                  <a-checkbox-group v-model="checkColumn" @change="refreshColumn">
                    <a-checkbox class="drop-down" v-for="(column,index) in columns" :key="index" :value="column.field">
                      {{column.title}}
                    </a-checkbox>
                  </a-checkbox-group>
                </a-menu>
              </a-dropdown>
              <erp-button label="车辆登记" perms="92010610410102" size="default" class="solar-eye-btn-primary" @click="add">
              </erp-button>
              <erp-button label="删除" perms="92010610410105" size="default" type="primary" :disabled="delDisabled"
                @click="delInfo"></erp-button>
                <a-dropdown v-if="isExit" overlayClassName="more_btn">
                  <a-menu slot="overlay">
                    <a-menu-item key="1" v-has="'92010610410106'"><a-spin :spinning="car"> <div class="menu_div"
                      @click="exportExcelEvent('car','100011', getSearchData())">车辆台账</div></a-spin> </a-menu-item>
                    <a-menu-item key="2" v-has="'92010610410121'"><a-spin :spinning="insurance">  <div class="menu_div"
                      @click="exportExcelEvent('insurance','100012',getSearchData())">保险记录</div></a-spin> </a-menu-item>
                    <a-menu-item key="3" v-has="'92010610410122'"><a-spin :spinning="illegal"><div class="menu_div"
                      @click="exportExcelEvent('illegal','100013',getSearchData())">违章记录</div></a-spin> </a-menu-item>
                  </a-menu>
                  <a-button style="margin-left: 8px"> 导出 <a-icon type="down" /> </a-button>
                </a-dropdown>
                <template v-else>
                  <a-button  :loading="car" v-has="'92010610410106'"
                      @click="exportExcelEvent('car','100011', getSearchData())">车辆台账</a-button>
                  <a-button  v-has="'92010610410121'" :loading="insurance"
                      @click="exportExcelEvent('insurance','100012',getSearchData())">保险记录</a-button>
                    <a-button   v-has="'92010610410122'" :loading="illegal"
                      @click="exportExcelEvent('illegal','100013',getSearchData())">违章记录</a-button>
                </template>
            </div>
            <vxe-table show-header-overflow ref="vxe-table" :data="data" height="340px" resizable align="center" border show-overflow
              highlight-hover-row size="small" highlight-current-row @current-change="currentChangeEvent"
              @checkbox-all="selectionChange" @checkbox-change="selectionChange"
              :seq-config="{startIndex: (page - 1) * size}">
              <vxe-table-column type="checkbox" fixed="left" :width="60"></vxe-table-column>
              <vxe-table-column type="seq" title="序号" :width="80" align="center" fixed='left'></vxe-table-column>
              <vxe-table-column v-for="info in columns" :fixed='info.fixed' :key="info.field" :title="info.title" :field="info.field" :visible="checkColumn.includes(info.field)"  :min-width="info.width || 120">
                <template #header="{ column }">
                  <table-sort
                    :column="column"
                    :filed="info.field"
                    @sortChange="mySortChange"
                    :downSort="downSort"
                    :upSort="upSort"
                    :sortFiled="sortFiled"
                    v-if='info.sortable'
                  />
                  <span v-else>{{info.title}}</span>
                </template>
                <template v-slot="{ row }">
                  <span v-if='info.field == "carUseBelong"'>{{getLabel(row[info.field],dictMap.car_user_belong)}}</span>
                  <span v-else>{{getLabel(row[info.field],null)}}</span>
                </template>
              </vxe-table-column>
              <vxe-table-column :visible="showHandle(perms)" field="action" title="操作" fixed="right" :width="120"
                :resizable="false" class-name="fixed-right-column-120">
                <template v-slot="{ row }">
                  <erp-button title="编辑" perms="92010610410103" size="default" type="primary" icon="edit"
                    @click="editInfo(row)"></erp-button>
                  <erp-button title="详情" perms="92010610410104" size="default" icon="file-text" @click="detail(row)">
                  </erp-button>
                </template>
              </vxe-table-column>
              <template v-slot:empty>
                <span>查询无数据</span>
              </template>
            </vxe-table>
            <page-pagination :pageSize="size" :current="page" :total="total" @size-change="sizeChange" />
          </div>
          <div class="solar-eye-gap"></div>
          <div class="footer-table">
            <!-- tabs 车辆使用记录、保险记录、违章记录、移交记录 -->
            <a-tabs v-model="active" @change="tabChange">
              <a-tab-pane key="use" tab="车辆使用记录">
                <use-model v-if="active == 'use'" ref="use" :carId="carId" />
              </a-tab-pane>
              <a-tab-pane key="insurance" tab="保险记录">
                <insurance-model v-if="active == 'insurance'" ref="insurance" :carId="carId" />
              </a-tab-pane>
              <a-tab-pane key="violation" tab="违章记录">
                <violation-model v-if="active == 'violation'" ref="violation" :carId="carId" />
              </a-tab-pane>
              <a-tab-pane key="move" tab="移交记录">
                <move-model v-if="active == 'move'" ref="move" :carId="carId" />
              </a-tab-pane>
            </a-tabs>
          </div>
        </a-col>
      </a-row>

      <!-- 车辆表单 -->
      <div class="right-content">
        <drawer-view ref="carRegisterForm" @cancel="closeModel" parentId="drawerViewDetail" />
      </div>
    </a-spin>
  </div>
</template>

<script>
import { getCarPropertyList, getCarPropertyCount, delCarList } from '@/api/isolarErp/asset/carlist';
import initDict from '@/mixins/initDict';
import { LeftMixin } from '@/mixins/LeftMixin';
import moment from 'moment';
import useModel from './use';
import insuranceModel from './insuranceRec/index';
import violationModel from './violationRec/index';
import moveModel from './move';
import downBigExcel from '@/mixins/downBigExcel';
import { isExistArray } from '@/utils/util';
export default {
  name: 'index',
  mixins: [initDict, LeftMixin, downBigExcel],
  components: {
    useModel,
    insuranceModel,
    violationModel,
    moveModel
  },
  data () {
    return {
      doubleRight: false,
      pageloading: false,
      isExit: isExistArray(['92010610410106', '92010610410121', '92010610410122']),
      columns: [
        {
          field: 'makeModel',
          title: '品牌/型号',
          fixed: 'left',
          width: 100
        }, {
          field: 'carNum',
          title: '车牌号码',
          fixed: 'left',
          width: 100
        },
        {
          field: 'carModelName',
          title: '车型',
          width: 60
        },
        {
          field: 'powerTypeName',
          title: '动力类型',
          width: 120
        },
        {
          field: 'isElecName',
          title: '是否纯电动',
          width: 80
        },
        {
          field: 'tutorName',
          title: '负责人'
        },
        {
          field: 'areaName',
          title: '所属区域'
        },
        {
          field: 'departName',
          title: '运维组'
        },
        {
          field: 'psaName',
          title: '所属部门/电站'
        },
        {
          field: 'psUuid',
          title: '电站身份编码'
        },
        {
          field: 'carBelongName',
          title: '车辆所有'
        },
        {
          field: 'carUseBelong',
          title: '使用归属'
        },
        {
          field: 'buyDate',
          title: '购车日期'
        }, {
          field: 'payDate',
          title: '交付日期'
        },
        {
          field: 'registerDate',
          title: '注册日期'
        }, {
          field: 'approvalCycleName',
          title: '年审周期/次'
        },
        {
          field: 'carStatusName',
          title: '车辆状态'
        }, {
          field: 'endMonthMileage',
          title: '总行驶里程数(km)'
        },
        {
          field: 'noMaintenanceMileage',
          title: '未保养里程(km)'
        },

        {
          field: 'insuranceEndDate',
          title: '保险到期日'
        },
        {
          field: 'oldCarNum',
          title: '旧车牌号码'
        },
        {
          field: 'propertyNo',
          title: '资产编号'
        },
        {
          field: 'leaseStartDate',
          title: '租赁开始日'
        },
        {
          field: 'leaseEndDate',
          title: '租赁结束日'
        },
        {
          field: 'leaseReturnDate',
          title: '归还日期'
        },
        {
          field: 'financePropertyNo',
          title: '财务资产编号'
        },
        {
          field: 'vehicleConfigurationPlan',
          title: '车辆配置计划',
          width: 160
        },
        {
          field: 'remark',
          title: '备注'
        },
        {
          field: 'createUser',
          title: '创建人'
        },
        {
          field: 'createTime',
          title: '创建时间',
          sortable: true
        },
        {
          field: 'updateUser',
          title: '修改人'
        },

        {
          field: 'updateTime',
          title: '修改时间',
          sortable: true
        }
      ],
      illegal: false,
      insurance: false,
      car: false,
      perms: '92010610410103,92010610410104',
      checkColumn: [
        'areaName', 'psaName', 'psUuid', 'tutorName', 'carBelongName', 'powerTypeName', 'makeModel', 'carNum', 'carModelName',
        'isElecName',
        'buyDate', 'payDate', 'registerDate',
        'approvalCycleName', 'carStatusName', 'endMonthMileage', 'insuranceEndDate', 'oldCarNum',
        'propertyNo', 'vehicleConfigurationPlan',
        'leaseStartDate', 'leaseEndDate', 'leaseReturnDate', 'financePropertyNo', 'remark', 'createUser',
        'createTime', 'updateUser', 'updateTime', 'carUseBelong', 'noMaintenanceMileage', 'departName'
      ], // 筛选列
      delDisabled: true, // 删除按钮
      // 车辆状态：
      checkedAllStatus: false,
      defAll: ['1', '2', '3', '4', '5'],
      allStatusList: [],
      deadStatus1: 0,
      deadStatus2: 0,
      deadStatus3: 0,
      deadStatus4: 0,
      deadStatus5: 0,
      carBelong: [],
      powerType: [],
      carModel: [],
      makeModel: '',
      carNum: '',
      carStatus: [],
      // 表格相关参数
      data: [],
      // 分页参数
      total: 0,
      page: 1,
      size: 10,
      // 选中的数据
      multipleTable: [],
      carId: '',
      psaName: '',
      // 车辆登记
      param: {
        type: null, // 1:新增 2:编辑 3:详情,
        title: null,
        carId: null,
        open: false
      },
      active: 'use',
      recordHeight: '',
      toggleSearchStatus: false,
      carUseBelong: undefined // 使用归属
    };
  },
  created () {
    // 加载数据字典
    this.getDictMap('car_belong,car_model,yes_no_emp,approval_cycle,car_status,car_user_belong,car_power_type');
  },

  methods: {
    moment,
    // 多选框选中事件
    statusClick () {
      if (this.allStatusList.length > 1) {
        this.allStatusList.splice(0, 1);
      }
      this.pageChange(1);
    },
    refreshColumn () {
      this.$nextTick(() => {
        let $table = this.$refs['vxe-table'];
        $table && $table.refreshColumn();
      });
    },
    getSearchData () {
      let self = this;
      return {
        'curPage': self.page,
        'size': self.size,
        'treeNodeId': self.selectId,
        'carBelong': self.carBelong.join(','),
        powerType: self.powerType.join(','),
        carModel: self.carModel.join(','),
        'makeModel': self.makeModel,
        'carNum': self.carNum,
        'carStatus': self.carStatus.join(','),
        'Sts': self.allStatusList,
        deptCode: this.deptCode,
        psaIds: this.psaIds,
        sortField: this.sortFiled,
        sortKind: this.sortKind,
        carUseBelong: this.carUseBelong
      };
    },
    // 查询表格列表数据
    async queryData () {
      let self = this;
      self.pageloading = true;
      let $table = this.$refs['vxe-table'];
      if ($table) {
        // await $table.clearScroll();
        $table.clearCheckboxRow(); // 清除选中
      }
      self.multipleTable = [];
      self.delDisabled = true;
      let requestMap = this.getSearchData();
      getCarPropertyList(requestMap).then(res => {
        self.pageloading = false;
        let data = (res.result_code == '1' ? res.result_data.rows : []);
        data.forEach((item, index) => {
          item.index = (self.page - 1) * self.size + index + 1;
        });
        self.data = data;
        if (data.length != 0) {
          self.$refs['vxe-table'].setCurrentRow(data[0]);
          self.carId = data[0].carId;
          self.psaName = data[0].psaName;
        } else {
          self.carId = '';
          self.psaName = '';
        }
        self.tabChange();
        self.total = (res.result_code == '1' ? res.result_data.total : 0);
      }).catch(() => {
        self.pageloading = false;
      });
      self.countStsData(requestMap);
    },
    // 统计各状态数据量
    countStsData (map) {
      let obj = Object.assign({}, map);
      obj.Sts = this.defAll;
      getCarPropertyCount(obj).then(res => {
        let data = (res.result_code == '1' ? res.result_data : '');
        this.dealData('1', data.insuranceSoonDateCount); // 保险即将到期
        this.dealData('2', data.insuranceEndDateCount);// 保险已到期
        this.dealData('3', data.rentCarSoonDateCount);// 租车即将到期
        this.dealData('4', data.rentCarEndDateCount);// 租车已到期
        this.dealData('5', data.noCareCarCount);// 超7500公里
      });
    },
    /**
     * 处理数据
     * params {num} 状态，
     * params {count} 后端的返回
    */
    // 处理数据
    dealData (num, count) {
      this['deadStatus' + num] = count || 0;
    },
    // 行选中change事件
    currentChangeEvent ({
      row
    }) {
      this.carId = row.carId;
      this.psaName = row.psaName;
      this.tabChange();
    },
    // tabs change事件
    tabChange () {
      this.$nextTick(() => {
        if (this.active == 'use') {
          this.$refs['use'].init();
        } else if (this.active == 'insurance') {
          this.$refs['insurance'].init();
        } else if (this.active == 'violation') {
          this.$refs['violation'].init();
        } else if (this.active == 'move') {
          this.$refs['move'].init();
        }
      });
    },
    // 车辆登记
    add () {
      this.dealScroll();
      this.$nextTick(() => {
        this.$refs.carRegisterForm.init('1', {
          carId: null
        }, '/operations/materialsManage/carList/carForm');
      });
    },
    dealScroll (flag) {
      let mainParentElement = document.getElementsByClassName('main-parent');
      if (mainParentElement) {
        mainParentElement[0].style.overflow = flag ? 'hidden auto' : 'hidden';
        if (!flag) {
          this.recordHeight = mainParentElement[0].offsetTop;
          document.getElementsByClassName('main-parent')[0].scrollTop = 0;
        }
        if (flag) {
          document.getElementsByClassName('main-parent')[0].scrollTop = this.recordHeight;
        }
      }
    },
    // 编辑
    editInfo (row) {
      this.$refs.carRegisterForm.init('2', row, '/operations/materialsManage/carList/carForm');
      this.dealScroll();
    },
    // 删除
    delInfo () {
      const self = this;
      if (self.multipleTable.length > 0) {
        self.$confirm({
          title: '确定要删除吗?',
          okText: '确定',
          cancelText: '取消',
          onOk () {
            let carIds = [];
            self.multipleTable.forEach((column, index) => {
              carIds.push(column.carId);
            });
            let requestMap = {
              'carIds': carIds
            };
            delCarList(requestMap).then(res => {
              if (res.result_code == '1') {
                self.$message.success('删除成功');
                self.pageChange(1);
              }
            });
          }
        });
      } else {
        self.$message.warning('请至少选择一条数据');
      }
    },
    // 详情
    detail (row) {
      this.$refs.carRegisterForm.init('3', row, '/operations/materialsManage/carList/carForm');
      this.dealScroll();
    },
    // 关闭弹窗
    closeModel () {
      this.dealScroll(true);
      this.queryData();
    },
    // 行选中
    selectionChange (val) {
      this.multipleTable = val.records; ;
      if (this.multipleTable.length > 0) {
        this.delDisabled = false;
      } else {
        this.delDisabled = true;
      }
    },
    // 分页事件
    pageChange (e) {
      this.page = e || 1;
      this.queryData();
    },
    // 分页事件
    sizeChange (current, pageSize) {
      this.page = current;
      this.size = pageSize;
      this.queryData();
    },
    handleToggleSearch () { // 展开收起
      this.toggleSearchStatus = !this.toggleSearchStatus;
    }
  }
};
</script>

<style lang="less" scoped>
  .page-header-index-wide {
    height: 100%;
    overflow: hidden auto;
  }

  .car-table {
    width: 100%;
    padding: 15px 15px 0 15px;
    margin-top: 12px;
  }

  .footer-table {
    overflow: auto;
    position: relative;
    z-index: 8;
    padding: 0 15px 15px 15px;
    min-height: 300px;

    :deep(.ant-spin-container) {
      margin-top: 10px;
    }
  }
  // 当前行高亮（费解：之前是怎么高亮的？）
  :deep(.vxe-table .vxe-body--row.row--current) {
    background-color: #e6f7ff!important;
  }
</style>
<style lang="less" scoped>
:deep(.vxe-table.size--small .vxe-header--column:not(.col--ellipsis)) {
  padding: 0;
}

:deep(.vxe-table .vxe-header--row .vxe-cell) {
  padding: 0;
}

:deep(.vxe-table .vxe-header--column) {
  line-height: 20px;
}

.sort-head {
  cursor: pointer;
  padding: 2px 0 10px;
}

:deep(.sort-head span) {
  display: inline-block;
  margin-top: 10px;
}

:deep(.up-icon) {
  top: 12px;
}

:deep(.down-icon) {
  top: 21px;
}

.sort-head:hover {
  background: #d8d8d8;
}

.solar-eye-dark {
  .sort-head:hover {
    background: #0e1e36;
  }
}

</style>
<style lang="less">
.more_btn {
  .ant-dropdown-menu-item {
    padding: 0;
    .menu_div {
      padding: 5px 12px;
    }
  }
}
</style>
