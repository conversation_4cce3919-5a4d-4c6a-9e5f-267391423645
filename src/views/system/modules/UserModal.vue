<template>
  <a-drawer :title="title" :width="drawerWidth" placement="right" :closable="true" :maskClosable="false"
    @close="handleCancel" :visible="visible" style="height: 100%;overflow: auto;padding-bottom: 53px;">

    <template slot="title">
      <div style="width: 100%;">
        <span>{{ title }}</span>
        <span style="display:inline-block;width:calc(100% - 51px);padding-right:10px;text-align: right">
          <a-button @click="toggleScreen" icon="appstore" style="height:20px;width:20px;border:0px"></a-button>
        </span>
      </div>
    </template>

    <a-spin :spinning="confirmLoading">
      <a-form :form="form" ref="userForm">
         <a-form-item label="用户姓名" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input placeholder="请输入用户姓名" v-decorator.trim="[ 'realname', validatorRules.realname]" @blur="getAccount" :disabled="disableSubmit"/>
        </a-form-item>
          <a-form-item label="用户账号" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input type="text" autocomplete="off" placeholder="请输入用户账号" disabled
            v-decorator.trim="[ 'username', validatorRules.username]" :readOnly="!!model.id" />
        </a-form-item>
        <template v-if="!model.id && visible">
          <a-form-item label="登录密码" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input-password autocomplete="new-password" placeholder="请输入登录密码"
              v-decorator="[ 'password',validatorRules.password]" />
          </a-form-item>
          <a-form-item label="确认密码" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input-password autocomplete="new-password"
              placeholder="请重新输入登录密码" v-decorator="[ 'confirmpassword', validatorRules.confirmpassword]" />
          </a-form-item>
        </template>
        <a-form-item label="工号" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input placeholder="请输入工号" v-decorator.trim="[ 'workNo', validatorRules.workNo]" :disabled="disableSubmit"/>
        </a-form-item>
        <a-form-item label="职位" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-select
              v-decorator="[ 'post', {rules: [{ required: true, message: '请选择职位' }]} ]"
              :disabled="disableSubmit"
            >
              <a-select-option
                v-for="item in dictMap.position"
                :key="item.dataId"
                :value="item.codeValue"
                :title="item.dispName"
                allowClear
              >
                {{ item.dispName }}
              </a-select-option>
            </a-select>
        </a-form-item>
        <a-form-item label="功能角色" :labelCol="labelCol" :wrapperCol="wrapperCol" v-show="!roleDisabled">
          <a-select mode="multiple" :disabled="departDisabled || disableSubmit" style="width: 100%" placeholder="请选择用户角色"
            optionFilterProp="children"
            v-decorator="[ 'selectedRole', {rules: [{ required: true, message: '请选择角色' }]} ]"
            :getPopupContainer="(target) => target.parentNode">
            <a-select-option v-for="(role,roleindex) in roleList" :key="roleindex.toString()" :value="role.id">
              {{ role.roleName }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <!--部门分配 - 单选-->
        <a-form-item label="部门分配" :labelCol="labelCol" :wrapperCol="wrapperCol" v-show="!departDisabled">
          <dep-tree-select
            v-decorator="[ 'selecteddeparts',  {initialValue: '', rules: [{ required: true, message: '请选择部门' }] } ]"
            placeholder="请选择部门" :all="true" :isExtra="true" style="width:100%;" :disabled="disableSubmit"
            @change="changeEvent"
             />
        </a-form-item>

        <!--数据角色分配 - 多选选-->
        <a-form-item label="数据权限" :labelCol="labelCol" :wrapperCol="wrapperCol">
        <a-tree-select
        v-decorator="['dataRoles', {initialValue: null, rules: [{ required: true, message: '请选择数据权限'}]}]"
          :tree-data="treeData"
          tree-checkable
          :replaceFields="{title: 'name', key: 'id',value:'id'}"
          allow-clear
          :treeCheckStrictly="true"
          :disabled="disableSubmit || disabledRoles"
          multiple
          search-placeholder="Please select"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          treeNodeFilterProp="title"
        />
          <!-- <dataRoleTreeSelect :disabled="disableSubmit" v-decorator="['dataRoles', {initialValue: null, rules: [{ required: true, message: '请选择数据角色'}]}]"></dataRoleTreeSelect> -->
        </a-form-item>
        <!--租户分配 - 可多选-->
        <a-form-item v-show="!departDisabled && isAdmin" label="租户分配" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-select style="width: 100%" placeholder="请选择租户分配" mode="multiple" maxTagCount="1" :disabled="disableSubmit"
            v-decorator="[ 'currentTenant', departDisabled ? [] : validatorRules.currentTenant]">
            <a-select-option v-for="(item, index) in tenantList" :key="index" :value="item.id">
              {{ item.name }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="手机号码" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input placeholder="请输入手机号码" :disabled="isDisabledAuth('user:form:phone') || disableSubmit"
            v-decorator="[ 'phone', validatorRules.phone]"/>
        </a-form-item>

      </a-form>
    </a-spin>

    <div class="drawer-bootom-button" v-show="!disableSubmit">
      <a-popconfirm title="确定放弃编辑？" @confirm="handleCancel" okText="确定" cancelText="取消">
        <a-button :disabled="confirmLoading" style="margin-right: .8rem">取消</a-button>
      </a-popconfirm>
      <a-button @click="handleSubmit" type="primary" :loading="confirmLoading" :disabled="confirmLoading">提交</a-button>
    </div>
  </a-drawer>
</template>

<script>
import pick from 'lodash.pick';
import initDict from '@/mixins/initDict';
import moment from 'moment';
import {
  queryDeptTree,
  addUser,
  editUser,
  queryUserRole,
  queryUserDataRole,
  queryall,
  queryUserRole2,
  duplicateCheck
} from '@/api/api';
import {
  ACCESS_TOKEN,
  USER_NAME,
  TENANT_ID
} from '@/store/mutation-types';
import {
  getAction
} from '@/api/manage';

import {
  disabledAuthFilter
} from '@/utils/authFilter';
import { aesEncrypt } from '@/utils/verify';
export default {
  name: 'UserModal',
  mixins: [initDict],
  data () {
    return {
      editFlag: false,
      departDisabled: false, // 是否是我的部门调用该页面
      roleDisabled: false, // 是否是角色维护调用该页面
      modalWidth: 800,
      drawerWidth: 700,
      modaltoggleFlag: true,
      selectedDepartKeys: [], // 保存用户选择部门id
      checkedDepartKeys: [],
      checkedDepartNames: [], // 保存部门的名称 =>title
      checkedDepartNameString: '', // 保存部门的名称 =>title
      resultDepartOptions: [],
      userId: '', // 保存用户id
      disableSubmit: false,
      dateFormat: 'YYYY-MM-DD',
      disabledRoles: true,
      validatorRules: {
        username: {
          rules: [{
            required: true,
            message: '请输入用户账号!'
          }, {
            validator: this.validateUsername
          }]
        },
        password: {
          rules: [{
            required: true,
            message: '请输入登录密码!'
          }, {
            validator: this.validateToNextPassword
          }]
        },
        confirmpassword: {
          rules: [{
            required: true,
            message: '请重新输入登录密码!'
          }, {
            validator: this.compareToFirstPassword
          }]
        },
        realname: {
          rules: [{
            required: true,
            message: '请输入用户名称!'
          }]
        },
        phone: {
          rules: [{
            validator: this.validatePhone
          }, {
            required: true,
            message: '请输入手机号!'
          }]
        },
        email: {
          rules: [{
            validator: this.validateEmail
          }]
        },
        roles: {},
        workNo: {
          rules: [{
            required: true,
            message: '请输入工号'
          },
          {
            validator: this.validateWorkNo
          }
          ]
        },
        currentTenant: {
          rules: [{
            required: true,
            message: '请选择租户!'
          } ]
        },
        telephone: {
          rules: [{
            pattern: /^0\d{2,3}-[1-9]\d{6,7}$/,
            message: '请输入正确的座机号码'
          }]
        }
      },
      departIdShow: false,
      departIds: [], // 负责部门id
      title: '操作',
      visible: false,
      model: {},
      roleList: [],
      selectedRole: [],
      post: '',
      selectedRoles: [],
      labelCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 5
        }
      },
      wrapperCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 16
        }
      },
      uploadLoading: false,
      confirmLoading: false,
      isAdmin: false,
      headers: {},
      form: this.$form.createForm(this),
      picUrl: '',
      url: {
        fileUpload: window._CONFIG['domianURL'] + '/sys/common/upload',
        userWithDepart: '/sys/user/userDepartList', // 引入为指定用户查看部门信息需要的url
        userId: '/sys/user/generateUserId', // 引入生成添加用户情况下的url
        syncUserByUserName: '/act/process/extActProcess/doSyncUserByUserName', // 同步用户到工作流
        queryTenantList: '/sys/tenant/queryList'
      },
      identity: '1',
      fileList: [],
      tenantList: [],
      currentTenant: [],
      selecteddeparts: '',
      dataRoles: null,
      treeData: [],
      backTreeData: []
    };
  },
  created () {
    const token = Vue.ls.get(ACCESS_TOKEN);
    this.headers = {
      'X-Access-Token': token
    };
    this.initTenantList();
    let userName = Vue.ls.get(USER_NAME);
    this.isAdmin = (userName == 'admin');
    this.loadTree();
  },
  computed: {
    uploadAction: function () {
      return this.url.fileUpload;
    }
  },
  mounted () {
    this.getDictMap('position');
  },
  methods: {
    loadTree () {
      var that = this;
      that.treeData = [];
      that.backTreeData = [];
      // 0表示不显示关闭的，1表示显示
      queryDeptTree({ 'show': '1' }).then((res) => {
        let result = Array.isArray(res.result) ? res.result : [res.result];
        that.treeData = result;
        that.backTreeData = result;
      }).catch(() => {
      });
    },
    isDisabledAuth (code) {
      return disabledAuthFilter(code);
    },
    initTenantList () {
      getAction(this.url.queryTenantList).then(res => {
        if (res.success) {
          this.tenantList = res.result;
        }
      });
    },
    getAccount () {
      let realname = this.form.getFieldValue('realname');
      if (!realname) return;
      if (!this.model.id) {
        getAction('/sys/user/getAvailableUsernameByRealName', { realName: realname }).then(res => {
          if (res.success) {
            this.form.setFieldsValue({ 'username': res.result ? res.result.username : '' });
          } else {
            this.form.setFieldsValue({ 'username': '' });
            this.$message.warning(res.message);
          }
        });
      }
    },
    // 窗口最大化切换
    toggleScreen () {
      if (this.modaltoggleFlag) {
        this.modalWidth = window.innerWidth;
      } else {
        this.modalWidth = 800;
      }
      this.modaltoggleFlag = !this.modaltoggleFlag;
    },
    initialRoleList () {
      queryall().then((res) => {
        if (res.success) {
          this.roleList = res.result;
        } else {
          console.log(res.message);
        }
      });
    },
    /*
        查詢用戶數據角色
      */
    loadUserDataRole (userid) {
      queryUserDataRole({ userid: userid }).then((res) => {
        this.form.setFieldsValue({
          dataRoles: Array.isArray(res.result) ? res.result : res.result
        });
      });
    },
    loadUserRoles (userid) {
      queryUserRole({ userid: userid }).then((res) => {
        this.form.setFieldsValue({
          selectedRole: res.result
        });
      });
      queryUserRole2({ userid: userid }).then((res) => {
        this.form.setFieldsValue({
          selectedRoles: res.result
        });
      });
    },
    refresh () {
      this.selectedDepartKeys = [];
      this.checkedDepartKeys = [];
      this.checkedDepartNames = [];
      this.checkedDepartNameString = '';
      this.userId = '';
      this.resultDepartOptions = [];
      this.departId = [];
      this.departIdShow = false;
      this.currentTenant = []; // 租户
      this.selecteddeparts = ''; // 所属部门
      this.dataRoles = null;
    },
    add (currentRoleId) {
      this.picUrl = '';
      this.refresh();
      this.edit({
        activitiSync: '1',
        username: '',
        selectedRole: currentRoleId ? [currentRoleId] : [],
        selectedRoles: []
      });
    },
    edit (record) {
      this.resetScreenSize(); // 调用此方法,根据屏幕宽度自适应调整抽屉的宽度
      let that = this;
      this.editFlag = record.editFlag;
      that.initialRoleList();
      that.checkedDepartNameString = '';
      that.form.resetFields();
      if (record.hasOwnProperty('id')) {
        that.loadUserRoles(record.id);
        that.loadUserDataRole(record.id);
        setTimeout(() => {
          that.fileList = record.avatar;
        }, 5);
      }
      that.userId = record.id;
      that.visible = true;
      that.model = Object.assign({}, record);
      // 身份为上级显示负责部门，否则不显示
      if (this.model.userIdentity == '2') {
        this.identity = '2';
        this.departIdShow = true;
      } else {
        this.identity = '1';
        this.departIdShow = false;
      }
      // 调用查询用户对应的部门信息的方法
      // that.checkedDepartKeys = [];
      that.loadCheckedDeparts();
      // 非超级管理员（admin）用户创建用户数据时，直接设定当前的租户
      if (that.isAdmin) {
        if (!record.relTenantIds || record.relTenantIds.length == 0) {
          that.model.currentTenant = [];
        } else {
          that.model.currentTenant = record.relTenantIds.split(',').map(Number);
        }
      } else {
        let tenId = Vue.ls.get(TENANT_ID);
        tenId = (tenId ? Number(tenId) : '');
        that.model.currentTenant = [tenId];
      }
      that.$nextTick(() => {
        that.form.setFieldsValue(pick(this.model, 'username', 'realname', 'phone',
          'workNo', 'selectedRole', 'post', 'dataRoles', 'selecteddeparts', 'currentTenant', 'selectedRoles'));
      });
    },
    //
    loadCheckedDeparts () {
      let that = this;
      if (!that.userId) {
        return;
      }
      getAction(that.url.userWithDepart, {
        userId: that.userId
      }).then((res) => {
        if (res.success) {
          let departId = [];
          res.result.forEach(item => {
            departId.push(item.key);
          });
          that.form.setFieldsValue({
            selecteddeparts: departId.join(',')
          });
          let parentNode = that.parentTree(that.backTreeData, departId[0]);
          that.treeData = that.backTreeData.filter(item => {
            return parentNode.indexOf(item.code) > -1;
          });
          that.disabledRoles = false;
        }
      });
    },
    close () {
      this.form.resetFields();
      this.$emit('close');
      this.visible = false;
      this.disableSubmit = false;
      this.selectedRole = [];
      this.selectedRoles = [];
      this.checkedDepartNames = [];
      this.checkedDepartNameString = '';
      this.checkedDepartKeys = [];
      this.selectedDepartKeys = [];
      this.resultDepartOptions = [];
      this.departIds = [];
      this.departIdShow = false;
      this.identity = '1';
      this.fileList = [];
      this.dataRoles = null;
      this.confirmLoading = false;
    },
    moment,
    handleSubmit () {
      const that = this;
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          let arr = [];
          values.dataRoles.forEach(item => {
            arr.push(item.value);
          });
          that.confirmLoading = true;
          if (!values.birthday) {
            values.birthday = '';
          } else {
            values.birthday = values.birthday.format(this.dateFormat);
          }
          let formData = Object.assign({}, this.model, values);
          if (that.fileList != '') {
            formData.avatar = that.fileList;
          } else {
            formData.avatar = null;
          }
          formData.password = aesEncrypt(formData.password);
          formData.confirmpassword = aesEncrypt(formData.confirmpassword);
          formData.relTenantIds = values.currentTenant.length > 0 ? values.currentTenant.join(',') : '';
          formData.selectedroles = values.selectedRole.length > 0 ? values.selectedRole.join(',') : '';
          formData.selectedroles2 = values.selectedRoles && values.selectedRoles.length > 0 ? values.selectedRoles.join(',') : '';
          formData.selecteddataroles = arr.join(',');
          formData.userIdentity = this.identity;

          let obj;
          if (!this.model.id) {
            formData.id = this.userId;
            obj = addUser(formData);
          } else {
            obj = editUser(formData);
          }
          obj.then((res) => {
            that.close();
            that.$message.success(res.message);
            that.$emit('ok');
          }).catch(() => {
            that.confirmLoading = false;
            that.checkedDepartNames = [];
          });
        } else {
          that.confirmLoading = false;
          return false;
        }
      });
    },
    handleCancel () {
      this.close();
    },
    validateToNextPassword  (rule, value, callback) {
      if (value) {
        let reg = /^(?=.*[a-zA-Z])(?=.*\d)(?=.*[\x21-\x2f\x3a-\x40\x5b-\x60\x7B-\x7F])[\da-zA-Z\x21-\x2f\x3a-\x40\x5b-\x60\x7B-\x7F]{8,14}$/;
        if (!reg.test(value)) {
          callback(new Error('密码必须包含数字、字母及特殊符号，长度8-14位，不允许有空格！'));
        } else {
          const form = this.form;
          const confirmpassword = form.getFieldValue('confirmpassword');
          if (confirmpassword && value !== confirmpassword) {
            callback(new Error('两次输入的密码不一致!'));
          } else if (value == confirmpassword) {
            form.validateFields(['confirmpassword'], { force: true });
          }
          callback();
        }
      }
      callback();
    },
    compareToFirstPassword (rule, value, callback) {
      const form = this.form;
      const password = form.getFieldValue('password');
      if (value && value !== password) {
        callback(new Error('两次输入的密码不一致!'));
      } else if (value && password && value == password) {
        form.validateFields(['password'], { force: true });
      }
      callback();
    },
    validatePhone (rule, value, callback) {
      if (!value) {
        callback();
      } else {
        // update-begin--Author:kangxiaolin  Date:20190826 for：[05] 手机号不支持199号码段--------------------
        // 2022 4-9 修改为人员档案一样的手机号校验
        if (new RegExp(/^[1][3|4|5|6|7|8|9][0-9]{9}$/).test(value)) {
          // update-end--Author:kangxiaolin  Date:20190826 for：[05] 手机号不支持199号码段--------------------

          var params = {
            tableName: 'sys_user',
            fieldName: 'phone',
            fieldVal: value,
            dataId: this.userId
          };
          duplicateCheck(params).then((res) => {
            if (res.success) {
              callback();
            } else {
              callback(new Error('手机号已存在!'));
            }
          });
        } else {
          callback(new Error('请输入正确格式的手机号码!'));
        }
      }
    },
    validateEmail (rule, value, callback) {
      if (!value) {
        callback();
      } else {
        if (new RegExp(
          /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
        ).test(value)) {
          var params = {
            tableName: 'sys_user',
            fieldName: 'email',
            fieldVal: value,
            dataId: this.userId
          };
          duplicateCheck(params).then((res) => {
            if (res.success) {
              callback();
            } else {
              callback(new Error('邮箱已存在!'));
            }
          });
        } else {
          callback(new Error('请输入正确格式的邮箱!'));
        }
      }
    },
    validateUsername (rule, value, callback) {
      var params = {
        tableName: 'sys_user',
        fieldName: 'username',
        fieldVal: value,
        dataId: this.userId
      };
      duplicateCheck(params).then((res) => {
        if (res.success) {
          callback();
        } else {
          callback(new Error('用户账号已存在!'));
        }
      });
    },
    validateWorkNo (rule, value, callback) {
      var params = {
        tableName: 'sys_user',
        fieldName: 'work_no',
        fieldVal: value,
        dataId: this.userId
      };
      duplicateCheck(params).then((res) => {
        if (res.success) {
          callback();
        } else {
          callback(new Error('工号已存在!'));
        }
      });
    },
    normFile (e) {
      if (Array.isArray(e)) {
        return e;
      }
      return e && e.fileList;
    },
    beforeUpload: function (file) {
      var fileType = file.type;
      if (fileType.indexOf('image') < 0) {
        this.$message.warning('请上传图片');
        return false;
      }
      // TODO 验证文件大小
    },
    handleChange (info) {
      this.picUrl = '';
      if (info.file.status === 'uploading') {
        this.uploadLoading = true;
        return;
      }
      if (info.file.status === 'done') {
        var response = info.file.response;
        this.uploadLoading = false;
        if (response.success) {
          this.model.avatar = response.message;
          this.picUrl = 'Has no pic url yet';
        } else {
          this.$message.warning(response.message);
        }
      }
    },
    // 根据屏幕变化,设置抽屉尺寸
    resetScreenSize () {
      let screenWidth = document.body.clientWidth;
      if (screenWidth < 500) {
        this.drawerWidth = screenWidth;
      } else {
        this.drawerWidth = 700;
      }
    },
    identityChange (e) {
      if (e.target.value === '1') {
        this.departIdShow = false;
      } else {
        this.departIdShow = true;
      }
    },
    changeEvent (val, extra) {
      this.disabledRoles = !val;
      this.form.setFieldsValue({ 'dataRoles': [] });
      this.treeData = this.backTreeData.filter(item => {
        return val && extra.triggerNode.$options.propsData.dataRef.orgCode.indexOf(item.code) > -1;
      });
    },
    parentTree (arr, id) { // arr 所有的树数据 id 某个子节点的id
      var temp = [];
      var callback = function (nowArr, id) { // 先定义个函数寻找子节点位置 找到后 再找改节点父元素位置 以此类推
        for (var i = 0; i < nowArr.length; i++) {
          var item = nowArr[i];
          if (item.id === id) {
            temp.push(item.code);
            callback(arr, item.parentId); // pid 父级ID
            break;
          } else {
            if (item.children) {
              callback(item.children, id); // menus 子节点字段名称
            }
          }
        }
      };
      callback(arr, id);
      return temp; // 最后返回
    }
  }
};
</script>

<style scoped>
  :deep(.ant-drawer-content) {
    overflow: hidden;
  }

  .avatar-uploader>.ant-upload {
    width: 104px;
    height: 104px;
  }

  .ant-upload-select-picture-card i {
    font-size: 49px;
    color: #999;
  }

  .ant-upload-select-picture-card .ant-upload-text {
    margin-top: 8px;
    color: #666;
  }

  .ant-table-tbody .ant-table-row td {
    padding-top: 10px;
    padding-bottom: 10px;
  }

  .drawer-bootom-button {
    position: absolute;
    bottom: -8px;
    width: 100%;
    padding: 10px 16px;
    text-align: right;
    left: 0;
    border-radius: 0 0 2px 2px;
  }
</style>
