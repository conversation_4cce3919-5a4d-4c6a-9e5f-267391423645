<!-- 用户配置 -->
<template>
  <div>
    <!-- 查询区域 -->
    <div class="solar-eye-search-model">
      <a-form labelAlign="left" class="solar-eye-search-content" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :md="6" :sm="12">
            <a-form-item label="账号">
              <s-input placeholder="输入账号模糊查询" v-model="queryParam.username"></s-input>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <a-form-item label="性别">
              <a-select v-model="queryParam.sex" placeholder="请选择性别">
                <a-select-option value="">请选择</a-select-option>
                <a-select-option value="1">男性</a-select-option>
                <a-select-option value="2">女性</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <a-form-item label="真实名字">
              <a-input placeholder="请输入真实名字" v-model="queryParam.realname"></a-input>
            </a-form-item>
          </a-col>
          <template v-if="toggleSearchStatus">
            <a-col :md="6" :sm="8">
              <a-form-item label="手机号码">
                <a-input placeholder="请输入手机号码查询" v-model="queryParam.phone"></a-input>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="8">
              <a-form-item label="用户状态">
                <a-select v-model="queryParam.status" placeholder="请选择">
                  <a-select-option value="">请选择</a-select-option>
                  <a-select-option value="1">正常</a-select-option>
                  <a-select-option value="2">冻结</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </template>
          <a-col :md="6" :sm="8">
            <a-form-item>
              <a-button class="solar-eye-btn-primary" @click="searchQuery">查询</a-button>
              <a-button @click="searchReset" style="margin-left: 8px">重置</a-button>
              <a class="com-color" @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
              </a>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <div class="solar-eye-gap"></div>
    <div class="solar-eye-main-content" :style="{height: tableHeight + 120 +'px'}">
      <!-- 操作按钮区域 -->
      <div class="operation" style="height: 32px">
        <div class="operation-btn">
          <a-button @click="handleAdd" class="solar-eye-btn-primary">添加用户</a-button>
          <a-button type="primary" @click="handleExportXls('用户信息')">导出</a-button>
          <a-button type="primary" @click="recycleBinVisible=true">回收站</a-button>
          <a-dropdown v-if="selectedRowKeys.length > 0">
            <a-menu slot="overlay" @click="handleMenuClick">
              <a-menu-item key="1">
                <a-icon type="delete" @click="batchDel" />
                删除
              </a-menu-item>
              <a-menu-item key="2">
                <a-icon type="lock" @click="batchFrozen('2')" />
                冻结
              </a-menu-item>
              <a-menu-item key="3">
                <a-icon type="unlock" @click="batchFrozen('1')" />
                解冻
              </a-menu-item>
            </a-menu>
            <a-button style="margin-left: 8px">
              批量操作
              <a-icon type="down" />
            </a-button>
          </a-dropdown>
        </div>
      </div>
      <!-- table区域-begin -->

      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i>已选择&nbsp;<a
          style="font-weight: 600">{{ selectedRowKeys.length }}</a>项&nbsp;&nbsp;
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table ref="table" bordered size="small" rowKey="id" :columns="columns" :dataSource="dataSource"
        :pagination="ipagination" :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}" @change="handleTableChange"
        :scroll="{y: tableHeight - 77 -44 }">

        <template slot="avatarslot" slot-scope="text, record">
          <div class="anty-img-wrap">
            <a-avatar shape="square" :src="getAvatarView(record.avatar)" icon="user" />
          </div>
        </template>

        <span slot="action" slot-scope="text, record">
          <a
            @click="function(){let params = {};Object.assign(params,record);params.editFlag = true;handleEdit(params)}">编辑</a>

          <a-divider type="vertical" />

          <a-dropdown>
            <a class="ant-dropdown-link">
              更多
              <a-icon type="down" />
            </a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a href="javascript:;" @click="handleDetail(record)">详情</a>
              </a-menu-item>

              <a-menu-item>
                <a href="javascript:;" @click="handleChangePassword(record.username)">密码</a>
              </a-menu-item>

              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)" okText="确定" cancelText="取消">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>

              <a-menu-item v-if="record.status==1">
                <a-popconfirm title="确定冻结吗?" @confirm="() => handleFrozen(record.id,2,record.username)" okText="确定" cancelText="取消">
                  <a>冻结</a>
                </a-popconfirm>
              </a-menu-item>

              <a-menu-item v-if="record.status==2">
                <a-popconfirm title="确定解冻吗?" @confirm="() => handleFrozen(record.id,1,record.username)" okText="确定" cancelText="取消">
                  <a>解冻</a>
                </a-popconfirm>
              </a-menu-item>

            </a-menu>
          </a-dropdown>
        </span>

      </a-table>
    </div>
    <!-- table区域-end -->

    <user-modal ref="modalForm" @ok="modalFormOk"></user-modal>
    <password-modal ref="passwordmodal" v-if="visible" @ok="()=> visible = false" @close="()=> visible = false"></password-modal>

    <!-- 用户回收站 -->
    <user-recycle-bin-modal :visible.sync="recycleBinVisible" @ok="modalFormOk" />

  </div>
</template>

<script>
import UserModal from './modules/UserModal';
import initDict from '@/mixins/initDict';
import PasswordModal from './modules/PasswordModal';
import {
  getFileAccessHttpUrl
} from '@/api/manage';
import {
  frozenBatch
} from '@/api/api';
import {
  SolarEyeListMixin
} from '@/mixins/SolarEyeListMixin';
import SInput from '@/components/solareye/Input';
import UserRecycleBinModal from './modules/UserRecycleBinModal';
import tableHeight from '@/mixins/tableHeightAndSearchModel';
export default {
  name: 'UserList',
  mixins: [SolarEyeListMixin, tableHeight, initDict],
  components: {
    UserModal,
    PasswordModal,
    SInput,
    UserRecycleBinModal
  },
  data () {
    return {
      description: '这是用户管理页面',
      queryParam: {},
      recycleBinVisible: false,
      columns: [
        /* {
            title: '#',
            dataIndex: '',
            key:'rowIndex',
            width:60,
            align:"center",
            customRender:function (t,r,index) {
              return parseInt(index)+1;
            }
          }, */
        {
          title: '用户账号',
          align: 'center',
          dataIndex: 'username',
          width: 120,
          sorter: true
        },
        {
          title: '用户姓名',
          align: 'center',
          width: 100,
          dataIndex: 'realname'
        },
        {
          title: '工号',
          align: 'center',
          width: 120,
          dataIndex: 'workNo'
        },

        {
          title: '性别',
          align: 'center',
          width: 80,
          dataIndex: 'sex_dictText',
          sorter: true
        },
        {
          title: '职位',
          align: 'center',
          dataIndex: 'post',
          width: 100,
          customRender: (t, r, index) => {
            return t ? this.disposePosition(t) : '';
          }
        },
        {
          title: '手机号码',
          align: 'center',
          width: 100,
          dataIndex: 'phone'
        },
        {
          title: '部门',
          align: 'center',
          width: 180,
          dataIndex: 'orgCodeTxt'
        },
        {
          title: '状态',
          align: 'center',
          width: 80,
          dataIndex: 'status_dictText'
        },
        {
          title: '操作',
          dataIndex: 'action',
          scopedSlots: {
            customRender: 'action'
          },
          align: 'center',
          width: 170
        }

      ],
      url: {
        syncUser: '/act/process/extActProcess/doSyncUser',
        list: '/sys/user/list',
        delete: '/sys/user/delete',
        deleteBatch: '/sys/user/deleteBatch',
        exportXlsUrl: '/sys/user/exportXls',
        importExcelUrl: 'sys/user/importExcel'
      },
      visible: false
    };
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
    }
  },
  mounted () {
    this.getDictMap('position');
  },
  watch: {
    'dataSource' () {
      this.setBodyMinHeight(this.dataSource.length, 120);
    },
    'tableHeight' () {
      this.setBodyMinHeight(this.dataSource.length, 120);
    }
  },
  methods: {
    // 处理职位
    disposePosition (t) {
      let data = this.dictMap.position.filter(item => item.codeValue == t);
      if (data && data.length) {
        return data[0].dataLable;
      } else {
        return '';
      }
    },
    getAvatarView: function (avatar) {
      return getFileAccessHttpUrl(avatar);
    },

    batchFrozen: function (status) {
      if (this.selectedRowKeys.length <= 0) {
        this.$message.warning('请选择一条记录！');
        return false;
      } else {
        let ids = '';
        let that = this;
        let isAdmin = false;
        that.selectionRows.forEach(function (row) {
          if (row.username == 'admin') {
            isAdmin = true;
          }
        });
        if (isAdmin) {
          that.$message.warning('管理员账号不允许此操作,请重新选择！');
          return;
        }
        that.selectedRowKeys.forEach(function (val) {
          ids += val + ',';
        });
        that.$confirm({
          title: '确认操作',
          content: '是否' + (status == 1 ? '解冻' : '冻结') + '选中账号?',
          okText: '确定',
          cancelText: '取消',
          onOk: function () {
            frozenBatch({
              ids: ids,
              status: status
            }).then((res) => {
              if (res.success) {
                that.$message.success(res.message);
                that.loadData();
                that.onClearSelected();
              } else {
                that.$message.warning(res.message);
              }
            });
          }
        });
      }
    },
    handleMenuClick (e) {
      if (e.key == 1) {
        this.batchDel();
      } else if (e.key == 2) {
        this.batchFrozen(2);
      } else if (e.key == 3) {
        this.batchFrozen(1);
      }
    },
    handleFrozen: function (id, status, username) {
      let that = this;
      // TODO 后台校验管理员角色
      if (username == 'admin') {
        that.$message.warning('管理员账号不允许此操作！');
        return;
      }
      frozenBatch({
        ids: id,
        status: status
      }).then((res) => {
        if (res.success) {
          that.$message.success(res.message);
          that.loadData();
        } else {
          that.$message.warning(res.message);
        }
      });
    },
    handleChangePassword (username) {
      this.visible = true;
      this.$nextTick(() => {
        this.$refs.passwordmodal.show(username);
      });
    }
  }

};
</script>
<style scoped>
</style>
