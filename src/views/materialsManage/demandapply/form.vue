<!-- 需求申请表单 -->
<template>
  <div class="drawer-form-com">
    <div class="drawer-form-content">
      <a-spin :spinning="loading">
        <a-form-model ref="demandapplyForm" :model="dataForm" :rules="rules" :labelCol="{ style: 'width: 110px' }"
                      :wrapperCol="{ style: 'width: calc(100% - 110px)' }">
          <template v-if="isEdit">
            <a-row>
              <a-col :span='24'>
                <div class="order-dispose">
                  <div class="title-box">
                    <span class="before"></span>
                    <span>基本信息</span>
                  </div>
                </div>
              </a-col>
              <a-col :xl='8' :sm='12' :xs='24'>
                <a-form-model-item label="申请类型" prop="applyType">
                  <a-radio-group v-model="dataForm.applyType" @change="applyTypeChange" :disabled="type == '2'" style="width: 100%;" >
                    <a-radio value="materialApply">物资</a-radio>
                    <a-radio value="serveApply">服务</a-radio>
                  </a-radio-group>
                </a-form-model-item>
              </a-col>
              <a-col :xl='8' :sm='12' :xs='24' v-if="dataForm.applyType == 'materialApply'">
                <a-form-model-item label="仓库名称" prop="warehouseCode">
                  <a-select size="default" v-model="dataForm.warehouseCode" :disabled="warehouseCodeDisabled || type == '2'"
                            @change='warehouseCodeChange' placeholder="请选择仓库名称">
                    <a-select-option v-for="item in warehouseOptions" :key="item.warehouseCode" :value="item.warehouseCode">
                      {{item.warehouseName}}
                    </a-select-option>
                  </a-select>
                </a-form-model-item>
              </a-col>
              <a-col :xl='8' :sm='12' :xs='24'>
                <a-form-model-item label="申请人" prop="applyUserLabel">
                  <a-input disabled v-model="dataForm.applyUserLabel" size="default" />
                </a-form-model-item>
              </a-col>
              <a-col :xl='8' :sm='12' :xs='24'>
                <a-form-model-item label="申请日期" prop="applyDate">
                  <a-input disabled v-model="dataForm.applyDate" size="default" />
                </a-form-model-item>
              </a-col>
              <a-col :xl='8' :sm='12' :xs='24'>
                <a-form-model-item label="建议采购方式" prop="procurementType">
                  <a-select v-model="dataForm.procurementType" placeholder="请选择建议采购方式" style="width: 100%;" :allowClear="false">
                    <a-select-option v-for="item in dictMap.procurement_type" :key="item.dataValue" :value="item.dataValue">
                      {{item.dataLable}}
                    </a-select-option>
                  </a-select>
                </a-form-model-item>
              </a-col>
              <template v-if="dataForm.applyType == 'materialApply'">
                <a-col :xl='8' :sm='12' :xs='24'>
                  <a-form-model-item label="资产归属" prop="proxyFlag">
                    <a-radio-group v-model="dataForm.proxyFlag" @change="proxyFlagChange" style="width: 100%;" >
                      <a-radio value="1">业主</a-radio>
                      <a-radio value="0">阳光智维</a-radio>
                    </a-radio-group>
                  </a-form-model-item>
                </a-col>
                <a-col :xl='8' :sm='12' :xs='24'>
                  <a-form-model-item label="履约类别" prop="fulfilmentType">
                    <a-select v-model="dataForm.fulfilmentType" placeholder="请选择履约类别"
                              style="width: 100%;" :allowClear="false">
                      <a-select-option v-for="item in fulfilmentTypeList" :key="item.dataValue" :value="item.dataValue">
                        {{item.dataLable}}
                      </a-select-option>
                    </a-select>
                  </a-form-model-item>
                </a-col>
              </template>
              <template v-if="dataForm.applyType == 'serveApply'">
                <a-col :xl='8' :sm='12' :xs='24'>
                  <a-form-model-item label="履约类别" prop="fulfilmentType">
                    <a-select v-model="dataForm.fulfilmentType" placeholder="请选择履约类别"
                              style="width: 100%;" :allowClear="false">
                      <a-select-option v-for="item in dictMap.fulfilment_type" :key="item.dataValue" :value="item.dataValue">
                        {{item.dataLable}}
                      </a-select-option>
                    </a-select>
                  </a-form-model-item>
                </a-col>
              </template>
              <a-col :xl='8' :sm='12' :xs='24'>
                <a-form-model-item label="是否指定供应商" prop="isSpecifySupplier">
                  <a-radio-group v-model="dataForm.isSpecifySupplier" @change="isSpecifySupplierChange" style="width: 100%;" >
                    <a-radio value="1">是</a-radio>
                    <a-radio value="0">否</a-radio>
                  </a-radio-group>
                </a-form-model-item>
              </a-col>
              <a-col :xl='8' :sm='12' :xs='24' v-if="dataForm.isSpecifySupplier == '1'">
                <a-form-model-item label="指定供应商" prop="supplierCode">
                  <a-input v-model='dataForm.supplierName' readOnly="true" @click="openSupplierModel()"
                           placeholder="请选择指定供应商"></a-input>
                </a-form-model-item>
              </a-col>
              <template v-if="dataForm.applyType == 'serveApply'">
                <a-col :xl='8' :sm='12' :xs='24'>
                  <a-form-model-item label="是否出险" prop="hasAccident">
                    <a-radio-group v-model="dataForm.hasAccident" @change="hasAccidentChange" style="width: 100%;" >
                      <a-radio value="1">是</a-radio>
                      <a-radio value="0">否</a-radio>
                    </a-radio-group>
                  </a-form-model-item>
                </a-col>
                <a-col :xl='8' :sm='12' :xs='24' v-if="dataForm.hasAccident == '1'">
                  <a-form-model-item label="事故事件备案" prop="accidentCode">
                    <a-input v-model='dataForm.accidentCode' readOnly="true" @click="openAssociationModel('1', {})"
                            placeholder="请选择事故事件备案"></a-input>
                  </a-form-model-item>
                </a-col>
              </template>
              <a-col :xl='8' :sm='12' :xs='24' v-if="dataForm.applyType == 'materialApply'">
                <div style='display: flex;align-items: center'>
                  <a-form-model-item label='收货地址' prop='area'>
                    <g-cascader v-model='dataForm.area' :options='areaList' placeholder='请选择省市区' @change='changeByArea' style='width: 100%;'/>
                  </a-form-model-item>
                  <a-form-model-item label=' ' prop='areaDetail' class='operator-label'>
                    <a-input v-model='dataForm.areaDetail' @blur="dataForm.areaDetail = $trim($event)" :maxLength='50' placeholder='请输入详细地址'
                             style='width: calc(100% + 100px);margin-left: 10px' />
                  </a-form-model-item>
                </div>
              </a-col>
              <a-col :span='24'>
                <a-form-model-item label="申请说明" prop="applyDescription">
                  <a-textarea :max-length="1000" v-model="dataForm.applyDescription" placeholder="请输入申请说明"
                              @blur="dataForm.applyDescription = $trim($event)" :auto-size="{ minRows: 2, maxRows: 7}" size="default" />
                </a-form-model-item>
              </a-col>
              <a-col :span="24">
                <a-form-model-item label="附件" prop="uploadFileList">
                  <fileUploadView v-model="dataForm.uploadFileList" :multiple="true" :maxNum="5" :maxSize="10"
                                  accept=".jpg,.png,.jpeg,.bmp,.pdf,.doc,.docx,.xlsx"
                                  tip="请上传jpg、jpeg、png、bmp、pdf、word、excel格式的文件，最多5个，单个不能超过10MB">
                  </fileUploadView>
                </a-form-model-item>
              </a-col>
            </a-row>
          </template>

          <template v-else>
            <detail-layout :labelList="baseList" :form="dataForm" title="基本信息"></detail-layout>
          </template>

          <div class='order-dispose'>
            <div class='title-box' style='padding-bottom: 10px'>
              <span class='before'></span>
              <span>申请明细</span>
            </div>
          </div>

          <a-row class="right-btn" v-if='isEdit'>
            <throttle-button label="添加" :disabled="applyDetails.length > 99" @click="addRow" />
            <throttle-button label="删行" :disabled="checkedData.length == 0" @click="deleteRow('applyDetails')" style='margin-left: 10px;'/>
          </a-row>
          <!--表格渲染-->
          <vxe-table :data="applyDetails" max-height="440" ref="formTable" class='my-table' style='margin-left: 20px'
                     :edit-rules="rules" show-overflow highlight-hover-row size="small"
                     @checkbox-all="handleSelectionChange" @checkbox-change="handleSelectionChange">
            <vxe-table-column v-if='isEdit' type="checkbox" width="50" fixed="left"></vxe-table-column>
            <vxe-table-column type="seq" width="80" title="序号"></vxe-table-column>
            <vxe-table-column :show-overflow="['materialBusinessAttributeNumber', 'psaId'].includes(info.name) ? false : 'title'"
                              v-for="info in columnList" :key="info.name"
                              :min-width="(info.width ? info.width : 100)"
                              :align="(info.align ? info.align : 'left')" :title="info.comment"
                              :field="info.name" :visible="isShowCol(info.name, info.type)">
              <template v-slot="{  row, column, rowIndex}">
                <template v-if='isEdit'>
                  <template v-if="info.name=='materialBusinessAttributeNumber'">
                    <a-select size="default" v-model="row.materialBusinessAttributeNumber" placeholder="请选择"
                          @change="$refs.formTable.updateStatus({ row, column })">
                      <a-select-option v-for="item in row.materialBusinessAttributes" :key="item.number" :value="item.number">
                        {{item.name}}
                      </a-select-option>
                    </a-select>
                  </template>
                  <template v-else-if="info.name=='applyNum'">
                    <a-input-number :controls=false :precision="0" :disabled="!isEdit" :min="1" :max="99999999"
                                    v-model="row[info.name]" @blur.stop="estimatePriceWithTaxChange(row)" placeholder="请输入"
                                    @change="$refs.formTable.updateStatus({ row, column })"></a-input-number>
                  </template>
                  <template v-else-if="info.name=='estimatePriceWithTax'">
                    <a-input-number :key='rowIndex' :controls=false :precision="2" :disabled="!isEdit" :min="0" :max="99999999.99"
                                    v-model="row[info.name]" @blur.stop="estimatePriceWithTaxChange(row)" placeholder="请输入"
                                    @change="$refs.formTable.updateStatus({ row, column })"></a-input-number>
                  </template>
                  <template v-else-if="info.name=='taxRate'">
                    <a-select size="default" v-model="row.taxRate" placeholder="请选择" @change="estimatePriceWithTaxChange(row)">
                      <a-select-option v-for="item in taxRateList" :key="item.code" :value="item.code" :title="item.name" >
                        {{item.name}}
                      </a-select-option>
                    </a-select>
                  </template>
                  <template v-else-if="info.name=='psaId' && dataForm.applyType == 'materialApply'" >
                    <a-select size="default" v-model="row.psaId" placeholder="请选择" allowClear
                              @change='psaIdAndEnterDateChange(row, column)' show-search
                              option-filter-prop="children" :filter-option="filterOption">
                      <a-select-option v-if='isPsaWarehouse' value="all" >全部</a-select-option>
                      <a-select-option v-for="item in psaOptions" :key="item.pasId + ''" :value="item.pasId + ''" :title="item.pasName">
                        {{item.pasName}}
                      </a-select-option>
                    </a-select>
                  </template>
                  <template v-else-if="info.name=='enterDate' && dataForm.applyType == 'materialApply'">
                    <a-date-picker v-model="row.enterDate" valueFormat="YYYY-MM-DD" @change='psaIdAndEnterDateChange(row, column)'
                                   format="YYYY-MM-DD" placeholder="请选择" size="default"></a-date-picker>
                  </template>
                  <template v-else-if="info.name=='subjectCode'">
                    <a-input v-model="row.subjectName" placeholder="请选择" readOnly="true"
                             @click="openSubjectModal(row, column)"></a-input>
                  </template>
                  <template v-else-if="info.name=='remark'">
                    <a-input v-model="row.remark" :disabled="!isEdit" :max-length="200" placeholder="请输入"></a-input>
                  </template>
                  <template v-else-if="info.name=='psaId' && dataForm.applyType == 'serveApply'">
                    <psa-select v-model="row.psaId" style="width: 100%;" @change='psaIdAndDataChange(row, column)' placeholder="请选择"></psa-select>
                  </template>
                  <template v-else-if="info.name=='relatedTaskNo'">
                    <a-input v-if='row.relatedTaskType' v-model="row.relatedTaskNo" placeholder="请选择" readOnly="true"
                             @click="openAssociationModel('2', row)"></a-input>
                    <template v-else>
                      <span>{{getLabel(row[info.name], null)}}</span>
                    </template>
                  </template>
                  <template v-else-if="info.name=='enterDate' && dataForm.applyType == 'serveApply' && !row.relatedTaskNo">
                    <a-date-picker v-model="row.enterDate" valueFormat="YYYY-MM-DD"
                                   format="YYYY-MM-DD" placeholder="请选择" size="default"
                                   :disabled-date="val => disabledEnterDate(val, row)"
                                   @change="$refs.formTable.updateStatus({ row, column })"></a-date-picker>
                  </template>
                  <template v-else-if="info.name=='completeDate' && dataForm.applyType == 'serveApply' && !row.relatedTaskNo">
                    <a-date-picker v-model="row.completeDate" valueFormat="YYYY-MM-DD" @change='psaIdAndDataChange(row, column)'
                                   format="YYYY-MM-DD" placeholder="请选择" size="default"
                                   :disabled-date="val => disabledCompleteDate(val, row)"></a-date-picker>
                  </template>
                  <template v-else>
                    <span>{{getLabel(row[info.name], null)}}</span>
                  </template>
                </template>
                <template v-else>
                  <span>{{getLabel(row[info.name], null)}}</span>
                </template>
              </template>
            </vxe-table-column>
            <template v-slot:empty>
              <span>查询无数据</span>
            </template>
          </vxe-table>
          <div class='amt-content'>
            <div class='amt-content-right'>
              <div class='amt-content-item'>
                <span class='item-label'>预估申请含税总价(元):</span>
                <span class='item-value'>{{ getMoneyLabel(estimateTotalPriceWithTax) }}</span>
              </div>
            </div>
          </div>
        </a-form-model>
      </a-spin>
    </div>

    <flow-chart :parentId="parentId" :processInstanceId="dataForm.processInstanceId" :flowUser="dataForm.flowUser"
                processDefinitionKey="资产管理_需求申请" :extraFlowChartMap="{formId:'pm_requirapplybill', app:'mypr'}"/>

    <div class="drawer-form-foot">
      <template v-if="isEdit">
        <a-button type="info" :disabled="loading" @click="cancel">取消</a-button>
        <a-button :loading="loading" class="ant-btn-primary" @click="doSubmit('1')">提交</a-button>
      </template>
      <template v-else>
        <a-button type="info" @click="cancel">返回</a-button>
        <a-button v-if="dataForm.applyUser == user.username && ['act0'].includes(dataForm.taskDefKey)" :loading="loading" class="ant-btn-primary" @click="processCancel(dataForm.applyType)">作废</a-button>
      </template>
    </div>
    <!-- 选择数据 -->
    <choose-asset-model v-model="showChooseAssetForm" :visible="showChooseAssetForm" :rowData="rowData" @cancel="val => doChooseAsset(val , 'applyDetails', dataForm.applyType)"/>
    <subject-modal v-model="showSubjectModal" :visible="showSubjectModal" :rowData="rowSubjectData" @ok="handleOk"/>
    <association-model v-model="showAssociationModel" :visible="showAssociationModel" :rowData="rowAssociationData" @cancel="doAssociationModel"/>
    <supplier-model v-model="showSupplierModel" :visible="showSupplierModel" :rowData="rowSupplierData" @cancel="doSupplierModel"/>
  </div>
</template>

<script>
import { createDemand, getAvailableBudget, queryDemandDetail } from '@/api/materialsManage/demandapply';
import initDict from '@/mixins/initDict';
import moment from 'moment';
import { getWidth } from '@/utils/util';
import { USER_INFO } from '@/store/mutation-types';
import ChooseAssetModel from '@views/materialsManage/modules/chooseAssetModel';
import SubjectModal from '@views/materialsManage/demandapply/subjectModal';
import AssociationModel from '@views/materialsManage/demandapply/associationModel';
import SupplierModel from '@views/materialsManage/demandapply/supplierModel';
import fileUploadView from '@/components/com/fileUploadView';
import FlowChart from '@comp/erp/flowChart';
import mixin from '../js/mixin';
import { queryWarehouseList } from '@api/isolarErp/asset/warehouseManage';
import {
  demandapplyDetailBaseList,
  demandapplyDetailColumnList,
  demandapplyRules
} from '@views/materialsManage/js/column';
import { areaTree } from '@api/isolarErp/safetyquality/safetyRisk';
export default {
  props: {
    /* 抽屉挂在的DOM节点ID，必要参数 */
    parentId: {
      type: String,
      required: true,
      default: ''
    }
  },
  components: {
    SubjectModal,
    AssociationModel,
    SupplierModel,
    ChooseAssetModel,
    fileUploadView,
    FlowChart
  },
  mixins: [initDict, mixin],
  data () {
    return {
      loading: false,
      expLoading: false,
      isEdit: false,
      warehouseCodeDisabled: false,
      isPsaWarehouse: false,
      type: '',
      dataForm: {
        applyType: 'materialApply',
        warehouseCode: undefined,
        applyUser: undefined,
        applyUserLabel: undefined,
        applyDate: undefined,
        procurementType: undefined,
        proxyFlag: undefined,
        area: undefined,
        areaValue: undefined,
        areaDetail: undefined,
        areaName: undefined,
        fulfilmentType: undefined,
        isUrgent: '0',
        isSpecifySupplier: undefined,
        supplierCode: undefined,
        hasAccident: undefined,
        accidentCode: undefined,
        applyDescription: undefined,
        uploadFileList: undefined,
        estimateTotalPriceWithTax: undefined
      },
      areaList: [],
      fulfilmentTypeList: [],
      applyDetails: [],
      psaOptions: [],
      taxRateList: [{ number: '0%', name: '0%', code: 'V0' },
        { number: '1%', name: '1%', code: 'V1' },
        { number: '1.5%', name: '1.5%', code: 'V1.5' },
        { number: '2%', name: '2%', code: 'V2' },
        { number: '3%', name: '3%', code: 'V3' },
        { number: '5%', name: '5%', code: 'V5' },
        { number: '6%', name: '6%', code: 'V6' },
        { number: '8%', name: '8%', code: 'V8' },
        { number: '8%', name: '8%（越南）', code: 'VN08' },
        { number: '9%', name: '9%', code: 'V9' },
        { number: '10%', name: '10%', code: 'V10' },
        { number: '10%', name: '10%（越南）', code: 'VN10' },
        { number: '13%', name: '13%', code: 'V13' }],
      baseList: demandapplyDetailBaseList,
      columnList: demandapplyDetailColumnList,
      rules: demandapplyRules,
      showAssociationModel: false,
      rowAssociationData: {},
      showSubjectModal: false,
      rowSubjectData: {},
      showSupplierModel: false,
      rowSupplierData: {}
    };
  },
  computed: {
    estimateTotalPriceWithTax () {
      return this.dataForm.estimateTotalPriceWithTax;
    }
  },
  created () {
    this.queryWarehouseLists({ 'byDataRole': true });
    this.columnList.forEach(item => {
      if (!item.width) {
        item.width = getWidth(item.comment);
      }
    });
    this.getDictMap('procurement_type,fulfilment_type,has_accident_realated_subject_code');
  },
  mounted () {
    this.queryArea();
  },
  methods: {
    moment,
    // 切换抽屉时动画结束后的回调，页面初始化init函数的异步请求须放在这里调用，否则可能会会导致抽屉侧滑出来时卡死
    afterEvent () {
      let self = this;
      if (self.type != '1') {
        // 查询信息
        let params = {
          id: self.row.id
        };
        queryDemandDetail(params).then(res => {
          if (res.result_data) {
            let form = res.result_data;
            form.area = form.areaValue ? form.areaValue.split(',') : [];
            form.areaDetails = form.areaName;
            if (form.areaDetail) {
              form.areaDetails = form.areaName + form.areaDetail;
            }
            Object.assign(self.dataForm, form);
            self.applyDetails = res.result_data.applyDetails;
            self.applyDetails.forEach(item => {
              item.key = Math.random();
              item.estimatePriceWithTax = Number(item.estimatePriceWithTax).toFixed(2);
            });
            if (form.applyType == 'materialApply') {
              self.getWarehouseTypeByWarehouseCode(self.dataForm.warehouseCode, 'materialApply');
              self.getFulfilmentTypeList(self.dataForm.proxyFlag);
            }
          }
          self.refreshColumn();
          self.loading = false;
        }).catch(() => {
          self.loading = false;
        });
      } else {
        let user = Vue.ls.get(USER_INFO);
        self.dataForm.applyUser = user.username;
        self.dataForm.applyUserLabel = user.realname;
        self.dataForm.applyDate = moment().format('YYYY-MM-DD');
        self.loading = false;
        self.refreshColumn();
      }
    },
    // type 新增：1  编辑：2  详情: 3
    init (type, row) {
      let self = this;
      self.isEdit = ['1', '2'].includes(type);
      self.loading = true;
      self.type = type;
      self.row = row;
      if (row) {
        Object.assign(self.dataForm, row);
      }
      self.afterEvent();
      switch (type) {
        case '1':
          return '发起申请';
        case '2':
          return '编辑申请';
        case '3':
          return '申请详情';
      }
    },
    // 表-列刷新
    refreshColumn () {
      this.$nextTick(() => {
        let $table = this.$refs.formTable;
        $table && $table.refreshColumn();
      });
    },
    // 查询仓库列表
    queryWarehouseLists (val) {
      let params = {
        'keyword': val.keyword,
        'byDataRole': val.byDataRole
      };
      queryWarehouseList(params).then(res => {
        let wareList = res.result_data.selectRangeList;
        let defaultSelectedList = res.result_data.defaultSelectedList;
        this.warehouseOptions = wareList.filter(item => defaultSelectedList.includes(item.warehouseCode));
        if (Array.isArray(defaultSelectedList) && defaultSelectedList.length == 1) {
          this.warehouseCodeDisabled = true;
          this.dataForm.warehouseCode = defaultSelectedList[0];
          this.getWarehouseTypeByWarehouseCode(defaultSelectedList[0], 'materialApply');
        }
      }).catch(() => {
        this.warehouseOptions = [];
      });
    },
    // 省市区查询
    async queryArea () {
      let res = await areaTree({});
      this.areaList = res.result_data;
    },
    // 仓库change
    warehouseCodeChange (val) {
      this.applyDetails = [];
      this.getWarehouseTypeByWarehouseCode(val, 'materialApply');
    },
    // 申请类型change
    applyTypeChange (val) {
      let value = val.target.value;
      if (value == 'materialApply') {
        this.dataForm.isSpecifySupplier = undefined;
        this.dataForm.hasAccident = undefined;
        this.dataForm.accidentCode = undefined;
        this.rules.estimatePriceWithTax[0].message = '请填写预估单价-含税(元)';
      } else {
        this.dataForm.proxyFlag = undefined;
        this.rules.estimatePriceWithTax[0].message = '请填写预估总价-含税(元)';
      }
      this.dataForm.estimateTotalPriceWithTax = undefined;
      this.dataForm.fulfilmentType = undefined;
      this.applyDetails = [];
      this.$refs['demandapplyForm'].clearValidate();
      this.refreshColumn();
    },
    // 是否代采change
    proxyFlagChange (val) {
      let value = val.target.value;
      this.dataForm.fulfilmentType = undefined;
      this.getFulfilmentTypeList(value);
    },
    // 获取履约类别下拉选项
    getFulfilmentTypeList (value) {
      let dicVal = value == '1' ? ['A', 'B', 'D'] : ['A', 'C', 'D'];
      this.fulfilmentTypeList = this.dictMap.fulfilment_type.filter(item => dicVal.includes(item.dataValue));
    },
    // 地址 change
    changeByArea (val) {
      this.dataForm.areaName = '';
      if (val.length > 0) {
        let label = [];
        let options = JSON.parse(JSON.stringify(this.areaList));
        val.forEach(item => {
          for (let option of options) {
            if (option.value == item) {
              label.push(option.label);
              options = option.children;
              break;
            }
          }
        });
        this.dataForm.areaName = label.join('/');
        this.dataForm.areaValue = this.dataForm.area.join(',');
      }
    },
    // 是否指定供应商change
    isSpecifySupplierChange () {
      this.dataForm.supplierCode = undefined;
      this.dataForm.supplierName = undefined;
    },
    // 是否出险change
    hasAccidentChange () {
      this.dataForm.accidentCode = undefined;
      this.applyDetails = [];
      this.dataForm.estimateTotalPriceWithTax = undefined;
    },
    // 履约项目change
    psaIdAndDataChange (row, column) {
      if (!(row.psaId && row.completeDate)) {
        row.subjectName = undefined;
        row.budgetSubjectCode = undefined;
        row.budgetSubjectName = undefined;
        row.remainingBudget = undefined;
        this.$refs['formTable'].reloadRow(row);
        this.$refs.formTable.updateStatus({ row, column });
        return;
      }
      let subjectCode;
      if (this.dataForm.hasAccident == '0') {
        subjectCode = row.subjectCode;
      } else {
        subjectCode = this.dictMap.has_accident_realated_subject_code[0].dataValue;
      }
      let params = {
        'psaId': row.psaId,
        'subjectCode': subjectCode,
        'entryDate': row.completeDate
      };
      getAvailableBudget(params).then(res => {
        let data = res.result_data;
        row.subjectCode = subjectCode;
        row.subjectName = data ? data.subjectName : undefined;
        row.remainingBudget = data ? data.availableAmount : undefined;
        row.budgetSubjectCode = data ? data.budgetSubjectCode : undefined;
        row.budgetSubjectName = data ? data.budgetSubjectName : undefined;
        this.$refs['formTable'].reloadRow(row);
        this.$refs.formTable.updateStatus({ row, column });
        this.refreshColumn();
      }).catch(() => {
      });
    },
    // 预估含税单价change
    estimatePriceWithTaxChange (row) {
      if (this.dataForm.applyType == 'materialApply') {
        if (row.estimatePriceWithTax && row.applyNum) {
          row.estimateTotalPriceWithTax = (row.applyNum * row.estimatePriceWithTax).toFixed(2);
        } else {
          row.estimateTotalPriceWithTax = undefined;
        }
      } else if (this.dataForm.applyType == 'serveApply') {
        if (row.estimatePriceWithTax && row.taxRate) {
          let dd = this.taxRateList.filter(item => item.code == row.taxRate);
          let taxRate = 1 + (Number(dd[0].number.substring(0, dd[0].number.length - 1)) / 100);
          row.estimatePriceWithoutTax = (row.estimatePriceWithTax / taxRate).toFixed(2);
        } else {
          row.estimatePriceWithoutTax = undefined;
        }
      }
      this.$refs['formTable'].reloadRow(row);
      this.getSum();
    },
    // 履约项目  进场日期change
    psaIdAndEnterDateChange (row, column) {
      row.subjectCode = undefined;
      row.subjectName = undefined;
      this.$refs['formTable'].reloadRow(row);
      this.$refs.formTable.updateStatus({ row, column });
    },
    // 增行
    addRow () {
      if (this.dataForm.applyType == 'materialApply' && !this.dataForm.warehouseCode) {
        this.$message.warning('请先选择仓库');
        return;
      }
      if (this.dataForm.applyType == 'serveApply' && !this.dataForm.hasAccident) {
        this.$message.warning('请先选择是否出险');
        return;
      }
      this.openChoose('applyDetails', 'demand');
    },
    // 关联费用项目弹窗
    openSubjectModal (row) {
      if (row.psaId && row.enterDate) {
        this.showSubjectModal = true;
        this.rowSubjectData = row;
        this.$refs.formTable.clearValidate();
      } else {
        this.$message.warning('请先选择履约项目和进场日期');
      }
    },
    // 关联项目费用弹窗回调
    handleOk (data) {
      this.$refs['formTable'].reloadRow(data);
    },
    // 关联任务 事故事件弹窗
    openAssociationModel (type, row) {
      if (type == '1' || (type == '2' && row.psaId)) {
        row.pageType = type;
        row.num = type == '1' ? this.dataForm.accidentCode : row.relatedTaskNo;
        this.showAssociationModel = true;
        this.rowAssociationData = row;
      } else {
        this.$message.warning('请先选择履约项目');
      }
    },
    // 关联任务 事故事件弹窗回调
    doAssociationModel (data) {
      if (data.pageType == '1') {
        this.dataForm.accidentCode = data.accidentNum;
        this.validateField('accidentCode');
      } else {
        this.$refs['formTable'].reloadRow(data);
        this.psaIdAndDataChange(data);
      }
    },
    // 选择供应商弹窗
    openSupplierModel () {
      let row = {};
      row.num = this.dataForm.supplierCode;
      this.showSupplierModel = true;
      this.rowSupplierData = row;
    },
    // 选择供应商弹窗回调
    doSupplierModel (data) {
      this.dataForm.supplierCode = data.supplierCode;
      this.dataForm.supplierName = data.supplierName;
      this.validateField('supplierCode');
    },
    // 保存
    doSubmit () {
      this.loading = true;
      let { applyDetails } = this;
      if (!applyDetails.length) {
        this.$message.warning('请添加申请明细');
        this.loading = false;
        return;
      }

      this.$refs['demandapplyForm'].validate(valid => {
        if (valid) {
          const check = this.checkData(applyDetails);
          if (check) {
            this.loading = false;
            return;
          }
          this.update();
        } else {
          this.loading = false;
          return false;
        }
      });
    },
    // 数据校验
    checkData (items) {
      try {
        items.forEach((item, index) => {
          if (item.relatedTaskType && !item.relatedTaskNo) {
            throw new Error('请选择关联任务');
          }
          if (this.dataForm.applyType == 'serveApply' && !item.subjectCode) {
            throw new Error('提交失败，费用项目不能为空');
          }
        });
        return false;
      } catch (e) {
        this.$message.warning(e.message);
        return true;
      }
    },
    async update () {
      const self = this;
      const errMap = await self.$refs['formTable'].validate(true).catch(errMap => errMap);
      if (errMap != undefined) {
        self.loading = false;
      } else {
        let map = {};
        Object.assign(map, self.dataForm);
        map.applyDetails = self.applyDetails;
        self.savePromise(map);
      }
    },
    // 提交
    savePromise (req) {
      createDemand(req).then(res => {
        this.loading = false;
        this.$message.success('操作成功');
        this.cancel();
      }).catch(() => {
        this.loading = false;
      });
    },

    validateField (field) {
      this.$refs.demandapplyForm.validateField(field);
    },
    // 设置字段是否显示
    isShowCol (columnName, type) {
      if (this.dataForm.applyType == 'materialApply' && (
        (['materialBusinessAttributeName', 'psaName', 'subjectName', 'taxRateLabel'].includes(columnName) && this.isEdit) ||
        (['materialBusinessAttributeNumber', 'psaId', 'subjectCode', 'taxRate'].includes(columnName) && !this.isEdit) || type == 'serveApply'
      )) {
        return false;
      }
      if (this.dataForm.applyType == 'serveApply' && (
        (['psaName', 'taxRateLabel'].includes(columnName) && this.isEdit) ||
        (['psaId', 'taxRate'].includes(columnName) && !this.isEdit) || type == 'materialApply'
      )) {
        return false;
      }
      return true;
    },
    // 求和
    getSum () {
      let applyDetails = this.applyDetails;
      let sums = null;
      let values;
      if (this.dataForm.applyType == 'materialApply') {
        values = applyDetails.map(item => Number(item.estimateTotalPriceWithTax));
      } else if (this.dataForm.applyType == 'serveApply') {
        values = applyDetails.map(item => Number(item.estimatePriceWithTax));
      }
      if (!values.every(value => isNaN(value))) {
        sums = values.reduce((prev, curr) => {
          const value = Number(curr);
          if (!isNaN(value)) {
            return prev + curr;
          } else {
            return prev;
          }
        }, 0);
      }
      this.dataForm.estimateTotalPriceWithTax = sums.toFixed(2);
      this.refreshColumn();
    },
    // 金额展示
    getMoneyLabel (data) {
      if (data) {
        return this.toThousands(data.toString());
      } else {
        return '--';
      }
    },
    // 千分位转换
    toThousands (num) {
      let arr = num.split('.');
      let a0 = (arr[0] || 0).toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,');
      return arr.length > 1 ? a0 + '.' + arr[1] : a0;
    },
    disabledEnterDate (startValue, row) {
      const endValue = row.completeDate;
      if (!startValue || !endValue) {
        return false;
      }
      return moment(startValue, 'YYYY-MM-DD').valueOf() > moment(endValue, 'YYYY-MM-DD').valueOf();
    },
    disabledCompleteDate (endValue, row) {
      const startValue = row.enterDate;
      if (!endValue || !startValue) {
        return false;
      }
      return moment(startValue, 'YYYY-MM-DD').valueOf() >= moment(endValue, 'YYYY-MM-DD').valueOf();
    },
    // 弹窗关闭回调方法
    cancel (type) {
      this.$emit('cancel', type);
      this.reset();
    },
    reset () {
      let dictMap = Object.assign({}, this.dictMap);
      Object.assign(this.$data, this.$options.data());
      this.$refs['demandapplyForm'].resetFields();
      this.$refs['demandapplyForm'].clearValidate();
      this.dictMap = dictMap;
    }
  }
};
</script>

<style lang="less" scoped>
.drawer-form-com {
  padding: 12px 12px 0px 0px;
}

:deep(.ant-form-item){
  width: 100%;
  display: inline-flex;
}

.color-dark-light {
  :deep(.ant-form-item-label) {
    line-height: 20px;
  }

  :deep(.ant-form-item-control) {
    line-height: 20px;
  }
}
.right-btn {
  width: 100%;
  text-align: right;
  padding-bottom: 10px;
}
.info-circle{
  margin-left: 12px;
  cursor: pointer;
}

.amt-content {
  width: 100%;
  display: flex;
  justify-content: flex-end;

  .amt-content-right {
    width: 260px;
    margin: 9px 23px;
  }

  .amt-content-item {
    display: flex;
    justify-content: space-between;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    font-weight: 550;
    line-height: 22px;

    .item-label {
      color: #222222;
    }

    .item-value {
      color: #222222;
    }
  }
}
.operator-label {
  :deep(.ant-form-item-label) {
    width: 0px !important;
  }
  :deep(.ant-form-explain) {
    margin-left: 10px;
  }
}
.solar-eye-dark {
  .amt-content{
    border-color: #364457 !important;
  }
  .item-label, .item-value {
    color: #F7F7F7 !important;
  }
}
</style>
