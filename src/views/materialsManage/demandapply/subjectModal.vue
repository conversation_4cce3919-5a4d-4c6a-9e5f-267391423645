<!-- 关联费用项目弹窗 -->
<template>
  <a-modal ref="modalview" :title="title" v-model="visible" :maskClosable="false" centered @cancel="cancel" destroy-on-close width="60vw" :bodyStyle="{ 'height': '520px' }">
    <div class="content">
      <div class="left">
        <div class="left-head">
          <span>费用项目</span>
          <span class="expand" @click="expandedAll">{{ expandedType ? '收起' : '展开' }}</span>
        </div>
        <a-tree :treeData="treeData" :expandedKeys.sync="expandedKeys" :selectedKeys.sync="selectedKeys"
                :replace-fields="{ children: 'children', title: 'subjectName', key: 'subjectCode' }"
                @expand="expand" @select="handleTreeSelect" />
      </div>
      <div class="center">
        <div class="center-operation">
          <a-input-search v-model="keyword" @pressEnter="handleSearch" @search="handleSearch" placeholder="请输入关键词搜索" />
        </div>
        <a-spin :spinning="pageLoading">
          <vxe-table ref="vxeTable" :data="data" size="small" :seq-config="{ startIndex: (page - 1) * size }" resizable
                     :height="400" highlight-hover-row show-overflow="title" class='my-table'
                     :radio-config="{ highlight: true, trigger: 'row' }" @radio-change="radioChangeEvent">
            <vxe-table-column type="radio" width="50"></vxe-table-column>
            <vxe-table-column type="seq" width="80" title="序号"></vxe-table-column>
            <vxe-table-column v-for="item in columnList" :key="item.field" show-overflow="title" :formatter="tabFormatter"
                              :field="item.field" :title="item.title" :min-width="(item.width ? item.width : 100)"/>
            <template v-slot:empty>
              <span>查询无数据</span>
            </template>
          </vxe-table>
          <!--分页组件-->
          <page-pagination :pageSize="size" :current="page" :total="total" @size-change="sizeChange" />
        </a-spin>

      </div>
    </div>
    <div slot="footer" class="modal-footer">
      <a-button size="default" @click="cancel()">取消</a-button>
      <a-button size="default" class="solar-eye-btn-primary" :disabled='Object.keys(chooseData).length === 0' @click="choose()">确定</a-button>
    </div>
  </a-modal>
</template>

<script>
import { getExpenseSubjectTreeApi, getExpenseSubjectList, getAvailableBudget } from '@/api/materialsManage/demandapply';
import { subjectColumn } from '../js/column';
import { getWidth } from '@/utils/util';
import { LeftMixin } from '@/mixins/LeftMixin';
import tableHeight from '@/mixins/tableHeightAndSearchModel';
import mixin from '@views/materialsManage/js/mixin';
export default {
  name: 'associationModel',
  mixins: [LeftMixin, tableHeight, mixin],
  model: {
    prop: 'visible',
    event: 'change'
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    rowData: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  watch: {
    'visible' (val, old) {
      if (val) {
        this.init();
      }
    }
  },
  data () {
    return {
      title: '选择费用项目',
      pageLoading: false,
      expandedType: false,
      treeData: [],
      expandedKeys: [],
      selectedKeys: [],
      keyword: undefined,
      columnList: subjectColumn,
      // 表格相关参数
      data: [],
      chooseData: {},
      // 分页参数
      total: 0,
      page: 1,
      size: 10
    };
  },
  created () {
    this.columnList.forEach(item => {
      if (!item.width) {
        item.width = getWidth(item.title);
      }
    });
  },
  methods: {
    init () {
      this.getExpenseSubjectTree();
    },
    // 表格列刷新
    refreshColumn () {
      this.$nextTick(() => {
        let $table = this.$refs.vxeTable;
        $table && $table.refreshColumn();
      });
    },
    // 获取费用项目树
    getExpenseSubjectTree () {
      let requestMap = {
        entryDate: this.rowData.enterDate
      };
      getExpenseSubjectTreeApi(requestMap).then(res => {
        let treeNodes = [{
          subjectName: '全部',
          subjectCode: '',
          children: res.result_data
        }];
        this.treeData = treeNodes;
        // 初始化加载时默认顶级节点
        let top = this.treeData[0];
        this.selectedKeys.push(top.subjectCode);
        this.expandedKeys.push(top.subjectCode);
        this.queryData();
      }).catch(() => { });
    },
    // 查询表格列表数据
    async queryData () {
      let self = this;
      self.pageLoading = true;
      self.chooseData = {};
      let $table = this.$refs.vxeTable;
      $table && await $table.clearScroll();
      let { page, size } = this;
      const subjectCode = this.selectedKeys[0];
      const params = {
        subjectCode,
        entryDate: this.rowData.enterDate,
        keyword: this.keyword,
        curPage: page,
        size: size
      };
      getExpenseSubjectList(params).then((res) => {
        self.pageLoading = false;
        let data = res.result_data;
        self.data = data.rows;
        self.total = data.total;
        let subjectCode = self.rowData.subjectCode;
        if (subjectCode) {
          let chosenData = self.data.find(item => item.subjectCode == subjectCode);
          self.chooseData = chosenData || {};
          this.$nextTick(() => {
            this.$refs.vxeTable.setRadioRow(chosenData);
          });
        }
      }).catch(() => {
        self.data = [];
        self.total = 0;
        self.pageLoading = false;
      });
    },
    // 树搜索查询
    handleTreeSelect (selectedKeys, { selected, selectedNodes, node, event }) {
      this.selectedKeys = [node.eventKey];
      this.expandedKeys.push(node.eventKey);
      if (selectedKeys.length) {
        this.queryData();
      }
    },
    // 模糊搜索
    handleSearch () {
      this.queryData();
    },
    // 重置
    resetParams () {
      Object.assign(this.searchData, this.$options.data().searchData);
      this.queryData();
    },
    // 查询点击事件
    pageChange () {
      this.page = 1;
      this.queryData();
    },
    // 分页事件
    sizeChange (current, pageSize) {
      this.page = current;
      this.size = pageSize;
      this.queryData();
    },
    // 行点击事件
    radioChangeEvent ({ row }) {
      this.rowData.subjectCode = undefined;
      this.chooseData = row;
    },
    // 确定
    choose () {
      let self = this;
      const psaId = this.rowData.psaId == 'all' ? null : this.rowData.psaId;
      if (psaId) {
        let subjectCode = self.chooseData.subjectCode;
        let enterDate = self.rowData.enterDate;
        let params = {
          'psaId': psaId,
          'subjectCode': subjectCode,
          'entryDate': enterDate
        };
        getAvailableBudget(params).then(res => {
          let data = res.result_data;
          self.returnRow(data);
        });
      } else {
        self.returnRow();
      }
    },
    returnRow (data) {
      let self = this;
      let chooseRow = Object.assign(self.rowData, {
        subjectCode: self.chooseData.subjectCode,
        subjectName: self.chooseData.subjectName,
        budgetSubjectCode: data ? data.budgetSubjectCode : undefined,
        budgetSubjectName: data ? data.budgetSubjectName : undefined,
        remainingBudget: data ? data.availableAmount : undefined
      });
      self.$emit('cancel', chooseRow);
      self.cancel();
    },
    // 展开或收起
    expandedAll () {
      function getChildCodes (rows) {
        return rows.reduce((acc, row) => {
          return acc.concat([row.subjectCode].concat(row.children ? getChildCodes(row.children) : []));
        }, []);
      }
      if (this.expandedType) {
        this.expandedKeys = [];
      } else {
        const codes = getChildCodes(this.treeData);
        this.expandedKeys = codes;
      }
      this.expandedType = !this.expandedType;
    },
    expand (expandedKeys, info) {
      this.expandedKeys = expandedKeys;
    },
    // 关闭回调方法
    cancel () {
      this.loading = false;
      Object.assign(this.$data, this.$options.data());
      this.$emit('change', false);
      this.visible = false;
      // 确保关闭后可以再次打开
      this.$refs.modalview.$destroyAll();
    }
  }
};
</script>

<style lang="less" scoped>
.content {
  display: flex;
  height: 100%;

  .left {
    height: 100%;
    width: 275px;
    padding-right: 16px;
    overflow: auto;

    .left-head {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .expand {
        color: @primary-color;
        cursor: pointer;
      }
    }

    :deep(.ant-tree-title) {
      max-width: 200px;
      overflow: hidden;
      display: inline-block;
      text-overflow: ellipsis;
    }
  }

  .center {
    flex: 1;
    border-left: 1px solid #D8D8D8;
    padding: 0 24px;

    .center-operation {
      width: 230px;
      margin-bottom: 16px;
    }
  }
}
.solar-eye-dark {
  .left-head {
    color: #D0D2D5 !important;
  }
  .expand {
    color: #409eff !important;
  }
}
</style>
