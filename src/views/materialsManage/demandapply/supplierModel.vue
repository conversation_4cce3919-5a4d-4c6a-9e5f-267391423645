<!-- 供应商弹窗 -->
<template>
  <a-modal ref="modalview" :title="title" v-model="visible" :maskClosable="false" centered @cancel="cancel" destroy-on-close width="60%">
    <a-spin :spinning="pageLoading">
      <div class="solar-eye-search-model">
        <a-row :gutter="24" class="solar-eye-search-content">
          <a-col :xxl="6" :xl="8" :md="12">
            <div class="search-item">
              <a-input v-model="searchData.keyword" @blur="searchData.keyword = $trim($event)" size="default"
                     @pressEnter='pageChange' placeholder="请输入" allowClear></a-input>
            </div>
          </a-col>
          <a-col :xxl="6" :xl="8" :md="12">
            <div class="search-item">
              <throttle-button label="查询" size="default" class="solar-eye-btn-primary" @click="pageChange()"/>
              <a-button @click="resetParams">重置</a-button>
            </div>
          </a-col>
        </a-row>
      </div>
      <div class="solar-eye-main-content">
        <vxe-table ref="vxeTable" :data="data" :height="330" resizable show-overflow
                   highlight-hover-row size="small" :seq-config="{ startIndex: (page - 1) * size }" class='my-table'
                   :radio-config="{ highlight: true, trigger: 'row' }"
                   @radio-change="radioChangeEvent">
          <vxe-table-column type="radio" width="50" fixed="left"></vxe-table-column>
          <vxe-table-column type="seq" width="80" title="序号"></vxe-table-column>
          <vxe-table-column v-for="item in columnList" :key="item.field" show-overflow="title" :formatter="tabFormatter"
                            :field="item.field" :title="item.title" :min-width="(item.width ? item.width : 100)">
          </vxe-table-column>
          <template v-slot:empty>
            <span>查询无数据</span>
          </template>
        </vxe-table>
        <!--分页组件-->
        <page-pagination :pageSize="size" :current="page" :total="total" @size-change="sizeChange" />
      </div>
    </a-spin>
    <div slot="footer" class="modal-footer">
      <a-button size="default" @click="cancel()">取消</a-button>
      <a-button size="default" class="solar-eye-btn-primary" :disabled='Object.keys(chooseData).length === 0' @click="choose()">确定</a-button>
    </div>
  </a-modal>
</template>

<script>
import { getSupplierPage } from '@/api/materialsManage/demandapply';
import { getWidth } from '@/utils/util';
import { LeftMixin } from '@/mixins/LeftMixin';
import initDict from '@/mixins/initDict';
import tableHeight from '@/mixins/tableHeightAndSearchModel';
import mixin from '@views/materialsManage/js/mixin';
import { supplierColumn } from '@views/materialsManage/js/column';
export default {
  name: 'supplierModel',
  mixins: [initDict, LeftMixin, tableHeight, mixin],
  model: {
    prop: 'visible',
    event: 'change'
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    rowData: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  watch: {
    'visible' (val, old) {
      if (val) {
        this.init();
      }
    }
  },
  data () {
    return {
      title: '请选择供应商',
      pageLoading: false,
      searchData: {
        'keyword': undefined
      },
      columnList: supplierColumn,
      // 表格相关参数
      data: [],
      chooseData: {},
      // 分页参数
      total: 0,
      page: 1,
      size: 10
    };
  },
  created () {
    this.columnList.forEach(item => {
      if (!item.width) {
        item.width = getWidth(item.title);
      }
    });
  },
  methods: {
    init () {
      this.refreshColumn();
      this.queryData();
    },
    // 表格列刷新
    refreshColumn () {
      this.$nextTick(() => {
        let $table = this.$refs.vxeTable;
        $table && $table.refreshColumn();
      });
    },
    // 查询表格列表数据
    async queryData () {
      let self = this;
      self.pageLoading = true;
      self.chooseData = {};
      let $table = this.$refs.vxeTable;
      $table && await $table.clearScroll();
      let requestMap = {};
      let { page, size } = this;
      requestMap = Object.assign(self.searchData, {
        curPage: page,
        size: size
      });
      getSupplierPage(requestMap).then((res) => {
        self.pageLoading = false;
        let data = res.result_data;
        self.data = data.rows;
        self.total = data.total;
        let num = this.rowData.num;
        if (num) {
          let chosenData = self.data.find(item => (item.supplierCode == num));
          self.chooseData = chosenData || {};
          this.$nextTick(() => {
            this.$refs.vxeTable.setRadioRow(chosenData);
          });
        }
      }).catch(() => {
        self.data = [];
        self.total = 0;
        self.pageLoading = false;
      });
    },
    // 重置
    resetParams () {
      Object.assign(this.searchData, this.$options.data().searchData);
      this.queryData();
    },
    // 查询点击事件
    pageChange () {
      this.page = 1;
      this.queryData();
    },
    // 分页事件
    sizeChange (current, pageSize) {
      this.page = current;
      this.size = pageSize;
      this.queryData();
    },
    // 行点击事件
    radioChangeEvent ({ row }) {
      this.rowData.num = undefined;
      this.chooseData = row;
    },
    // 确定
    choose () {
      let self = this;
      let resDate = {};
      resDate = Object.assign(self.rowData, {
        supplierCode: self.chooseData.supplierCode,
        supplierName: self.chooseData.supplierName
      });
      self.$emit('cancel', resDate);
      self.cancel();
    },
    // 关闭回调方法
    cancel () {
      this.loading = false;
      Object.assign(this.$data, this.$options.data());
      this.$emit('change', false);
      this.visible = false;
      // 确保关闭后可以再次打开
      this.$refs.modalview.$destroyAll();
    }
  }
};
</script>

<style lang="less" scoped>
:deep(.ant-modal-body){
  padding: 0 24px;
}
.solar-eye-main-content {
  padding: 0;
}
.solar-eye-main-content .operation-btn {
  justify-content: space-between;
  height: 100%;
  margin-bottom: 0;
  .left-operation{
    display: flex;
    width: 300px;
  }
}
</style>
