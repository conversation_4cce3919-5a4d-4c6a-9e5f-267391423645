<!-- 需求申请 -->
<template>
  <div id="materialsDemandapply">
    <a-spin :spinning="pageLoading">
      <div class="solar-eye-search-model">
        <a-row :gutter="24" class="solar-eye-search-content">
          <a-col :span="24">
            <div class="search-item">
              <span class="search-label">流程状态</span>
              <a-checkbox @change="checkedAllClick" v-model="checkedAll">全部({{countAll}})</a-checkbox>
              <a-checkbox-group v-model="allStatusList" @change="statusClick">
                <a-checkbox value="1">处理中({{taskStatus0}})</a-checkbox>
                <a-checkbox value="2">已完成({{taskStatus1}})</a-checkbox>
                <a-checkbox value="3">已退回({{taskStatus2}})</a-checkbox>
                <a-checkbox value="4">已作废({{taskStatus3}})</a-checkbox>
              </a-checkbox-group>
            </div>
          </a-col>
          <a-col :xxl="6" :xl="8" :md="12">
            <div class="search-item">
              <span class="search-label">需求单编号</span>
              <a-input v-model="searchData.applyCode" @blur="searchData.applyCode = $trim($event)" size="default"
                       @pressEnter='pageChange' placeholder="请输入需求单编号" allowClear></a-input>
            </div>
          </a-col>
          <a-col :xxl="6" :xl="8" :md="12">
            <div class="search-item">
              <span class="search-label">部门</span>
              <a-input v-model="searchData.departName" @blur="searchData.departName = $trim($event)" size="default"
                       @pressEnter='pageChange' placeholder="请输入部门" allowClear></a-input>
            </div>
          </a-col>
          <a-col :xxl="6" :xl="8" :md="12">
            <div class="search-item">
              <span class="search-label">申请类型</span>
              <a-select size="default" v-model="searchData.applyType" @select='pageChange' placeholder="请选择申请类型" allowClear>
                <a-select-option v-for="item in dictMap.requirement_apply_type" :key="item.codeValue" :value="item.codeValue">
                  {{item.dispName}}
                </a-select-option>
              </a-select>
            </div>
          </a-col>
          <template v-if="toggleSearchStatus">
            <a-col :xxl="6" :xl="8" :md="12">
              <div class="search-item">
                <span class="search-label">申请人</span>
                <a-input v-model="searchData.applyUserName" @blur="searchData.applyUserName = $trim($event)" size="default"
                         @pressEnter='pageChange' placeholder="请输入申请人" allowClear></a-input>
              </div>
            </a-col>
            <a-col :xxl="6" :xl="8" :md="12">
              <div class="search-item">
                <span class="search-label">申请日期</span>
                <a-range-picker v-model="searchData.timeRange" format="YYYY-MM-DD" @change="changeTimeRange"
                                :placeholder="['开始日期', '结束日期']" allowClear></a-range-picker>
              </div>
            </a-col>
          </template>
          <a-col :xxl="6" :xl="8" :md="12">
            <div class="search-item">
              <throttle-button label="查询" size="default" class="solar-eye-btn-primary" @click="pageChange()"/>
              <a-button @click="resetParams">重置</a-button>
              <span class="com-color" @click="handleToggleSearch">
                {{ toggleSearchStatus ? "收起" : "展开" }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
              </span>
            </div>
          </a-col>
        </a-row>
      </div>

      <div class="solar-eye-gap"></div>
      <div class="solar-eye-main-content">
        <div class="operation-btn">
          <!-- 筛选列下拉框 -->
          <a-dropdown trigger="click">
            <span class='filter-column'>
              筛选列 <svg-icon icon-class='filter-column' style="font-size: 16px;" />
            </span>
            <a-menu slot="overlay">
              <a-checkbox-group v-model="checkColumn" @change="refreshColumn">
                <a-checkbox class="drop-down" v-for="(column,index) in columnList" :key="index" :value="column.name">
                  {{column.comment}}
                </a-checkbox>
              </a-checkbox-group>
            </a-menu>
          </a-dropdown>
          <throttle-button label="发起申请" perms='demandapply:add' size="default" class="solar-eye-btn-primary" @click="showDetail('1', {})"></throttle-button>
        </div>
        <vxe-table :data="data" :height="tableHeight - 24" ref="multipleTable" :seq-config="{startIndex: (page - 1) * size}"
                   resizable show-overflow highlight-hover-row size="small" class='my-table'>
          <vxe-table-column type="seq" width="80" title="序号"></vxe-table-column>
          <vxe-table-column show-overflow="title" v-for="item in columnList" :key="item.name" :field="item.name"
                            :visible="checkColumn.includes(item.name)" :title="item.comment" :min-width="item.width || 140"
                            :fixed="item.fixed ? item.fixed : ''">
            <template v-slot:default="{row}">
              <div v-if="item.name == 'flowStsLabel' && row.flowStsLabel" class="status-info-left">
                <div :class="`statusCol color-${getColor(row.flowStsLabel)}`"></div>
                <div>{{ row.flowStsLabel }}</div>
              </div>
              <span v-else>{{ getLabel(row[item.name], null) }}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column title="操作" fixed="right" width="160" :resizable="false" class-name="fixed-right-column-160">
            <template v-slot="{ row }">
              <throttle-button title="详情" icon="file-text" @click="showDetail('3', row)" class="operation-btn-hover"/>
              <throttle-button title="编辑" v-if="row.hasPermission && row.taskDefKey == 'act0'" icon="edit" @click="showDetail('2', row)" class="operation-btn-hover"/>
            </template>
          </vxe-table-column>
          <template v-slot:empty>
            <span>查询无数据</span>
          </template>
        </vxe-table>
        <!--分页组件-->
        <page-pagination :pageSize="size" :current="page" :total="total" @size-change="sizeChange" />
      </div>
    </a-spin>
    <!-- 表单 -->
    <drawer-view ref="demandapplyForm" @cancel="queryData" parentId="drawerViewDetail" />
  </div>
</template>

<script>
import { getDemandList } from '@/api/materialsManage/demandapply';
import moment from 'moment';
import { getWidth } from '@/utils/util';
import { LeftMixin } from '@/mixins/LeftMixin';
import initDict from '@/mixins/initDict';
import tableHeight from '@/mixins/tableHeightAndSearchModel';
import mixin from '../js/mixin';
import { demandapplyCheck, demandapplyColumn } from '@views/materialsManage/js/column';
export default {
  mixins: [initDict, LeftMixin, tableHeight, mixin],
  name: 'demandapply',
  components: {},
  data () {
    return {
      pageLoading: false,
      exp_loading: false,
      // 查询参数
      searchData: {
        applyCode: undefined,
        departName: undefined,
        applyType: undefined,
        applyUserName: undefined,
        timeRange: [],
        applyDateFrom: null,
        applyDateTo: null
      },
      columnList: demandapplyColumn,
      checkColumn: demandapplyCheck,
      // 表格相关参数
      data: [],
      // 分页参数
      total: 0,
      page: 1,
      size: 10
    };
  },
  created () {
    // 加载数据字典
    this.getDictMap('requirement_apply_type');
    this.tabKind = '1';
    this.allStatusList = this.defAll = ['1', '2', '3', '4'];
    this.columnList.forEach(item => {
      if (!item.width) {
        item.width = getWidth(item.comment);
      }
    });
    this.queryData();
  },
  mounted () {
    if (Vue.ls.get('formWorkbenchToDemandapply')) {
      let row = Vue.ls.get('formWorkbenchToDemandapply');
      this.showDetail('3', row);
      Vue.ls.remove('formWorkbenchToDemandapply');
    }
  },
  methods: {
    moment,
    // 表-列刷新
    refreshColumn () {
      this.$nextTick(() => {
        let $table = this.$refs.multipleTable;
        $table && $table.refreshColumn();
      });
    },
    // 查询
    async queryData () {
      let self = this;
      self.pageLoading = true;
      let $table = this.$refs.multipleTable;
      $table && await $table.clearScroll();
      let requestMap = {
        curPage: self.page,
        size: self.size,
        flowSts: self.allStatusList
      };
      Object.assign(requestMap, self.searchData);
      getDemandList(requestMap).then(res => {
        let data = res.result_data;
        self.data = data.rows;
        self.total = data.total;
        self.countStsData(data.countInfo);
        self.pageLoading = false;
      }).catch(() => {
        self.data = [];
        self.total = 0;
        self.pageLoading = false;
      });
    },
    // 统计各状态数据量
    countStsData (data) {
      let fakeResData = {
        'taskStatus0': data.processing || 0,
        'taskStatus1': data.complete || 0,
        'taskStatus2': data.returned || 0,
        'taskStatus3': data.cancel || 0
      };
      this.assingnData(fakeResData);
    },
    // 表单
    showDetail (type, row) {
      this.$refs.demandapplyForm.init(type, row, '/materialsManage/demandapply/form');
    },
    // 重置
    resetParams () {
      Object.assign(this.searchData, this.$options.data().searchData);
      this.queryData();
    },
    // 查询点击事件
    pageChange () {
      this.page = 1;
      this.queryData();
    },
    // 分页事件
    sizeChange (p, e) {
      this.page = p;
      this.size = e;
      this.queryData();
    },
    // 日期change
    changeTimeRange (moment, data) {
      this.searchData.applyDateFrom = data[0];
      this.searchData.applyDateTo = data[1];
      this.pageChange();
    }
  }
};
</script>
