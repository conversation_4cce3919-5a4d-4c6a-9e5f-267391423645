<!-- 任务 事故事件弹窗 -->
<template>
  <a-modal ref="modalview" :title="title" v-model="visible" :maskClosable="false" centered @cancel="cancel" destroy-on-close width="90%">
    <a-spin :spinning="pageLoading">
      <div class="solar-eye-search-model">
        <a-row :gutter="24" class="solar-eye-search-content">
          <template v-if="rowData.pageType == '1'">
            <a-col :xxl="12" :xl="16" :md="24">
              <role-tree-select @change="treeChange" ref="unPlanRoleTree"></role-tree-select>
            </a-col>
            <a-col :xxl="6" :xl="8" :md="12">
              <div class="search-item">
                <span class="search-label">事故事件编号</span>
                <a-input v-model="searchData.accidentNum" @blur="searchData.accidentNum = $trim($event)" size="default"
                         placeholder="请输入事故事件编号" allowClear></a-input>
              </div>
            </a-col>
            <template v-if="toggleSearchStatus">
              <a-col :xxl="6" :xl="8" :md="12">
                <div class="search-item">
                  <span class="search-label">类别</span>
                  <a-select size="default" v-model="searchData.accidentTypeList" mode='multiple' :maxTagCount="1" placeholder="请选择" allowClear style="width: 100%;">
                    <a-select-option v-for="item in dictMap.accident_type" :key="item.dataValue" :value="item.dataValue">{{item.dataLable}}</a-select-option>
                  </a-select>
                </div>
              </a-col>
              <a-col :xxl="6" :xl="8" :md="12">
                <div class="search-item">
                  <span class="search-label">发生时间</span>
                  <a-range-picker v-model="searchData.happenTime" format="YYYY-MM-DD" value-format="YYYY-MM-DD"
                                  :placeholder="['开始日期', '结束日期']" allowClear></a-range-picker>
                </div>
              </a-col>
            </template>
          </template>
          <template v-else>
            <a-col :xxl="6" :xl="8" :md="12">
              <div class="search-item">
                <span class="search-label">任务编号</span>
                <a-input v-model="searchData.taskNo" @blur="searchData.taskNo = $trim($event)" size="default"
                         placeholder="请输入任务编号" allowClear></a-input>
              </div>
            </a-col>
            <a-col :xxl="6" :xl="8" :md="12">
              <div class="search-item">
                <span class="search-label">业务来源</span>
                <a-select v-model="searchData.opsBusinessSource" placeholder="请选择业务来源" style="width: 100%; height: 32px" allowClear>
                  <a-select-option v-for="item in dictMap.ops_business_source" :key="item.dataValue" :value="item.dataValue">
                    {{ item.dataLable }}
                  </a-select-option>
                </a-select>
              </div>
            </a-col>
            <a-col :xxl="6" :xl="8" :md="12">
              <div class="search-item">
                <span class="search-label">计划开始</span>
                <a-range-picker v-model="searchData.planStartQuery" format="YYYY-MM-DD" @change="changeTimeRange"
                                :placeholder="['开始日期', '结束日期']" allowClear></a-range-picker>
              </div>
            </a-col>
          </template>
          <a-col :xxl="6" :xl="8" :md="12">
            <div class="search-item">
              <throttle-button label="查询" size="default" class="solar-eye-btn-primary" @click="pageChange()"/>
              <a-button @click="resetParams">重置</a-button>
              <span class="com-color" @click="handleToggleSearch" v-if="rowData.pageType == '1'">
                {{ toggleSearchStatus ? "收起" : "展开" }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
                </span>
            </div>
          </a-col>
        </a-row>
      </div>
      <div class="solar-eye-main-content">
        <vxe-table ref="vxeTable" :data="data" :height="tableHeight - 44" resizable show-overflow
                   highlight-hover-row size="small" :seq-config="{ startIndex: (page - 1) * size }" class='my-table'
                   :radio-config="{ highlight: true, trigger: 'row' }"
                   @radio-change="radioChangeEvent">
          <vxe-table-column type="radio" width="50" fixed="left"></vxe-table-column>
          <vxe-table-column type="seq" width="80" title="序号"></vxe-table-column>
          <vxe-table-column v-for="item in columnList" :key="item.field" show-overflow="title" :formatter="tabFormatter"
                            :field="item.field" :title="item.title" :min-width="(item.width ? item.width : 100)">
          </vxe-table-column>
          <template v-slot:empty>
            <span>查询无数据</span>
          </template>
        </vxe-table>
        <!--分页组件-->
        <page-pagination :pageSize="size" :current="page" :total="total" @size-change="sizeChange" />
      </div>
    </a-spin>
    <div slot="footer" class="modal-footer">
      <a-button size="default" @click="cancel()">取消</a-button>
      <a-button size="default" class="solar-eye-btn-primary" :disabled='Object.keys(chooseData).length === 0' @click="choose()">确定</a-button>
    </div>
  </a-modal>
</template>

<script>
import { queryAccidentList } from '@/api/isolarErp/safetyquality/accident';
import { getTaskHallPage } from '@api/taskCenter/task';
import { getWidth } from '@/utils/util';
import { LeftMixin } from '@/mixins/LeftMixin';
import initDict from '@/mixins/initDict';
import tableHeight from '@/mixins/tableHeightAndSearchModel';
import mixin from '@views/materialsManage/js/mixin';
import { associationColumn1, associationColumn2 } from '@views/materialsManage/js/column';
export default {
  name: 'associationModel',
  mixins: [initDict, LeftMixin, tableHeight, mixin],
  model: {
    prop: 'visible',
    event: 'change'
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    rowData: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  watch: {
    'visible' (val, old) {
      if (val) {
        this.init();
      }
    }
  },
  data () {
    return {
      title: '请选择关联的事故事件',
      pageLoading: false,
      searchData: {
        'deptCode': null,
        'psaIds': [],
        'accidentNum': undefined,
        'accidentTypeList': [],
        'happenTime': [],
        'taskNo': undefined,
        'opsBusinessSource': undefined,
        'planStartQuery': []
      },
      columnList: associationColumn1,
      columnList1: associationColumn2,
      // 表格相关参数
      data: [],
      chooseData: {},
      // 分页参数
      total: 0,
      page: 1,
      size: 10
    };
  },
  created () {
    // 加载数据字典
    this.getDictMap('accident_type,ops_business_source');
    this.columnList.forEach(item => {
      if (!item.width) {
        item.width = getWidth(item.title);
      }
    });
  },
  methods: {
    init () {
      if (this.rowData.pageType == '2') {
        this.title = '请选择关联的任务';
        this.columnList = this.columnList1;
      }
      this.refreshColumn();
      this.queryData();
    },
    // 部门电站change
    treeChange (deptCode, psaIds) {
      this.page = 1;
      Object.assign(this.searchData, { 'deptCode': deptCode, 'psaIds': psaIds });
      this.queryData();
    },
    // 表格列刷新
    refreshColumn () {
      this.$nextTick(() => {
        let $table = this.$refs.vxeTable;
        $table && $table.refreshColumn();
      });
    },
    // 查询表格列表数据
    async queryData () {
      let self = this;
      self.pageLoading = true;
      self.chooseData = {};
      let $table = this.$refs.vxeTable;
      $table && await $table.clearScroll();
      let requestMap = {};
      let pageType = self.rowData.pageType;
      let getDataList = null;
      let { page, size } = this;
      if (pageType == '1') {
        let accidentTypeList = self.searchData.accidentTypeList;
        requestMap = Object.assign(self.searchData, {
          curPage: page,
          size: size,
          flowSts: '1,2,3',
          accidentType: accidentTypeList.join(',')
        });
        getDataList = queryAccidentList;
      } else {
        let psaId = self.rowData.psaId;
        let taskType = self.rowData.relatedTaskType;
        let taskSubType = self.rowData.relatedTaskSubType;
        requestMap = Object.assign(self.searchData, {
          curPage: page,
          size: size,
          deptCode: 'A09',
          taskStatus: '0,1',
          psaIds: [psaId],
          taskTypeList: [taskType],
          taskSubTypeList: taskSubType ? [taskSubType] : []
        });
        getDataList = getTaskHallPage;
      }
      getDataList(requestMap).then((res) => {
        self.pageLoading = false;
        let data = res.result_data;
        self.data = data.rows;
        self.total = data.total;
        let num = this.rowData.num;
        if (num) {
          let chosenData = self.data.find(item => (item.accidentNum == num) || (item.taskNo == num));
          self.chooseData = chosenData || {};
          this.$nextTick(() => {
            this.$refs.vxeTable.setRadioRow(chosenData);
          });
        }
      }).catch(() => {
        self.data = [];
        self.total = 0;
        self.pageLoading = false;
      });
    },
    // 重置
    resetParams () {
      Object.assign(this.searchData, this.$options.data().searchData);
      if (this.rowData.pageType == '1') {
        this.$refs.unPlanRoleTree.reset();
      } else {
        this.queryData();
      }
    },
    // 查询点击事件
    pageChange () {
      this.page = 1;
      this.queryData();
    },
    // 分页事件
    sizeChange (current, pageSize) {
      this.page = current;
      this.size = pageSize;
      this.queryData();
    },
    // 行点击事件
    radioChangeEvent ({ row }) {
      this.rowData.num = undefined;
      this.chooseData = row;
    },
    // 确定
    choose () {
      let self = this;
      let resDate = {};
      resDate.pageType = self.rowData.pageType;
      if (self.rowData.pageType == '1') {
        resDate.accidentNum = self.chooseData.accidentNum;
      } else {
        resDate = Object.assign(self.rowData, {
          relatedTaskNo: self.chooseData.taskNo,
          enterDate: self.chooseData.planStartTime,
          completeDate: self.chooseData.planEndTime,
          worksQuantity: self.chooseData.worksQuantity,
          worksQuantityUnit: self.chooseData.worksQuantityUnit,
          worksQuantityUnitLabel: self.chooseData.worksQuantityUnitLabel
        });
      }
      self.$emit('cancel', resDate);
      self.cancel();
    },
    // 日期change
    changeTimeRange (moment, data) {
      this.searchData.planStartQueryStartTime = data[0];
      this.searchData.planStartQueryEndTime = data[1];
    },
    // 关闭回调方法
    cancel () {
      this.loading = false;
      let dictMap = Object.assign({}, this.dictMap);
      Object.assign(this.$data, this.$options.data());
      this.dictMap = dictMap;
      this.$emit('change', false);
      this.visible = false;
      // 确保关闭后可以再次打开
      this.$refs.modalview.$destroyAll();
    }
  }
};
</script>

<style lang="less" scoped>
:deep(.ant-modal-body){
  padding: 0 24px;
}
.solar-eye-main-content {
  padding: 0;
}
.solar-eye-main-content .operation-btn {
  justify-content: space-between;
  height: 100%;
  margin-bottom: 0;
  .left-operation{
    display: flex;
    width: 300px;
  }
}
</style>
