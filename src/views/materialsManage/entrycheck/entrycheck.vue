<!-- 物资入库 -->
<template>
  <div id="materialsEntrycheck">
    <a-spin :spinning="pageLoading">
      <div class="solar-eye-search-model">
        <a-row :gutter="24" class="solar-eye-search-content">
          <a-col :span="24">
            <div class="search-item">
              <span class="search-label">流程状态</span>
              <a-checkbox @change="checkedAllClick" v-model="checkedAll">全部({{countAll}})</a-checkbox>
              <a-checkbox-group v-model="allStatusList" @change="statusClick">
                <a-checkbox value="4">待提交({{taskStatus4}})</a-checkbox>
                <a-checkbox value="0">处理中({{taskStatus0}})</a-checkbox>
                <a-checkbox value="1">已完成({{taskStatus1}})</a-checkbox>
                <a-checkbox value="2">已退回({{taskStatus2}})</a-checkbox>
                <a-checkbox value="3">已作废({{taskStatus3}})</a-checkbox>
              </a-checkbox-group>
            </div>
          </a-col>
          <a-col :xxl="6" :xl="8" :md="12">
            <div class="search-item">
              <span class="search-label">仓库名称</span>
              <a-select size="default" v-model="searchData.warehouseCodeList" placeholder="请选择仓库名称" @change="pageChange()"
                        mode='multiple' :maxTagCount="1" allowClear option-filter-prop="children" :filter-option="filterOption">
                <a-select-option v-for="item in warehouseOptions" :key="item.warehouseCode" :value="item.warehouseCode">
                  {{item.warehouseName}}
                </a-select-option>
              </a-select>
            </div>
          </a-col>
          <a-col :xxl="5" :xl="8" :md="12">
            <div class="search-item">
              <span class="search-label">资产归属</span>
              <a-select size="default" v-model="searchData.belongCode" @change='pageChange' placeholder="请选择资产归属" allowClear>
                <a-select-option v-for="item in dictMap.property_belong" :key="item.codeValue" :value="item.codeValue">
                  {{item.dispName}}
                </a-select-option>
              </a-select>
            </div>
          </a-col>
<!--          <a-col :xxl="6" :xl="8" :md="12">-->
<!--            <div class="search-item">-->
<!--              <span class="search-label">物资来源</span>-->
<!--              <a-select size="default" v-model="searchData.applyTypeCode" placeholder="请选择物资来源" allowClear>-->
<!--                <a-select-option v-for="item in dictMap.material_from" :key="item.codeValue" :value="item.codeValue">-->
<!--                  {{item.dispName}}-->
<!--                </a-select-option>-->
<!--              </a-select>-->
<!--            </div>-->
<!--          </a-col>-->
<!--          <template v-if="toggleSearchStatus">-->
          <a-col :xxl="5" :xl="8" :md="12">
            <div class="search-item">
              <span class="search-label">验收日期</span>
              <a-range-picker v-model="searchData.timeRange" format="YYYY-MM-DD" @change="changeTimeRange"
                              :placeholder="['开始日期', '结束日期']" allowClear></a-range-picker>
            </div>
          </a-col>
          <a-col :xxl="5" :xl="8" :md="12">
            <div class="search-item">
              <span class="search-label">验收人</span>
              <a-input v-model="searchData.acceptancePerson" @blur="searchData.acceptancePerson = $trim($event)" size="default"
                       @pressEnter='pageChange' placeholder="请输入验收人" allowClear></a-input>
            </div>
          </a-col>
          <a-col :xxl="3" :xl="8" :md="12">
            <div class="search-item">
              <throttle-button label="查询" size="default" class="solar-eye-btn-primary" @click="pageChange()"/>
              <a-button @click="resetParams">重置</a-button>
            </div>
          </a-col>
        </a-row>
      </div>

      <div class="solar-eye-gap"></div>
      <div class="solar-eye-main-content">
        <div class="operation-btn">
          <!-- 筛选列下拉框 -->
          <a-dropdown trigger="click">
            <a class="ant-dropdown-link" @click="e => e.preventDefault()">筛选列
              <a-icon type="form" />
            </a>
            <a-menu slot="overlay">
              <a-checkbox-group v-model="checkColumn" @change="refreshColumn">
                <a-checkbox class="drop-down" v-for="(column,index) in columnList" :key="index" :value="column.name">
                  {{column.comment}}
                </a-checkbox>
              </a-checkbox-group>
            </a-menu>
          </a-dropdown>
          <throttle-button label="入库登记" perms='entrycheck:add' size="default" class="solar-eye-btn-primary" @click="showDetail('1', {})"></throttle-button>
        </div>
        <vxe-table :data="data" :height="tableHeight - 24" ref="multipleTable" :seq-config="{startIndex: (page - 1) * size}"
                   resizable align="center" border show-overflow highlight-hover-row size="small">
          <vxe-table-column type="seq" width="80" title="序号"></vxe-table-column>
          <vxe-table-column show-overflow="title" v-for="item in columnList" :key="item.name" :field="item.name"
                            :visible="checkColumn.includes(item.name)" :title="item.comment" :min-width="item.width || 140">
            <template v-slot:default="{row}">
              <div v-if="item.name == 'flowStsLabel' && row.flowStsLabel" class="status-info">
                <div :class="`statusCol color-${getColor(row.flowStsLabel)}`"></div>
                <div>{{ row.flowStsLabel }}</div>
              </div>
              <span v-else>{{ getLabel(row[item.name], null) }}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column title="操作" fixed="right" width="160" :resizable="false" class-name="fixed-right-column-160">
            <template v-slot="{ row }">
              <throttle-button title="详情" icon="file-text" @click="showDetail('3', row)" class="operation-btn-hover"/>
              <throttle-button title="编辑" v-if="row.hasPermission && (row.taskDefKey == 'act1' || row.taskDefKey == null)" icon="edit" @click="showDetail('2', row)" class="operation-btn-hover"/>
              <throttle-button title="审批" v-if="row.hasPermission && ['act2', 'act3'].includes(row.taskDefKey)" icon="form" @click="showDetail('4', row)" class="operation-btn-hover"/>
            </template>
          </vxe-table-column>
          <template v-slot:empty>
            <span>查询无数据</span>
          </template>
        </vxe-table>
        <!--分页组件-->
        <page-pagination :pageSize="size" :current="page" :total="total" @size-change="sizeChange" />
      </div>
    </a-spin>
    <!-- 表单 -->
    <drawer-view ref="entrycheckForm" @cancel="queryData" parentId="drawerViewDetail" />
  </div>
</template>

<script>
import { getEnterList, countData } from '@/api/materialsManage/entrycheck';
import moment from 'moment';
import { getWidth } from '@/utils/util';
import { LeftMixin } from '@/mixins/LeftMixin';
import initDict from '@/mixins/initDict';
import tableHeight from '@/mixins/tableHeightAndSearchModel';
import mixin from '../js/mixin';
import { entrycheckCheck, entrycheckColumn } from '@views/materialsManage/js/column';
export default {
  mixins: [initDict, LeftMixin, tableHeight, mixin],
  name: 'entrycheck',
  components: {},
  data () {
    return {
      pageLoading: false,
      // 查询参数
      searchData: {
        warehouseCodeList: undefined,
        belongCode: undefined,
        applyTypeCode: undefined,
        timeRange: [],
        startDate: null,
        endDate: null,
        acceptancePerson: undefined
      },
      columnList: entrycheckColumn,
      checkColumn: entrycheckCheck,
      // 表格相关参数
      data: [],
      // 分页参数
      total: 0,
      page: 1,
      size: 10
    };
  },
  created () {
    this.tabKind = '1';
    this.allStatusList = this.defAll = ['0', '1', '2', '3', '4'];
    this.columnList.forEach(item => {
      if (!item.width) {
        item.width = getWidth(item.comment);
      }
    });
    // 加载数据字典
    this.getDictMap('property_belong,material_from');
    this.queryWarehouseList({ 'byDataRole': true, 'isMainPage': true, 'defaultSelected': true });
  },
  methods: {
    moment,
    // 表-列刷新
    refreshColumn () {
      this.$nextTick(() => {
        let $table = this.$refs.multipleTable;
        $table && $table.refreshColumn();
      });
    },
    // 查询
    async queryData () {
      let self = this;
      self.pageLoading = true;
      let $table = this.$refs.multipleTable;
      $table && await $table.clearScroll();
      let requestMap = {
        curPage: self.page,
        size: self.size,
        flowSts: self.allStatusList
      };
      Object.assign(requestMap, self.searchData);
      getEnterList(requestMap).then(res => {
        self.data = res.result_data.rows;
        self.total = res.result_data.total;
        self.pageLoading = false;
      }).catch(() => {
        self.data = [];
        self.total = 0;
        self.pageLoading = false;
      });
      self.countStsData(requestMap);
    },
    // 统计各状态数据量
    countStsData (param) {
      let map = Object.assign({}, param);
      map.flowSts = ['0', '1', '2', '3', '4'];
      countData(map).then(res => {
        this.assingnData(res.result_data);
      }).catch(() => {
        this.assingnData();
      });
    },
    // 表单
    showDetail (type, row) {
      this.$refs.entrycheckForm.init(type, row, '/materialsManage/entrycheck/form');
    },
    // 重置
    resetParams () {
      Object.assign(this.searchData, this.$options.data().searchData);
      this.queryData();
    },
    // 查询点击事件
    pageChange () {
      this.page = 1;
      this.queryData();
    },
    // 分页事件
    sizeChange (p, e) {
      this.page = p;
      this.size = e;
      this.queryData();
    },
    // 日期change
    changeTimeRange (moment, data) {
      this.searchData.startDate = data[0];
      this.searchData.endDate = data[1];
      this.pageChange();
    }
  }
};
</script>
