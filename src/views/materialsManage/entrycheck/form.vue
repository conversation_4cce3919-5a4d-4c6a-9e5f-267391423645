<!-- 物资入库表单 -->
<template>
  <div class="drawer-form-com">
    <div class="drawer-form-content">
      <a-spin :spinning="loading">
        <a-form-model ref="entrycheckForm" :model="dataForm" :rules="rules" :labelCol="{ style: 'width: 110px' }"
                      :wrapperCol="{ style: 'width: calc(100% - 110px)' }">
          <template v-if="isEdit">
            <a-row>
              <a-col :span='24'>
                <div class="order-dispose">
                  <div class="title-box">
                    <span class="before"></span>
                    <span>基本信息</span>
                  </div>
                </div>
              </a-col>
              <template v-if="dataForm.fromType =='0'">
                <a-col :xl='8' :sm='12' :xs='24' >
                  <a-form-model-item label="仓库名称" prop="warehouseCode">
                    <a-select size="default" v-model="dataForm.warehouseCode" :disabled="warehouseCodeDisabled || type == '2'"
                              @change='warehouseCodeChange' placeholder="请选择仓库名称">
                      <a-select-option v-for="item in warehouseOptions" :key="item.warehouseCode" :value="item.warehouseCode">
                        {{item.warehouseName}}
                      </a-select-option>
                    </a-select>
                  </a-form-model-item>
                </a-col>
                <a-col :xl='8' :sm='12' :xs='24'>
                  <a-form-model-item label="资产归属" prop="belongCode">
                    <a-select v-model="dataForm.belongCode" placeholder="请选择资产归属" @change="belongCodeChange"
                              style="width: 100%;" :allowClear="false">
                      <a-select-option v-for="item in dictMap.property_belong" :key="item.dataValue" :value="item.dataValue">
                        {{item.dataLable}}
                      </a-select-option>
                    </a-select>
                  </a-form-model-item>
                </a-col>
<!--              <a-col :xl='8' :sm='12' :xs='24'>-->
<!--                <a-form-model-item label="物资来源" prop="applyTypeCode">-->
<!--                  <a-select v-model="dataForm.applyTypeCode" placeholder="请选择物资来源"-->
<!--                            style="width: 100%;" :allowClear="false">-->
<!--                    <template v-for="item in dictMap.material_from">-->
<!--                      <a-select-option :key="item.dataValue" :value="item.dataValue"-->
<!--                                       v-if="dataForm.belongCode == '1' ? item.dataValue !='3' : true">-->
<!--                        {{item.dataLable}}-->
<!--                      </a-select-option>-->
<!--                    </template>-->
<!--                  </a-select>-->
<!--                </a-form-model-item>-->
<!--              </a-col>-->
              </template>
            </a-row>
            <template v-if="['1', '2', '3'].includes(dataForm.fromType)">
              <a-row>
                <a-col :xl='8' :sm='12' :xs='24'>
                  <a-form-model-item label="仓库名称" prop="warehouseName">
                    <a-input disabled v-model="dataForm.warehouseName" size="default" />
                  </a-form-model-item>
                </a-col>
                <a-col :xl='8' :sm='12' :xs='24'>
                  <a-form-model-item label="资产归属" prop="belongName">
                    <a-input disabled v-model="dataForm.belongName" size="default" />
                  </a-form-model-item>
                </a-col>
              </a-row>
              <a-row>
                <a-col :xl='8' :sm='12' :xs='24'>
                  <a-form-model-item label="采购方式" prop="purchaseTypeStr">
                    <a-input disabled v-model="dataForm.purchaseTypeStr" size="default" />
                  </a-form-model-item>
                </a-col>
                <a-col :xl='8' :sm='12' :xs='24'>
                  <a-form-model-item :label="['1', '3'].includes(dataForm.fromType) ? '采购订单': '采购申请单'" prop="purchaseNo">
                    <a-input disabled v-model="dataForm.purchaseNo" size="default" />
                  </a-form-model-item>
                </a-col>
                <a-col :xl='8' :sm='12' :xs='24'>
                  <a-form-model-item label="需求申请单" prop="requirementCode">
                    <template v-if="dataForm.requirementCode">
                      <span  @click="openDemandapplyDetail()" class="blue operation-btn-hover">
                       <a>{{dataForm.requirementCode}}</a>
                      </span>
                    </template>
                    <template v-else>
                      {{ '--' }}
                    </template>
                  </a-form-model-item>
                </a-col>
              </a-row>
            </template>
            <a-row>
              <a-col :xl='8' :sm='12' :xs='24'>
                <a-form-model-item label="验收日期" prop="acceptanceTime">
                  <a-input disabled v-model="dataForm.acceptanceTime" size="default" />
                </a-form-model-item>
              </a-col>
              <a-col :xl='8' :sm='12' :xs='24'>
                <a-form-model-item label="验收人" prop="acceptancePersonName">
                  <a-input disabled v-model="dataForm.acceptancePersonName" size="default" />
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col :span="24">
                <a-form-model-item label="附件" prop="uploadFileList">
                  <fileUploadView v-model="dataForm.uploadFileList" :multiple="true" :maxNum="5" :maxSize="10"
                                  @set="validateField('uploadFileList')"
                                  accept=".jpg,.png,.jpeg,.bmp,.pdf,.doc,.docx,.xlsx"
                                  tip="请上传jpg、jpeg、png、bmp、pdf、word、excel格式的文件，最多5个，单个不能超过10MB">
                    <template v-slot:tootip>
                      <a-popover placement="right">
                        <template slot="content">
                          <span>请上传收货单、业主确认单、物流信息等相关附件</span>
                        </template>
                        <a-icon class="info-circle" type="info-circle" @click.stop="()=>{}"/>
                      </a-popover>
                    </template>
                  </fileUploadView>
                </a-form-model-item>
              </a-col>
              <a-col :span='16'>
                <a-form-model-item label="备注" prop="remarks">
                  <a-textarea :max-length="200" v-model="dataForm.remarks"
                              @blur="dataForm.remarks = $trim($event)" :auto-size="{ minRows: 2, maxRows: 4}" size="default" />
                </a-form-model-item>
              </a-col>
              <a-col :span='8' class="color-dark-light">
                <a-form-model-item label="验收要求" style="margin-bottom: 0;">
                  <span>1.外包装应牢固,无破损、变形，要具有良好的防潮、防压及防腐蚀等措施。</span><br>
                  <span>2.本体上应有规格型号、生产厂家、标识参数、认证标识等。</span><br>
                  <span>3.有出厂检验合格证。</span><br>
                  <span>4.符合该设备的电气性能及相应参数。</span>
                </a-form-model-item>
              </a-col>
            </a-row>
          </template>

          <template v-else>
            <detail-layout :labelList="baseList" :form="dataForm" title="基本信息">
              <template v-slot:requirementCode v-if="['1', '2', '3'].includes(dataForm.fromType)">
                <a-col :span="8" class="detail_layout_content">
                  <span class="left">需求申请单</span>
                  <span class="right">
                   <span v-if="dataForm.requirementCode" @click="openDemandapplyDetail()" class="blue operation-btn-hover">
                     <a>{{dataForm.requirementCode}}</a>
                   </span>
                   <span v-else>{{'--' }}</span>
                  </span>
                </a-col>
              </template>
            </detail-layout>
          </template>

          <template v-if="type === '4'">
            <approve ref="approve"></approve>
          </template>

          <div class='order-dispose'>
            <div class='title-box' style='padding-bottom: 10px'>
              <span class='before'></span>
              <span v-if="dataForm.fromType == '3'">退货明细</span>
              <span v-else>入库明细</span>
            </div>
          </div>

          <a-row class="right-btn" v-if="isEdit && dataForm.fromType == '0'">
            <throttle-button label="增行" :disabled="materialEnterApplyDetails.length > 99" @click="addRow" />
            <throttle-button label="删行" :disabled="checkedData.length == 0" @click="deleteRow('materialEnterApplyDetails')" style='margin-left: 10px;'/>
          </a-row>
          <!--表格渲染-->
          <vxe-table show-header-overflow :data="materialEnterApplyDetails" max-height="440" ref="formTable" resizable
                     :edit-rules="rules" align="center" border show-overflow
                     highlight-hover-row size="small" @checkbox-all="handleSelectionChange"
                     @checkbox-change="handleSelectionChange">
            <vxe-table-column v-if="isEdit && dataForm.fromType == '0'" type="checkbox" width="50" fixed="left" align="center"></vxe-table-column>
            <vxe-table-column type="seq" width="80" title="序号" align="center"></vxe-table-column>
            <vxe-table-column :show-overflow="info.name=='psaId' ? false : 'title'" v-for="info in columnList" :key="info.name"
                              :min-width="(info.width ? info.width : 100)" :title="info.comment"
                              :field="info.name" :visible="isShowCol(info.name)">
              <!-- 入库数量头部加必填项 -->
              <template v-slot:header v-if="info.comment === '入库数量'">
                <div class="required-col">
                  <div>入库数量(<span style="color: red;">*</span>)</div>
                </div>
              </template>
              <template v-slot="{ row, rowIndex}">
                <template v-if="isEdit && (['psaId', 'enterCount', 'checkCount' ].includes(info.name) ||
                 (info.name=='price' && ['0', '2'].includes(dataForm.fromType)))">
                  <template v-if="info.name=='psaId'">
                    <a-select size="default" v-model="row.psaId" placeholder="请选择" allowClear>
                      <a-select-option value="all">全部</a-select-option>
                      <a-select-option v-for="item in psaOptions" :key="item.pasId + ''" :value="item.pasId + ''">
                        {{item.pasName}}
                      </a-select-option>
                    </a-select>
                  </template>
                  <template v-else-if="info.name=='enterCount'">
                    <a-input-number :controls=false :precision="0" :min="1" :max="99999999"
                                v-model="row[info.name]" placeholder="请输入入库数量"></a-input-number>
                  </template>
                  <template v-else-if="info.name=='checkCount'">
                    <a-input-number :controls=false :precision="0" :min="0" :max="row.remainCount"
                                    @blur.stop="getSum()"
                                    v-model="row[info.name]" placeholder="请输入本次验收数量"></a-input-number>
                  </template>
                  <template v-else-if="info.name=='price' && ['0', '2'].includes(dataForm.fromType)">
                    <a-input-number :controls=false :precision="2" :min="0" :max="99999999.99"
                                    v-model="row[info.name]" placeholder="请输入单价"></a-input-number>
                  </template>
                </template>
                <template v-else>
                  <template v-if="info.name=='returnCount'">
                    <span style="color: red;">{{getLabel(row[info.name], null)}}</span>
                  </template>
                  <template v-else>
                    <span>{{getLabel(row[info.name], null)}}</span>
                  </template>
                </template>
              </template>
            </vxe-table-column>
            <template v-slot:empty>
              <span>查询无数据</span>
            </template>
          </vxe-table>
          <div class='amt-content' v-if="['1', '2'].includes(this.dataForm.fromType)">
            <div class='amt-content-right'>
              <div class='amt-content-item'>
                <span class='item-label'>本次验收总价(元):</span>
                <span class='item-value'>{{ getMoneyLabel(checkCountTotalPrice) }}</span>
              </div>
            </div>
          </div>
        </a-form-model>
      </a-spin>
    </div>

    <div @click="openFlowChart" class="flow-chart-btn"> <svg-icon iconClass="flow"></svg-icon>流程图</div>
    <flow-chart-drawer ref="flowChartDrawer" :parentId="parentId" :processInstanceId="dataForm.processInstanceId"
                       :flowUser="dataForm.flowUser" processDefinitionKey="资产管理_新物资入库" />

    <div class="drawer-form-foot">
      <template v-if="isEdit">
        <a-button type="info" :disabled="loading" @click="cancel">取消</a-button>
        <a-button :loading="loading" class="ant-btn-primary" @click="doSubmit('1')">提交</a-button>
      </template>
      <template v-else-if="type == '4'">
        <a-button type="info" :disabled="loading" @click="cancel">取消</a-button>
        <a-button :loading="loading" class="ant-btn-primary" @click="doAudit()">提交</a-button>
      </template>
      <template v-else>
        <a-button type="info" @click="cancel">返回</a-button>
        <a-button v-if="dataForm.fromType == '0' && dataForm.createUser == user.username && ['act1', 'act2'].includes(dataForm.taskDefKey)" :loading="loading" class="ant-btn-primary" @click="processCancel('materialEnter')">作废</a-button>
      </template>
    </div>
    <!-- 选择数据 -->
    <choose-asset-model v-model="showChooseAssetForm" :visible="showChooseAssetForm" :rowData="rowData" @cancel="val => doChooseAsset(val , 'materialEnterApplyDetails')"></choose-asset-model>
    <!-- 表单 -->
    <drawer-view ref="demandapplyForm" parentId="drawerViewDetail" />
  </div>
</template>

<script>
import { createEnter, queryEntryDetail, auditEntry } from '@/api/materialsManage/entrycheck';
import initDict from '@/mixins/initDict';
import moment from 'moment';
import { getWidth } from '@/utils/util';
import { USER_INFO } from '@/store/mutation-types';
import ChooseAssetModel from '@views/materialsManage/modules/chooseAssetModel';
import Approve from '@views/materialsManage/modules/approve';
import fileUploadView from '@/components/com/fileUploadView';
import mixin from '../js/mixin';
import { listByPermission } from '@api/isolarErp/asset/warehouseManage';
import {
  entrycheckDetailBaseList,
  entrycheckDetailColumnList,
  entrycheckRules
} from '@views/materialsManage/js/column';
export default {
  props: {
    /* 抽屉挂在的DOM节点ID，必要参数 */
    parentId: {
      type: String,
      required: true,
      default: ''
    }
  },
  components: {
    ChooseAssetModel,
    Approve,
    fileUploadView
  },
  mixins: [initDict, mixin],
  data () {
    return {
      loading: false,
      expLoading: false,
      isEdit: false,
      warehouseCodeDisabled: false,
      isPsaWarehouse: false,
      type: '',
      dataForm: {
        fromType: '0',
        warehouseCode: undefined,
        warehouseName: undefined,
        belongCode: undefined,
        belongName: undefined,
        applyTypeCode: '2',
        purchaseTypeStr: undefined,
        purchaseNo: undefined,
        requirementCode: undefined,
        acceptanceTime: undefined,
        acceptancePerson: undefined,
        acceptancePersonName: undefined,
        uploadFileList: [],
        remarks: undefined,
        checkCountTotalPrice: undefined
      },
      materialEnterApplyDetails: [],
      psaOptions: [],
      baseList: entrycheckDetailBaseList,
      columnList: entrycheckDetailColumnList,
      rules: entrycheckRules
    };
  },
  computed: {
    checkCountTotalPrice () {
      return this.dataForm.checkCountTotalPrice;
    }
  },
  created () {
    translate.listener.start();
    translate.execute();
    this.listByPermissions({ 'byDataRole': true });
    this.columnList.forEach(item => {
      if (!item.width) {
        item.width = getWidth(item.comment);
      }
    });
    this.getDictMap('property_belong,material_from');
  },
  methods: {
    moment,
    // 切换抽屉时动画结束后的回调，页面初始化init函数的异步请求须放在这里调用，否则可能会会导致抽屉侧滑出来时卡死
    afterEvent () {
      let self = this;
      if (self.type != '1') {
        // 查询信息
        let params = {
          id: self.row.id
        };
        queryEntryDetail(params).then(res => {
          if (res.result_data) {
            let form = res.result_data;
            let user = Vue.ls.get(USER_INFO);
            if (self.isEdit && !form.acceptancePerson) {
              form.acceptancePerson = user.username;
              form.acceptancePersonName = user.realname;
            }
            form.fromType = form.fromType + '';
            Object.assign(self.dataForm, form);
            self.materialEnterApplyDetails = res.result_data.materialEnterApplyDetails;
            self.materialEnterApplyDetails.forEach(item => {
              item.key = Math.random();
            });
            self.rules.price[0].required = form.fromType == '2';
            self.getWarehouseTypeByWarehouseCode(self.dataForm.warehouseCode);
          }
          self.getSum();
          self.refreshColumn();
          self.loading = false;
        }).catch(() => {
          self.loading = false;
        });
      } else {
        let user = Vue.ls.get(USER_INFO);
        self.dataForm.acceptancePerson = user.username;
        self.dataForm.acceptancePersonName = user.realname;
        self.dataForm.acceptanceTime = moment().format('YYYY-MM-DD');
        self.loading = false;
        self.refreshColumn();
      }
    },
    // type 新增：1  编辑：2  详情: 3  审批: 4
    init (type, row) {
      let self = this;
      self.isEdit = ['1', '2'].includes(type);
      self.loading = true;
      self.type = type;
      self.row = row;
      if (row) {
        Object.assign(self.dataForm, row);
      }
      self.afterEvent();
      switch (type) {
        case '1':
          return '入库登记';
        case '2':
          return '编辑入库';
        case '3':
          return '入库详情';
        case '4':
          return '入库审批';
      }
    },
    // 打开流程图
    openFlowChart () {
      this.$refs.flowChartDrawer.openView();
    },
    // 表-列刷新
    refreshColumn () {
      this.$nextTick(() => {
        let $table = this.$refs.formTable;
        $table && $table.refreshColumn();
      });
    },
    // 查询仓库列表
    listByPermissions (val) {
      let params = {
        'keyword': val.keyword,
        'byDataRole': val.byDataRole
      };
      listByPermission(params).then(res => {
        this.warehouseOptions = res.result_data;
      }).catch(() => {
        this.warehouseOptions = [];
      });
    },
    // 仓库change
    warehouseCodeChange (val) {
      this.materialEnterApplyDetails = [];
      this.getWarehouseTypeByWarehouseCode(val);
    },
    // 资产归属change
    belongCodeChange () {
      // this.dataForm.applyTypeCode = undefined;
    },
    // 增行
    addRow () {
      if (!this.dataForm.warehouseCode) {
        this.$message.warning('请先选择仓库');
        return;
      }
      this.openChoose('materialEnterApplyDetails', 'enter');
    },
    // 保存
    doSubmit () {
      this.loading = true;
      let { materialEnterApplyDetails } = this;
      if (!materialEnterApplyDetails.length) {
        this.$message.warning('请添加入库明细');
        this.loading = false;
        return;
      }

      this.$refs['entrycheckForm'].validate(valid => {
        if (valid) {
          this.update();
        } else {
          this.loading = false;
          return false;
        }
      });
    },
    async update () {
      const self = this;
      const errMap = await self.$refs['formTable'].validate(true).catch(errMap => errMap);
      if (errMap != undefined) {
        self.loading = false;
      } else {
        let map = {};
        let { materialEnterApplyDetails } = this;
        if (['1', '2'].includes(self.dataForm.fromType)) {
          const check = self.checkData(materialEnterApplyDetails);
          if (check) {
            self.loading = false;
            return;
          }
        }
        Object.assign(map, self.dataForm);
        map.materialEnterApplyDetails = materialEnterApplyDetails;
        self.savePromise(map);
      }
    },
    // 数据校验
    checkData (items) {
      try {
        let checkFlg = true;
        items.forEach((item, index) => {
          if (item.checkCount) {
            checkFlg = false;
          }
        });
        if (checkFlg) {
          throw new Error('提交失败，验收数量不能都为0');
        }
        return false;
      } catch (e) {
        this.$message.warning(e.message);
        return true;
      }
    },
    // 提交
    savePromise (req) {
      createEnter(req).then(res => {
        this.loading = false;
        this.$message.success('操作成功');
        this.cancel();
      }).catch(() => {
        this.loading = false;
      });
    },

    // 审核
    doAudit () {
      this.loading = true;
      this.$refs.approve.saveApprove().then(params => {
        // 审核参数
        let auditParam = {
          ...this.dataForm,
          ...params
        };
        auditEntry(auditParam).then(res => {
          this.loading = false;
          if (res.result_code == '1') {
            this.$message.success('操作成功');
            this.cancel();
          }
        }).catch(() => {
          this.loading = false;
        });
      }).catch(() => {
        this.loading = false;
      });
    },
    validateField (field) {
      this.$refs.entrycheckForm.validateField(field);
    },
    // 设置字段是否显示
    isShowCol (columnName) {
      if (this.dataForm.fromType == '0' && (
        ['purchaseCount', 'checkedCount', 'remainCount', 'returnCount', 'checkCount'].includes(columnName) ||
        (['psaId', 'psaName'].includes(columnName) && !this.isPsaWarehouse) ||
        (columnName == 'psaName' && this.isEdit) ||
        (columnName == 'psaId' && !this.isEdit)
      )) {
        return false;
      }
      if (['1', '2'].includes(this.dataForm.fromType) && (
        ['returnCount', 'enterCount', 'psaId'].includes(columnName)
      )) {
        return false;
      }
      if (this.dataForm.fromType == '3' && (
        ['purchaseCount', 'checkedCount', 'remainCount', 'enterCount', 'checkCount', 'psaId'].includes(columnName)
      )) {
        return false;
      }
      return true;
    },
    // 关联需求申请单
    openDemandapplyDetail () {
      let row = {
        id: this.dataForm.requirementId
      };
      this.$refs.demandapplyForm.init('3', row, '/materialsManage/demandapply/form');
    },
    // 求和
    getSum () {
      let materialEnterApplyDetails = this.materialEnterApplyDetails;
      let sums = null;
      let values;
      if (['1', '2'].includes(this.dataForm.fromType)) {
        values = materialEnterApplyDetails.map(item => Number(item.checkCount) * Number(item.price));
      }
      if (!values.every(value => isNaN(value))) {
        sums = values.reduce((prev, curr) => {
          const value = Number(curr);
          if (!isNaN(value)) {
            return prev + curr;
          } else {
            return prev;
          }
        }, 0);
      }
      this.dataForm.checkCountTotalPrice = sums.toFixed(2);
      this.refreshColumn();
    },
    // 金额展示
    getMoneyLabel (data) {
      if (data) {
        return this.toThousands(data.toString());
      } else {
        return '--';
      }
    },
    // 千分位转换
    toThousands (num) {
      let arr = num.split('.');
      let a0 = (arr[0] || 0).toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,');
      return arr.length > 1 ? a0 + '.' + arr[1] : a0;
    },
    // 弹窗关闭回调方法
    cancel (type) {
      this.$emit('cancel', type);
      this.reset();
    },
    reset () {
      let dictMap = Object.assign({}, this.dictMap);
      Object.assign(this.$data, this.$options.data());
      this.$refs['entrycheckForm'].resetFields();
      this.$refs['entrycheckForm'].clearValidate();
      this.dictMap = dictMap;
    }
  }
};
</script>

<style lang="less" scoped>
:deep(.drawer-form-com) {
  padding: 0 !important;
}

:deep(.ant-form-item){
  width: 100%;
  display: inline-flex;
}

.amt-content {
  width: 100%;
  display: flex;
  justify-content: flex-end;

  .amt-content-right {
    width: 260px;
    margin: 9px 23px;
  }

  .amt-content-item {
    display: flex;
    justify-content: space-between;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    font-weight: 550;
    line-height: 22px;

    .item-label {
      color: #222222;
    }

    .item-value {
      color: #222222;
    }
  }
}

.color-dark-light {
  :deep(.ant-form-item-label) {
    line-height: 20px;
  }

  :deep(.ant-form-item-control) {
    line-height: 20px;
  }
}
.right-btn {
  width: 100%;
  text-align: right;
  padding-bottom: 10px;
}
.info-circle{
  margin-left: 12px;
  cursor: pointer;
}
</style>
