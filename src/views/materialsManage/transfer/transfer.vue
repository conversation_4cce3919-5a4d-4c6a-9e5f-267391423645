<!-- 物资调拨 -->
<template>
  <div id="materialsTransfer">
    <a-spin :spinning="pageLoading">
      <div class="solar-eye-search-model">
        <a-row :gutter="24" class="solar-eye-search-content">
          <a-col :span="24">
            <div class="search-item">
              <span class="search-label">流程状态</span>
              <a-checkbox @change="checkedAllClick" v-model="checkedAll">全部({{countAll}})</a-checkbox>
              <a-checkbox-group v-model="allStatusList" @change="statusClick">
                <a-checkbox value="1">处理中({{taskStatus0}})</a-checkbox>
                <a-checkbox value="2">已完成({{taskStatus1}})</a-checkbox>
                <a-checkbox value="3">已退回({{taskStatus2}})</a-checkbox>
                <a-checkbox value="4">已作废({{taskStatus3}})</a-checkbox>
              </a-checkbox-group>
            </div>
          </a-col>
          <a-col :xxl="6" :xl="8" :md="12">
            <div class="search-item">
              <span class="search-label">仓库名称</span>
              <a-select size="default" v-model="searchData.warehouseCodeList" placeholder="请选择仓库名称" @change="pageChange()"
                        mode='multiple' :maxTagCount="1" allowClear option-filter-prop="children" :filter-option="filterOption">
                <a-select-option v-for="item in warehouseOptions" :key="item.warehouseCode" :value="item.warehouseCode">
                  {{item.warehouseName}}
                </a-select-option>
              </a-select>
            </div>
          </a-col>
          <a-col :xxl="5" :xl="8" :md="12">
            <div class="search-item">
              <span class="search-label">调拨单编号</span>
              <a-input v-model="searchData.transferCode" @blur="searchData.transferCode = $trim($event)" size="default"
                       @pressEnter='pageChange' placeholder="请输入调拨单编号" allowClear></a-input>
            </div>
          </a-col>
          <a-col :xxl="5" :xl="8" :md="12">
            <div class="search-item">
              <span class="search-label">登记日期</span>
              <a-range-picker v-model="searchData.timeRange" format="YYYY-MM-DD" @change="changeTimeRange"
                              :placeholder="['开始日期', '结束日期']" allowClear></a-range-picker>
            </div>
          </a-col>
          <a-col :xxl="5" :xl="8" :md="12">
            <div class="search-item">
              <span class="search-label">经办人</span>
              <a-input v-model="searchData.keyword" @blur="searchData.keyword = $trim($event)" size="default"
                       @pressEnter='pageChange' placeholder="请输入经办人" allowClear></a-input>
            </div>
          </a-col>
          <a-col :xxl="3" :xl="8" :md="12">
            <div class="search-item">
              <throttle-button label="查询" size="default" class="solar-eye-btn-primary" @click="pageChange()"/>
              <a-button @click="resetParams">重置</a-button>
            </div>
          </a-col>
        </a-row>
      </div>

      <div class="solar-eye-gap"></div>
      <div class="solar-eye-main-content">
        <div class="operation-btn">
          <!-- 筛选列下拉框 -->
          <a-dropdown trigger="click">
            <a class="ant-dropdown-link" @click="e => e.preventDefault()">筛选列
              <a-icon type="form" />
            </a>
            <a-menu slot="overlay">
              <a-checkbox-group v-model="checkColumn" @change="refreshColumn">
                <a-checkbox class="drop-down" v-for="(column,index) in columnList" :key="index" :value="column.name">
                  {{column.comment}}
                </a-checkbox>
              </a-checkbox-group>
            </a-menu>
          </a-dropdown>
          <throttle-button label="调拨登记" perms='transfer:add' size="default" class="solar-eye-btn-primary" @click="showDetail('1', {})"></throttle-button>
        </div>
        <vxe-table :data="data" :height="tableHeight - 24" ref="multipleTable" :seq-config="{startIndex: (page - 1) * size}"
                   resizable align="center" border show-overflow highlight-hover-row size="small">
          <vxe-table-column type="seq" width="80" title="序号"></vxe-table-column>
          <vxe-table-column show-overflow="title" v-for="item in columnList" :key="item.name" :field="item.name"
                            :visible="checkColumn.includes(item.name)" :title="item.comment" :min-width="item.width || 140">
            <template v-slot:default="{row}">
              <div v-if="item.name == 'flowStsLabel' && row.flowStsLabel" class="status-info">
                <div :class="`statusCol color-${getColor(row.flowStsLabel)}`"></div>
                <div>{{ row.flowStsLabel }}</div>
              </div>
              <span v-else>{{ getLabel(row[item.name], null) }}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column title="操作" fixed="right" width="160" :resizable="false" class-name="fixed-right-column-160">
            <template v-slot="{ row }">
              <throttle-button title="详情" icon="file-text" @click="showDetail('3', row)" class="operation-btn-hover"/>
              <throttle-button title="编辑" v-if="row.hasPermission && row.taskDefKey == 'act1'" icon="edit" @click="showDetail('2', row)" class="operation-btn-hover"/>
              <throttle-button title="审批" v-if="row.hasPermission && ['act2', 'act3', 'act4'].includes(row.taskDefKey)" icon="form" @click="showDetail('4', row)" class="operation-btn-hover"/>
            </template>
          </vxe-table-column>
          <template v-slot:empty>
            <span>查询无数据</span>
          </template>
        </vxe-table>
        <!--分页组件-->
        <page-pagination :pageSize="size" :current="page" :total="total" @size-change="sizeChange" />
      </div>
    </a-spin>
    <!-- 表单 -->
    <drawer-view ref="transferForm" @cancel="queryData" parentId="drawerViewDetail" />
  </div>
</template>

<script>
import { getTransferList, countData } from '@/api/materialsManage/transfer';
import moment from 'moment';
import { getWidth } from '@/utils/util';
import { LeftMixin } from '@/mixins/LeftMixin';
import initDict from '@/mixins/initDict';
import tableHeight from '@/mixins/tableHeightAndSearchModel';
import mixin from '../js/mixin';
import { transferCheck, transferColumn } from '@views/materialsManage/js/column';
export default {
  mixins: [initDict, LeftMixin, tableHeight, mixin],
  name: 'transfer',
  components: {},
  data () {
    return {
      pageLoading: false,
      // 查询参数
      searchData: {
        warehouseCodeList: undefined,
        transferCode: undefined,
        timeRange: [],
        startDate: null,
        endDate: null,
        keyword: undefined
      },
      columnList: transferColumn,
      checkColumn: transferCheck,
      // 表格相关参数
      data: [],
      // 分页参数
      total: 0,
      page: 1,
      size: 10
    };
  },
  created () {
    this.tabKind = '1';
    this.allStatusList = this.defAll = ['1', '2', '3', '4'];
    this.columnList.forEach(item => {
      if (!item.width) {
        item.width = getWidth(item.comment);
      }
    });
    this.queryData();
    this.queryWarehouseList({ 'byDataRole': false, 'isMainPage': true, 'defaultSelected': true });
  },
  methods: {
    moment,
    // 表-列刷新
    refreshColumn () {
      this.$nextTick(() => {
        let $table = this.$refs.multipleTable;
        $table && $table.refreshColumn();
      });
    },
    // 查询
    async queryData () {
      let self = this;
      self.pageLoading = true;
      let $table = this.$refs.multipleTable;
      $table && await $table.clearScroll();
      let requestMap = {
        curPage: self.page,
        size: self.size,
        flowSts: self.allStatusList
      };
      Object.assign(requestMap, self.searchData);
      getTransferList(requestMap).then(res => {
        self.data = res.result_data.rows;
        self.total = res.result_data.total;
        self.pageLoading = false;
      }).catch(() => {
        self.data = [];
        self.total = 0;
        self.pageLoading = false;
      });
      self.countStsData(requestMap);
    },
    // 统计各状态数据量
    countStsData (param) {
      let map = Object.assign({}, param);
      map.flowSts = ['1', '2', '3', '4'];
      countData(map).then(res => {
        this.assingnData(res.result_data);
      }).catch(() => {
        this.assingnData();
      });
    },
    // 表单
    showDetail (type, row) {
      this.$refs.transferForm.init(type, row, '/materialsManage/transfer/form');
    },
    // 重置
    resetParams () {
      Object.assign(this.searchData, this.$options.data().searchData);
      this.queryData();
    },
    // 查询点击事件
    pageChange () {
      this.page = 1;
      this.queryData();
    },
    // 分页事件
    sizeChange (p, e) {
      this.page = p;
      this.size = e;
      this.queryData();
    },
    // 日期change
    changeTimeRange (moment, data) {
      this.searchData.startDate = data[0];
      this.searchData.endDate = data[1];
      this.pageChange();
    }
  }
};
</script>
