<!-- 物料清单 -->
<template>
  <div id="personalRecord" class="inventory-c">
    <a-row :gutter="24" style="display: flex">
      <a-col style="width: 350px !important">
        <materialsInventoryTree
          v-model="searchData.materialTypeCode"
          belongTo="all"
          faHeight="60"
          @listenChange="pageChange(1)"
        />
      </a-col>
      <a-col style="flex: 1">
        <a-col class="solar-eye-main-content">
          <!-- 操作按钮 -->
          <div class="operation" style="height: 35px">
            <div class="operation-btn">
              <div class="left-operation">
                <a-input-search
                  v-model="searchData.keyword"
                  placeholder="请输入关键词搜索"
                  allow-clear
                  style="width: 100%"
                  @search="pageChange(1)"
                />
              </div>
              <div class="right-operation">
                <a-dropdown>
                  <a class="ant-dropdown-link" @click="(e) => e.preventDefault()"
                    >筛选列
                    <a-icon type="form" />
                  </a>
                  <a-menu slot="overlay">
                    <a-checkbox-group v-model="checkColumn" @change="refreshColumn">
                      <a-checkbox
                        class="drop-down"
                        v-for="column in columnList"
                        :key="column.field"
                        :value="column.field"
                      >
                        {{ column.title }}</a-checkbox
                      >
                    </a-checkbox-group>
                  </a-menu>
                </a-dropdown>
                <throttle-button
                  label="新增物料"
                  perms="material:add"
                  size="default"
                  class="solar-eye-btn-primary"
                  @click="addClick"
                ></throttle-button>
                <a-button
                  v-has="'inventory:export'"
                  :loading="exp_loading"
                  :disabled="exp_loading || total < 1"
                  @click="exportExcelEvent('exp_loading', '100020', paramsCopy)"
                >
                  导出</a-button
                >
              </div>
            </div>
          </div>
          <a-spin :spinning="pageLoading">
            <vxe-table
              ref="vxeTable"
              :data="data"
              :height="tableHeight - 24"
              resizable
              align="center"
              border
              show-overflow
              highlight-hover-row
              size="small"
              :seq-config="{ startIndex: (page - 1) * size }"
            >
              <vxe-table-column
                type="seq"
                width="80"
                align="center"
                title="序号"
              ></vxe-table-column>
              <vxe-table-column
                v-for="item in columnList"
                :key="item.field"
                show-overflow="title"
                :formatter="tabFormatter"
                :field="item.field"
                :title="item.title"
                min-width="150"
                :visible="checkColumn.includes(item.field)"
              >
            </vxe-table-column>
            <vxe-table-column show-overflow="title" width="120" :formatter="tabFormatter" field="deviceStsLabel" title="设备状态" min-width="200">
              <template v-slot="{ row }">
                <div class="device-info">
                  <span class="device-status" :class="row.status === '0' ? 'forbidden' : ''"></span>
                  <span>{{ row.status === '1' ? '正常' : (row.status === '0' ? '禁用' : '') }}</span>
                </div>
              </template>
            </vxe-table-column>
              <vxe-table-column title="操作" :visible="showHandle(perms)" fixed="right" width="120"
                :resizable="false" class-name="fixed-right-column-120">
                <!-- 编辑 删除标识符重新设置 -->
                <template v-slot="{row}">
                  <erp-button title="编辑" perms="material:edit" icon="edit"
                    @click="detailClick(row)"></erp-button>
                  <erp-button title="删除" perms="material:delete" icon="delete"
                    @click="deleteClick(row.id)"></erp-button>
                </template>
              </vxe-table-column>
              <template v-slot:empty>
                <span>查询无数据</span>
              </template>
            </vxe-table>
            <page-pagination
              :pageSize="size"
              :current="page"
              :total="total"
              @size-change="sizeChange"
            />
          </a-spin>
        </a-col>
      </a-col>
    </a-row>
    <!-- 新增、编辑物料 -->
    <a-modal
      v-model="detailOpen"
      :maskClosable="false"
      centered
      @cancel="cancel"
      :title="detailTitle"
      width="70%"
    >
      <a-spin :spinning="saveLoad">
        <a-form-model
          :model="materialInfo"
          :rules="rules"
          ref="material"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 18 }"
        >
          <a-row :gutter="24">
            <a-col :sm="24" :md="11">
              <a-form-model-item label="物料名称" prop="materialName">
                <a-input
                  size="default"
                  v-model.trim="materialInfo.materialName"
                  :max-length="15"
                  style="width: 100%"
                ></a-input>
              </a-form-model-item>
            </a-col>
            <a-col :sm="24" :md="11">
              <div style="width: 100%; height: 1px"></div>
            </a-col>
            <a-col :sm="24" :md="11">
              <a-form-model-item label="所属分类:" prop="pid">
                <a-cascader
                  :show-search="{ filter }"
                  :allowClear="false"
                  v-model="materialInfo.pid"
                  :options="materialParent"
                  size="default"
                  :field-names="{
                    label: 'materialTypeName',
                    value: 'materialTypeCode',
                    children: 'children',
                  }"
                  placeholder="请选择所属分类"
                  style="width: 100%"
                ></a-cascader>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :sm="24" :md="11">
              <a-form-model-item label="规格型号" prop="specs">
                <a-input
                  size="default"
                  v-model.trim="materialInfo.specs"
                  :max-length="15"
                  style="width: 100%"
                ></a-input>
              </a-form-model-item>
            </a-col>
            <a-col :sm="24" :md="11">
              <a-form-model-item label="单位" prop="unitCode">
                <a-select size="default" v-model="materialInfo.unitCode" allowClear placeholder="请选择单位" style="width: 100%;height: 32px;">
                  <a-select-option v-for="(item,index) in dictMap.catalog_unit" :key="index" :value="item.codeValue">{{item.dispName}}</a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :sm="24" :md="11">
              <a-form-model-item label="状态" prop="status">
                <!-- 禁用 '0'  正常  '1' -->
                <a-radio-group v-model="materialInfo.status" name="radioGroup">
                  <a-radio :value="'1'">正常</a-radio>
                  <a-radio :value="'0'">禁用</a-radio>
                </a-radio-group>
              </a-form-model-item>
            </a-col>
          </a-row>
          </a-row>
        </a-form-model>
      </a-spin>
      <template slot="footer">
        <div class="modal-footer">
          <a-button size="default" :disabled="saveLoad" @click="resetForm(true)">取消</a-button>
          <a-button size="default" :loading="saveLoad" type="primary" @click="submitForm()"
            >确定</a-button
          >
        </div>
      </template>
    </a-modal>
    <drawer-view ref="inventoryForm" @cancel="pageChange(1)" parentId="drawerViewDetail" />
  </div>
</template>

<script>
import { getInventoryList, getInventoryTreeList, insertMatCatalog, updateMatCatalog, logicDelCatalog } from '@/api/materialsManage/inventory';
import initDict from '@/mixins/initDict';
import tableHeight from '@/mixins/tableHeightAndSearchModel';
import downBigExcel from '@/mixins/downBigExcel';
export default {
  name: 'inventory',
  mixins: [initDict, tableHeight, downBigExcel],
  components: {},
  data () {
    return {
      perms: 'material:edit,material:delete',
      detailOpen: false, // 新增、编辑物料弹窗
      materialParent: [], // 所属上级
      addOrEdit: '',
      saveLoad: false,
      materialInfo: {
        materialName: '',
        materialTypeCode: '',
        specs: '',
        pid: [],
        unitCode: '',
        status: '1'
      },
      pageLoading: false,
      exp_loading: false,
      doubleRight: false,
      searchData: {
        materialTypeCode: undefined,
        keyword: undefined
      },
      rules: {
        materialName: [
          {
            required: true,
            message: '请输入物料名称',
            trigger: 'blur'
          }
        ],
        specs: [
          {
            required: true,
            message: '请输入规格型号',
            trigger: 'blur'
          }
        ],
        status: [
          {
            required: true,
            message: '请选择状态',
            trigger: 'change'
          }
        ],
        unitCode: [
          {
            required: true,
            message: '请选择单位',
            trigger: 'change'
          }
        ],
        pid: [
          {
            required: true,
            message: '请选择所属分类',
            trigger: 'change'
          }
        ]
      },
      paramsCopy: {},
      columnList: [
        { field: 'materialCode', title: '编码' },
        { field: 'materialTypeName', title: '分类' },
        { field: 'materialName', title: '名称' },
        { field: 'specs', title: '规格型号' },
        { field: 'unit', title: '单位' }
      ],
      checkColumn: ['materialCode', 'materialTypeName', 'materialName', 'specs', 'unit'], // 筛选列
      // 表格相关参数
      data: [],
      // 分页参数
      total: 0,
      page: 1,
      size: 10
    };
  },
  created () {
    this.queryData();
    // 获取字典
    this.getDictMap('catalog_unit');
  },
  methods: {
    // 查询设备类型详情
    detailClick (item) {
      this.materialInfo = {
        id: item.id,
        materialName: item.materialName,
        materialTypeCode: '',
        allLevelMaterialTypeCode: item.allLevelMaterialTypeCode,
        specs: item.specs,
        pid: [],
        unitCode: item.unitCode,
        status: item.status
      };
      this.initParentMaterialInfoList('edit');
    },
    // 删除
    deleteClick (id) {
      let self = this;
      this.$confirm({
        title: '是否确认删除物料?',
        okText: '确定',
        cancelText: '取消',
        onOk () {
          self.saveLoad = true;
          logicDelCatalog({ id }).then((res) => {
            self.saveLoad = false;
            if (res.result_code == '1') {
              self.$message.success('删除成功');
              self.queryData();
            } else {
              self.saveLoad = false;
              self.$message.warning(res.result_msg);
            }
          }).catch(() => {
            self.saveLoad = false;
          });
        }
      });
    },
    // 保存验证
    submitForm () {
      this.saveLoad = true;
      this.$refs['material'].validate((valid) => {
        if (valid) {
          this.save();
        } else {
          this.saveLoad = false;
          return false;
        }
      });
    },
    // 保存数据
    save () {
      let portName = this.addOrEdit === 'add' ? insertMatCatalog : updateMatCatalog;
      // 新增
      this.materialInfo.materialTypeCode = this.materialInfo.pid[this.materialInfo.pid.length - 1];
      let params = Object.assign({}, this.materialInfo);
      delete params.pid;
      portName(params)
        .then((res) => {
          this.saveLoad = false;
          if (res.result_code == '1') {
            this.resetForm(false);
            this.queryData();
            this.$message.success('操作成功');
          } else {
            this.$message.warning(res.result_msg);
          }
        })
        .catch(() => {
          this.saveLoad = false;
        });
    },
    // 获取所属上级
    initParentMaterialInfoList (type) {
      getInventoryTreeList({ belongTo: this.belongTo, status: '1', typeKeyWords: '' }).then((res) => {
        if (res.result_code == '1') {
          this.materialParent = res.result_data;
          if (type === 'edit') {
            let data = this.materialInfo.allLevelMaterialTypeCode.split('%#&');
            this.$set(this.materialInfo, 'pid', data);
            this.addOrEdit = 'edit';
            this.detailTitle = '编辑物料';
            this.detailOpen = true;
          }
        }
      });
    },
    // 新增
    addClick () {
      this.resetForm(true);
      this.addOrEdit = 'add';
      this.initParentMaterialInfoList('add');
      this.detailTitle = '新增物料';
      this.detailOpen = true;
    },
    // 取消
    resetForm (flag) {
      if (this.$refs['material']) {
        this.$refs['material'].resetFields();
      }
      this.addOrEdit = '';
      this.materialInfo = {
        id: '',
        materialTypeName: '',
        parentCode: '',
        pid: ['0'],
        status: '1'
      };
      this.pageLoading = false;
      this.detailOpen = false;
      if (!flag) {
        this.queryData();
      }
    },
    // 表格列刷新
    refreshColumn () {
      this.$nextTick(() => {
        let $table = this.$refs.vxeTable;
        $table && $table.refreshColumn();
      });
    },
    // 查询表格列表数据
    async queryData () {
      let self = this;
      self.pageLoading = true;
      let $table = this.$refs.vxeTable;
      if ($table) {
        await $table.clearScroll();
      }
      let requestMap = {
        curPage: self.page,
        size: self.size,
        materialTypeCode: self.searchData.materialTypeCode === '0' || !self.searchData.materialTypeCode ? '' : self.searchData.materialTypeCode,
        keyword: self.searchData.keyword,
        belongTo: 'all'
      };
      self.paramsCopy = requestMap;
      getInventoryList(requestMap)
        .then((res) => {
          self.pageLoading = false;
          let data = res.result_code == '1' ? res.result_data.rows : [];
          self.data = data;
          self.total = res.result_code == '1' ? res.result_data.total : 0;
        })
        .catch(() => {
          self.data = [];
          self.total = 0;
          self.pageLoading = false;
        });
    },
    // 打开弹窗
    openForm (type, row) {},
    // 分页事件
    pageChange (e) {
      this.page = e;
      this.queryData();
    },
    // 分页事件
    sizeChange (current, pageSize) {
      this.page = current;
      this.size = pageSize;
      this.queryData();
    }
  }
};
</script>

<style lang="less" scoped>
.inventory-c {
  background: #f0f0f0;
  height: 100%;
}
.solar-eye-main-content .operation-btn {
  justify-content: space-between;
  height: 100%;
  margin-bottom: 0;
  .left-operation {
    display: flex;
    width: 300px;
  }
}
.solar-eye-dark {
  .inventory-c {
    background: #17202f !important;
  }
}
.device-info {
  display: flex;
  align-items: center;
  justify-content: center;
  .device-status {
    display: flex;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #2BA471;
    margin-right: 6px;
    &.forbidden {
      background: #C5C5C5;
    }
  }
}
</style>
