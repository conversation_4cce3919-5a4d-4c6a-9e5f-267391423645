<template>
  <div class="tab-content">
    <template>
      <inventory ref="inventory"/>
    </template>
  </div>
</template>

<script>
import inventory from './inventory.vue';
export default {
  name: 'index',
  components: {
    inventory
  },
  data () {
    return {
      tabkey: '1'
    };
  },
  methods: {
  }
};
</script>

<style lang="less" scoped>
  :deep(.protal-tabs) {
    padding: 0;
    .ant-tabs-bar{
      margin: 0;
      padding: 0 23px;
    }
    .ant-tabs-tab{
      padding: 8px 16px;
    }
  }
</style>
