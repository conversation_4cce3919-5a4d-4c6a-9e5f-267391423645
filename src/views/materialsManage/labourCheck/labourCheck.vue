<!--劳务验收-->

<template>
  <div class="drawer-form-com">
    <div class='drawer-form-content'>
      <a-spin :spinning='loading' style='width: 100%; min-height: 60px'>
          <a-alert :message="`服务工单未完成，暂不可验收`" banner class='info-alert' v-if='submitDisabled' />
          <detail-layout :labelList="labelList" :form="formData" title="基本信息">
            <template v-slot:requireCode>
              <a-col :span="8" class="detail_layout_content">
                <span class="left">需求申请单</span>
                <span class="right">
                  <a v-if="formData.requireCode" class="menu-skiplink" @click="jumpToOuter('1')">{{ formData.requireCode }}</a>
                  <span v-else>--</span>
                </span>
              </a-col>
            </template>
             <template v-slot:relatedTaskNo v-if="formData.haveTask == '1'">
              <a-col :span="8" class="detail_layout_content">
                <span class="left">工单编号</span>
                <span class="right">
                  <a v-if="formData.orderNo" class="menu-skiplink" @click="jumpToOuter('2')">{{ formData.orderNo }}</a>
                  <span v-else>--</span>
                </span>
              </a-col>
            </template>
            <template v-slot:remarks>
                <div class='detail_layout_content ant-col ant-col-24' :style='{padding:"0 8px"}'>
                  <span class='left'>补充说明</span>
                  <a-textarea
                    class='right'
                    v-if='!isDisabled'
                    v-model="formData.remarks"
                    :maxLength="200"
                    @blur="formData.remarks = $trim($event)"
                    placeholder="请输入"
                    :auto-size="{ minRows: 3, maxRows: 4 }"
                  />
                  <span v-else>{{ formData.remarks !='' ? formData.remarks : '--' }}</span>
                </div>
            </template>
          </detail-layout>

        <template v-if="type == 4">
          <!-- 审核表单 -->
          <ApproveForm ref="approve"/>
        </template>

        <a-row :gutter="24" >
          <a-col :span="24">
            <div class="order-dispose">
              <div class="title-box">
                <span class="before"></span>
                <span>验收明细</span>
              </div>
            </div>
          </a-col>
        </a-row>
        <vxe-table class="my-table table-area" ref="tableForm" :data="formData.matServiceCheckApplyItems"
        :edit-rules="tableRules" resizable :edit-config="{mode:'row', showStatus: true}" max-height="457"
        show-overflow highlight-hover-row size="small">
          <vxe-table-column title="序号" type="seq" width="80"></vxe-table-column>
          <vxe-table-column v-for="item in columns" :key="item.key" :title="item.title" show-overflow="title" :min-width="item.width || 130">
            <template v-slot="{ row }">
              <span v-if="item.key==='engineeringUnit'">{{ getLabel(row[item.key], dictMap.works_quantity_unit) }}</span>
              <span v-else>{{ getLabel(row[item.key], null) }}</span>
            </template>
          </vxe-table-column>

          <vxe-table-column field="actualStartTimeStr" title="实际进场日期" show-overflow="title" width="160">
            <template v-slot="{ row }">
              <a-date-picker v-model="row.actualStartTimeStr" v-if="!isDisabled" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" size="default" style="width: 95%;"/>
              <span v-else>{{getLabel(row.actualStartTimeStr,null)}}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column field="actualEndTimeStr" title="实际完工日期" show-overflow="title" width="160">
            <template v-slot="{ row }">
              <a-date-picker v-model="row.actualEndTimeStr" v-if="!isDisabled" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" size="default" style="width: 95%;"/>
              <span v-else>{{getLabel(row.actualEndTimeStr,null)}}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column field="accepteEngineeringCount" title="本次验收工程量" show-overflow="title" width="160">
            <template v-slot="{ row }">
              <a-input-number v-model="row.accepteEngineeringCount" :min='0.0001'
                :max='99999.9999' :precision='4'  v-if="!isDisabled" style="width: 95%;"/>
              <span v-else>{{getLabel(row.accepteEngineeringCount,null)}}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column field="acceptePrice" title="本次验收金额·含税(元)" show-overflow="title" width="200">
            <template #header="{ column }">
              <div class='table-head'>
                <span>本次验收金额·含税(元)</span>
                <a-popover placement="top" >
                  <template slot="content">
                    该金额包含扣款金额
                  </template>
                  <svg-icon  iconClass="health-info" class='tip-icon'></svg-icon>
                </a-popover>
              </div>
            </template>
            <template v-slot="{ row }">
              <a-input-number v-model="row.acceptePrice" :max='999999999999.99' :min='0.01' :precision='2' @blur='changePrice'
                :disabled=" formData.haveTask == '1'" v-if="!isDisabled" size="default" style="width: 95%;"/>
                <span v-else>{{getLabel(row.acceptePrice,null)}}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column field="actualPayPrice" title="实际付款金额·含税(元)" show-overflow="title" width="200">
            <template v-slot="{ row }">
              <a-input-number v-model="row.actualPayPrice" :max='999999999999.99' :min='0.01' :precision='2'
                 @blur='changePrice' :disabled="actualPayPriceDisabled" v-if="!isDisabled" size="default" style="width: 95%;"/>
                <span v-else>{{getLabel(row.actualPayPrice,null)}}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column field="reducePrice" title="扣款金额_含税(元)" show-overflow="title" width="200">
            <template v-slot="{ row }">
                <span >{{ row.reducePrice }}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column field="remarks" title="备注" show-overflow="title" width="350">
            <template v-slot="{ row,column }">
              <a-input v-model="row.remarks" v-if="!isDisabled" size="default" style="width: 100%;" @blur="$refs.tableForm.updateStatus({ row ,column})"/>
              <span v-else>{{getLabel(row.remarks, null)}}</span>
            </template>
          </vxe-table-column>

          <template v-slot:empty>
            <span>查询无数据</span>
          </template>
        </vxe-table>

        <a-row :gutter="24" >
          <a-col :span="24">
            <div class="order-dispose">
              <div class="title-box">
                <span class="before"></span>
                <span>验收附件</span>
              </div>
            </div>
          </a-col>
        </a-row>

        <a-row :getter='24' class='files-area'>
          <a-col :span='10'>
            <a-form-model :model="uploadFileMap" :rules="fileRules" ref="fileForm" :labelCol="{ style: 'width:125px' }"
               :wrapperCol="{ style: 'width:calc(100% - 125px)' }">
              <a-form-model-item :label='item.label' v-for='(item, index) in filesArr' :key='index' :prop='item.value'>
                <uploadFileView v-model='uploadFileMap[item.value]' :tip='tip' @set="files => handleUploadChange(item.value,files)"
                  :multiple='true' :disabled='isDisabled' accept='.png,.jpg,.jpeg,.bmp,.pdf'>
                  上传
                </uploadFileView>
              </a-form-model-item>
               <a-form-model-item :label="getLabel(item, dictMap.service_check_file_type)" v-for='(item, index) in Object.keys(formData.taskUploadFileMap || {})' :key='`uuid-${index}`'>
                <uploadFileView v-model='formData.taskUploadFileMap[item]' disabled/>
              </a-form-model-item>
            </a-form-model>

          </a-col>

          <a-col :span='14'>
            <p class='file-explain'>附件说明：</p>
            <p class='file-explain' v-for='(item,index) in fileExplain' :key='index'>{{ index+1 }}. {{ item }}</p>
          </a-col>
        </a-row>
        <a-row :getter='24'>

        </a-row>
      </a-spin>
    </div>
    <div class="drawer-form-foot">
      <template  v-if="['2', '7'].includes(type)">
        <throttle-button :disabled="loading" type='plain' label="取消" @click="cancel(false)" class="solar-eye-btn-primary-cancel"/>
        <throttle-button :loading="loading" @click="confirmOrderResolve(true)" :disabled="submitDisabled" label="提交" />
      </template>
      <template  v-else-if='type == 4'>
        <throttle-button :disabled="loading" label="取消" @click="cancel(false)" class="solar-eye-btn-primary-cancel"/>
        <throttle-button :loading="loading" @click="doAudit(1)" label="提交" />
      </template>
      <throttle-button v-else :disabled="loading" label="返回" @click="$emit('cancel')" />
    </div>
    <div @click="openFlowChart" class="flow-chart-btn">
      <svg-icon iconClass="flow"></svg-icon>
      流程图
    </div>
    <flow-chart-drawer
      v-if="showDiagram"
      ref="flowChartDrawer"
      :parentId="parentId"
      :processInstanceId="formData.processInstanceId"
      processDefinitionKey="劳务验收"
      :flowUser="formData.flowUser"
    />
  </div>
</template>

<script>
import initDict from '@/mixins/initDict';
import uploadFileView from '@/components/com/fileUploadView';
import { getCheckDetail, getCheckAudit, getCheckApply } from '@/api/materialsManage/labourCheck';
import ApproveForm from '@/components/com/ApproveForm';
import { findTree, subtract, toFixed, toNumber } from 'xe-utils';

export default {
  components: { uploadFileView, ApproveForm },
  mixins: [initDict],
  props: {
    parentId: {
      type: String,
      required: true,
      default: ''
    }
  },
  data () {
    return {
      model: {
        type: null,
        rowInfo: {}
      },
      formData: {},
      uploadFileMap: {},
      tableData: [
        {
          test: '',
          files: []
        }
      ],
      type: '',
      tip: '提示：请上传jpg、jpeg、png、bmp、pdf格式的文件，最多5个，单个不能超过10MB',

      fileExplain: [
        '租赁服务类：上传对应的《服务合同》；',
        '人工劳务类：厨师/司机/保洁/保安等辅助岗位服务，上传《用工合同》；清洗/除草/除雪/支架调节/螺栓紧固类需上传《验收确认单》、《总结报告》、《扣款考核单》（如有），涉及到采购集采/对公付款类的，还需上传《完工确认单》；',
        '技术服务类：上传《服务合同》/《检测报告》、《扣款考核单》（如有），涉及到采购集采/对公付款类/无合同&报告的，还需上传《完工确认单》；',
        '保险服务类：上传《保险合同》。'
      ],
      tableRules: {
        actualStartTimeStr: [
          { required: true, message: '请选择实际进场日期' }
        ],
        actualEndTimeStr: [
          { required: true, message: '请选择实际完工日期' }
        ],
        accepteEngineeringCount: [
          { required: true, message: '请输入本次验收工程量' }
        ],
        acceptePrice: [
          { required: true, message: '请输入本次验收金额·含税' }
        ],
        actualPayPrice: [
          { required: true, message: '请输入实际付款金额·含税' }
        ],
        remarks: [
          { validator: this.remarksValidator }
        ]
      },
      loading: false,
      showDiagram: false,
      businessType: '',
      fileMap: [],
      filesArr: [],
      fileRules: {},
      labelList: [
        {
          label: '采购方式',
          key: 'purchaseTypeStr'
        }, {
          label: '采购订单',
          key: 'purchaseNo'
        }, {
          slot: 'requireCode'
        }, {
          // label: '工单编号',
          // func: e => e.haveTask == '1',
          slot: 'relatedTaskNo'
        }, {
          label: '验收日期',
          key: 'acceptanceTimeStr'
        }, {
          label: '验收人',
          key: 'acceptancePersonName'
        }, {
          slot: 'remarks',
          span: '24'
        }
      ],
      columns: [
        { title: '履约项目', key: 'psaName', width: 160 },
        { title: '物料编码', key: 'materialCode' },
        { title: '物料名称', key: 'materialName' },
        { title: '需求进场日期', key: 'startTimeStr' },
        { title: '需求完工日期', key: 'endTimeStr' },
        { title: '工程量', key: 'engineeringCount', width: 80 },
        { title: '单位', key: 'engineeringUnit', width: 80 },
        { title: '合同金额(元)', key: 'totalPrice' },
        { title: '已验收工程量', key: 'acceptedEngineeringCount' },
        { title: '已验收金额·含税(元)', key: 'acceptedPrice', width: 160 }
      ]
    };
  },
  created () {
  },
  methods: {
    // type 1申请
    async init (type, rowInfo) {
      this.model = { type, rowInfo };
      this.type = type;
      this.loading = true;
      let res = await getCheckDetail({
        id: rowInfo.id
      });
      this.formData = res.result_data;
      const applyItem = (this.formData.matServiceCheckApplyItems && this.formData.matServiceCheckApplyItems[0]) || {};
      // 如果关联工单，本次验收金额为合同金额
      if (this.formData.haveTask) {
        Object.assign(applyItem, { acceptePrice: applyItem.totalPrice });
      }
      // 初始化默认计算扣款金额_含税
      if (applyItem.actualPayPrice >= 0 && !this.isEmpty(applyItem.actualPayPrice)) {
        Object.assign(applyItem, { 'reducePrice': toNumber(toFixed(subtract(applyItem.acceptePrice || 0, applyItem.actualPayPrice || 0), 2)) });
      }

      this.uploadFileMap = this.formData.uploadFileMap;
      let fileIndex = this.formData.fileUploadTypeList;
      await this.getDictMap(`service_check_file_type,works_quantity_unit`);
      this.fileMap = this.dictMap.service_check_file_type.map(item => {
        return {
          value: item.dataValue,
          label: item.dataLable
        };
      });
      this.filesArr = this.fileMap.filter(item => fileIndex.map(el => el.typeCode).includes(item.value));
      this.filesArr.map(item => {
        let temp = fileIndex.find(el => item.value == el.typeCode);
        let required = !!temp.needFlag;
        this.fileRules[item.value] = [
          {
            required, message: '请上传' + item.label, trigger: 'change'
          }
        ];
      });
      // 如果扣款金额大于0，必须上传考核单
      if (applyItem.reducePrice > 0) {
        this.filesArr.push({ label: '考核单', value: '1' });
      }
      Object.assign(this.fileRules, {
        1: [
          { required: true, message: '请上传考核单', trigger: 'change' }
        ]
      });
      this.loading = false;
      if (['3', '4'].includes(type)) return '服务验收详情';
      return '服务验收';
    },
    async confirmOrderResolve (isCommit) {
      let res = await this.validEvent();
      if (!res) {
        this.$errorScroll();
        return;
      }
      this.$refs.fileForm.validate(valid => {
        if (valid) {
          const { matServiceCheckApplyItems } = this.formData;
          const matServiceCheckApplyItem = matServiceCheckApplyItems[0] || {};
          const { acceptedPrice, acceptePrice, totalPrice = 0, reducePrice } = matServiceCheckApplyItem;
          if (Number(reducePrice || 0) < 0) {
            this.$message.error('扣款金额不能小于0');
            return;
          }
          if (Number(acceptePrice || 0) + Number((acceptedPrice || 0)) > Number(totalPrice.replace(/,/g, ''))) {
            this.$message.error('本次验收金额 + 已验收金额 不能大于合同金额');
          } else if (Number(acceptePrice || 0) + Number(acceptedPrice || 0) < Number(totalPrice.replace(/,/g, ''))) {
            this.$confirm({
              title: '该服务未全部验收，平台会再生成一条剩余服务的验收待办',
              okText: '确定',
              cancelText: '取消',
              onOk: () => {
                this.doExecute(isCommit);
              }
            });
          } else {
            this.doExecute(isCommit);
          }
        } else {
          this.$errorScroll();
        }
      });
    },
    changePrice () {
      const applyItem = this.formData.matServiceCheckApplyItems[0] || {};
      const { acceptePrice, actualPayPrice } = applyItem;
      if (actualPayPrice >= 0 && !this.isEmpty(actualPayPrice)) {
        Object.assign(applyItem, { 'reducePrice': toNumber(toFixed(subtract(acceptePrice || 0, actualPayPrice || 0), 2)) });
        this.$set(this.formData.matServiceCheckApplyItems, '0', applyItem);
      } else {
        Object.assign(applyItem, { 'reducePrice': null });
        this.$set(this.formData.matServiceCheckApplyItems, '0', applyItem);
      }
      if (applyItem.reducePrice > 0) {
        if (this.filesArr.every(item => item.value != '1') || this.filesArr.length == 0) {
          this.filesArr.push({
            label: '考核单',
            value: '1'
          });
        }
      } else {
        if (this.filesArr.some(item => item.value == '1')) {
          this.filesArr.splice(this.filesArr.length - 1, 1);
        }
      }
    },
    // 审核
    doAudit () {
      this.loading = true;
      this.$refs.approve.saveApprove().then(params => {
        // 审核参数
        let auditParam = {
          ...this.formData,
          ...params,
          taskId: this.model.rowInfo.taskId,
          taskDefKey: this.model.rowInfo.taskDefKey
        };
        getCheckAudit(auditParam).then(res => {
          this.loading = false;
          if (res.result_code == '1') {
            this.$message.success('操作成功');
            this.cancel();
          }
        }).catch(() => {
          this.loading = false;
        });
      }).catch(() => {
        this.loading = false;
      });
    },
    async validEvent () {
      const $table = this.$refs.tableForm;
      const errMap = await $table.validate(true).catch(errMap => errMap);
      return !errMap;
    },
    async doExecute (isCommit) {
      this.loading = true;
      this.formData.uploadFileMap = this.uploadFileMap;
      let params = {
        ...this.formData,
        taskId: this.model.rowInfo.taskId,
        taskDefKey: this.model.rowInfo.taskDefKey
      };
      await getCheckApply(params);
      this.$message.success('操作成功');
      this.cancel();
    },
    openFlowChart () {
      this.showDiagram = true;
      const _self = this;
      this.$nextTick(() => {
        _self.$refs.flowChartDrawer.openView();
      });
    },
    // 弹窗关闭回调方法
    cancel (type) {
      this.$emit('cancel', type);
      this.reset();
    },
    reset () {
      Object.assign(this.$data, this.$options.data());
      this.$refs['fileForm'].resetFields();
      this.$refs['fileForm'].clearValidate();
    },
    // 跳转到1：工单大厅，2：服务申请单
    jumpToOuter (type) {
      if (type === '2') {
        const row = { taskNo: this.formData.relatedTaskNo };
        Vue.ls.set('formWorkbenchToOrderHall', row);
        this.handleRouterGo('/orderHall');
      } else if (type === '1') {
        const row = { id: this.formData.applyId, applyType: this.formData.applyType };
        Vue.ls.set('formWorkbenchToDemandapply', row);
        this.handleRouterGo('/materialsManage/demandapply');
      }
    },
    handleRouterGo (url) {
      const permissionList = this.$store.state.user.permissionList;
      const findItem = findTree(permissionList, (item) => item.path === url);
      if (findItem) {
        this.$router.push(url);
      } else {
        this.$message.warning('您当前暂无权限，请联系管理员！');
      }
    },
    handleUploadChange (key, files) {
      this.$refs.fileForm.validateField(key);
    },
    remarksValidator ({ row }) {
      if (Number(row.reducePrice || 0) > 0 && !row.remarks) {
        return new Error('请填写备注');
      }
    },
    isEmpty (str) {
      if (str == null || str == '' || str == undefined) {
        return true;
      } else {
        return false;
      }
    }
  },

  computed: {
    isDisabled () {
      return !['2', '7'].includes(this.type);
    },
    submitDisabled () {
      if (this.formData.haveTask) {
        if (this.formData.finish) {
          return false;
        }
        return true;
      }
      return false;
    },
    actualPayPriceDisabled () {
      // 清晰、除草、系统维护、保险购买
      return ['1', '2', '4', '17'].includes(this.formData.taskType);
    }
  }
};
</script>

<style scoped lang='less'>
.table-head{
  display: flex;
  justify-content: flex-start;
  align-items: center;
  .tip-icon{
    margin-left: 8px;
    outline: none;
  }
}
.files-area{
  padding: 0 24px;
}
.table-area{
  padding: 0 24px 40px;
}
.drawer-form-com {
  padding: 12px 0 0;
  .drawer-form-content{
    .info-alert{
      margin-left: 18px;
      margin-bottom: 24px;
      background: #fff4e2;
      border-radius: 6px;
    }
    :deep(.ant-form-item) {
      width: 100%;
      display: inline-flex;
    }
    .menu-skiplink {
      color: #5BAAEDFF;
      font-weight: 600;

      span {
        line-height: 0.5;
        border-bottom: 1px solid #5BAAEDFF;
      }
    }
  }
}
.file-explain{
  margin: 0;
  color: #666666;
}
</style>
