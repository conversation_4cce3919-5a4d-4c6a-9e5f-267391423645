<template>
  <div class="app-container" style="height: 100%" id='bi-views' >
    <a-spin :spinning="loading">
      <!-- 1.自定义报表数据源设置  -->
      <div class="report-layout">
        <div class="report-main" style="postion: relative" v-for="(detail, index) in dataSource" :key="index">
          <div class="filter-bg"></div>
          <div class="main-content">
            <div style="position: absolute" class="operationimg">
              <img
                src="@/assets/images/bianji.png"
                alt="编辑"
                title="编辑"
                @click="edit(detail.reportCode)"
              />
              <img
                v-if="detail.rdpType == 2"
                src="@/assets/images/shanchu.png"
                alt="删除"
                title="删除"
                @click="delTableInfo(detail.id)"
              />
            </div>
            <img src="@/assets/images/customStyle.png" v-if="templateType == '0'" />
            <img src="@/assets/images/customStyle.png" v-else />
            <div class="main-bottom">
              <div class="main-bottom-title">
                <div v-if="!detail.show">
                  <span >{{ detail.reportName }}</span>
                  <a-icon title="编辑" type="edit" class="edit-icon" :style="{ color: '#666' }" @click="editTitle(detail)"/>
                </div>
                <div v-else class="edit-input">
                  <a-input v-model="changeName" placeholder="请输入报表名称" class="search-input"></a-input>
                  <div class="icons">
                    <a-icon title="保存" type="check" :style="{ color: '#666' }" @click="saveTitle(detail)"/>
                    <a-icon type="reload" title="取消" :style="{ color: '#666' }" @click="cancelTitle(detail)"/>
                  </div>
                </div>
              </div>
              <a-button class="solar-eye-btn-primary" @click="showTableInfo(detail.reportCode)"
                >查看</a-button
              >
            </div>
          </div>
        </div>
      </div>
    </a-spin>
  </div>
</template>

<!-- 二.JS脚本 -->

<script>
import { BiListMixin } from '@/mixins/BiListMixin';
import { postAction } from '@/api/manage';
const webUrl = process.env.VUE_APP_REPORT_WEB_BASE_URL;
const url = process.env.VUE_APP_BI_BASE_URL;
export default {
  mixins: [BiListMixin],
  name: 'CustomStyle',
  props: {
    templateType: {
      type: String,
      default: '0',
      require: true
    }
  },
  data () {
    return {
      // 默认选中项
      activeName: 'first',
      url: {
        list: '/reportFormTemplate/list', // 列表
        delete: '/reportFormTemplate/delete', // 删除
        edit: '/reportFormTemplate/update'
      },
      queryParams: {
        rdpType: this.templateType === '0' ? 1 : null
      }
    };
  },
  created () {
    this.loadData();
  },
  computed: {},
  methods: {
    // 取消修改标题
    cancelTitle (item) {
      this.changeName = '';
      this.$set(item, 'show', false);
    },
    // 保存标题
    saveTitle (item) {
      if (!this.changeName) {
        this.$message.error('请填写报表名称!');
        return;
      }
      let params = {
        id: item.id,
        reportName: this.changeName
      };
      postAction(url + this.url.edit, params)
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('保存成功');
            this.loadData();
          } else {
            this.$message.warning(res.result_msg);
          }
        })
        .catch(() => {
          this.delLoading = false;
        });
    },
    // 编辑标题
    editTitle (item) {
      this.changeName = item.reportName;
      this.$set(item, 'show', true);
    },
    edit (reportCode) {
      window.open(webUrl + '/#/excelreport/designer?reportCode=' + reportCode);
    },
    showTableInfo (reportCode) {
      window.open(webUrl + '/#/excelreport/viewer?reportCode=' + reportCode);
    },
    delTableInfo (id) {
      let that = this;
      this.$confirm({
        title: '删除',
        content: '确定要删除该报表吗?',
        onOk () {
          const requestMap = {
            ids: [id]
          };
          postAction(url + that.url.delete, requestMap)
            .then((res) => {
              if (res.code === '200') {
                that.$message.success('删除成功');
                that.loadData();
              } else {
                that.$message.warning(res.result_msg);
              }
            })
            .catch(() => {
              that.delLoading = false;
            });
        }
      });
    }
  },

  mounted () {}
};
</script>

<!-- 三.CSS样式 -->

<style lang="less" scoped>
@import '../assets/common.less';
</style>
