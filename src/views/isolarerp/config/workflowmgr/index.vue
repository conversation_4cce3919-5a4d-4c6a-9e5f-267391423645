<!-- 流程配置 -->
<template>
  <div class="page-header-index-wide">
    <a-spin :spinning="pageloading">
      <!--工具栏-->
     <a-col class="solar-eye-main-content">
      <!-- 操作按钮 -->
        <div class="operation" style="height: 32px">
          <div class="operation-btn">
          <erp-button perms="920111114101" label="新增" size="default" class="solar-eye-btn-primary" @click="add"></erp-button>
        </div>
      </div>
        <!--表格渲染-->
        <vxe-table :data="dataTab" :height="tableHeight - 24" ref="multipleTable" align="center" border show-overflow highlight-hover-row size="small">
          <vxe-table-column show-overflow="title" :formatter="tabFormatter" field="modelId" title="模板id" min-width="100"></vxe-table-column>
          <vxe-table-column show-overflow="title" :formatter="tabFormatter" field="deploymentId" title="部署id" min-width="100"></vxe-table-column>
          <vxe-table-column show-overflow="title" :formatter="tabFormatter" field="name" title="流程名称" min-width="100"></vxe-table-column>
          <vxe-table-column show-overflow="title" :formatter="tabFormatter" field="resourceName" title="流程定义" min-width="100"></vxe-table-column>
          <vxe-table-column show-overflow="title" :formatter="tabFormatter" field="diagramResourceName" title="流程图" min-width="100"></vxe-table-column>
          <vxe-table-column show-overflow="title" :formatter="tabFormatter" field="key" title="流程key" min-width="100"></vxe-table-column>
          <vxe-table-column visible="showHandle('920111111103,920111111105')" title="操作" fixed="right" :width="120" :resizable="false" class-name="fixed-right-column-120">
            <template v-slot="{ row }">
              <erp-button title="部署" perms="920111111105" size="default" type="primary" icon="redo" @click="deploy(row.modelId)">部署</erp-button>
              <erp-button title="修改" perms="920111111103" size="default" type="primary" icon="edit" @click="modify(row.modelId)">修改</erp-button>
            </template>
          </vxe-table-column>
          <template slot="empty">查询无数据</template>
        </vxe-table>
        <!--分页组件-->
        <page-pagination :pageSize="size" :current="page" :total="total" @size-change="sizeChange"/>
     </a-col>
    </a-spin>

    <a-modal title="新建流程" v-model="dialogDisabled" :maskClosable="false" @cancel="creatDialogClose('creatForm')" width="30%" centered>
      <a-spin :spinning="pageloading">
        <a-form-model :model="creatForm" :rules="rules" ref="creatForm" :labelCol="{ style: 'width: 120px' }" :wrapperCol="{ style: 'width: calc(100% - 120px)' }">
          <a-form-model-item label="流程名称">
            <a-select size="default" v-model="codeValue" :title="creatForm.name" placeholder="请选择" @change="changCodeValue(codeValue)" style="width: 80%;">
              <a-select-option v-for="item in dictMap.flowchart_type" :key="item.key" :value="item.dataValue">{{item.dataLable}}</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-form-model>
      </a-spin>
      <div slot="footer" class="modal-footer">
        <a-button size="default" :loading="pageloading" type="primary" @click="creatFlowBtn('creatForm')">保存</a-button>
        <a-button size="default" :disabled="pageloading" @click="creatDialogClose('creatForm')">取消</a-button>
      </div>
    </a-modal>

    <a-modal title="流程绘制" v-model="workFlowEditorVisible" @cancel="workFlowEditorDialogClose" width="100%" centered class="full-modal work-full-modal">
      <iframe width="100%" style="height: calc(100vh - 275px)" :src="drawWorkFlowSrc"></iframe>
      <template slot="footer">
        <a-button size="default" @click="workFlowEditorDialogClose">返回</a-button>
      </template>
    </a-modal>
  </div>
</template>

<script>
import { queryFlow, createFlowModel, deployProcess } from '@/api/isolarErp/config/workFlow';
import initDict from '@/mixins/initDict';
import tableHeight from '@/mixins/tableHeightAndSearchModel';
import { ACCESS_TOKEN } from '@/store/mutation-types';
// 语言配置
// import $i18n from '@/assets/i18n';

export default {
  mixins: [initDict, tableHeight],
  name: 'index',
  data () {
    return {
      codeValue: '',
      workFlowEditorVisible: false,
      modelId: '1',
      drawWorkFlowSrc: '',
      dataTab: [],
      total: 0,
      size: 10,
      page: 1,
      dialogDisabled: false,
      creatForm: {
        name: '',
        key: ''
      },
      rules: {
        name: [{ required: true, message: '请输入流程名称', trigger: 'blur' }],
        key: [{ required: true, message: '请输入流程key', trigger: 'blur' }]
      }
    };
  },

  created () {
    // 加载数据字典
    this.getDictMap('flowchart_type');
    this.onload();
  },
  methods: {
    // 初始化数据
    async onload () {
      this.pageloading = true;
      let $table = this.$refs.multipleTable;
      $table && await $table.clearScroll();
      let param = {
        curPage: this.page,
        size: this.size
      };
      queryFlow(param).then(res => {
        if (res.result_code == '1') {
          this.dataTab = res.result_data.rows;
          this.total = res.result_data.total;
        } else {
          this.dataTab = [];
          this.total = 0;
          this.$message.warning(res.result_msg);
        }
        this.pageloading = false;
      }).catch(() => {
        this.dataTab = [];
        this.total = 0;
        this.pageloading = false;
      });
    },

    // 新建
    add () {
      this.creatForm.name = '';
      this.creatForm.key = '';
      this.codeValue = '';
      if (this.dictMap.flowchart_type.length > 0) {
        this.codeValue = this.dictMap.flowchart_type[0].codeValue;
        this.creatForm.name = this.dictMap.flowchart_type[0].dispName;
        this.creatForm.key = 'activiti' + this.dictMap.flowchart_type[0].codeValue + this.dictMap.flowchart_type[0].dictId + 'Workflow';
      }
      this.dialogDisabled = true;
    },

    // 关闭新建弹框
    creatDialogClose (ruleForm) {
      this.dialogDisabled = false;
      this.$refs[ruleForm].resetFields();
    },

    // 新增流程选择chang事件
    changCodeValue (codeValue) {
      for (let i = 0; i < this.dictMap.flowchart_type.length; i++) {
        if (this.dictMap.flowchart_type[i].codeValue == codeValue) {
          this.creatForm.name = this.dictMap.flowchart_type[i].dispName;
          this.creatForm.key = 'activiti' + this.dictMap.flowchart_type[i].codeValue + this.dictMap.flowchart_type[i].dictId + 'Workflow';
          return;
        }
      }
    },

    // 新建弹框:保存
    creatFlowBtn (ruleForm) {
      this.$refs[ruleForm].validate((valid) => {
        if (valid) {
          createFlowModel(this.creatForm).then(res => {
            if (res.result_code == '1') {
              this.creatDialogClose(ruleForm);
              this.modify(res.result_data);
            } else {
              this.$message.warning(res.result_msg);
            }
          });
        } else {
          return false;
        }
      });
    },

    // 修改
    modify (modelId) {
      let token = Vue.ls.get(ACCESS_TOKEN);
      let url = process.env.VUE_APP_API_ERP_URL;
      // 本地调试需要static目录
      // let path = (process.env.NODE_ENV == 'development' ? '/static/' : './static/');
      this.drawWorkFlowSrc = `https://staticres.isolareye.com/js/activiti/modeler.html?modelId=${modelId}&token=${token}&url=${url}`;
      this.workFlowEditorVisible = true;
    },

    // 部署
    deploy (modelId) {
      let self = this;
      self.$confirm({
        title: '确认部署？',
        okText: '确定',
        cancelText: '取消',
        centered: true,
        onOk () {
          var requestParam = {
            'modelId': modelId
          };
          deployProcess(requestParam).then(res => {
            if (res.result_code == '1') {
              self.$message.success('部署成功');
              self.onload();
            } else {
              self.$message.warning('部署失败');
            }
          });
        },
        onCancel () {
          self.$message.warning('部署取消');
        }
      });
    },

    // 流程绘制关闭
    workFlowEditorDialogClose () {
      localStorage.removeItem('xUrl');
      localStorage.removeItem('xToken');
      localStorage.removeItem('xTenantId');
      this.onload();
      this.workFlowEditorVisible = false;
    },

    // 分页事件
    pageChange (e) {
      this.page = e;
      this.onload();
    },

    // 分页事件
    sizeChange (p, e) {
      this.page = p;
      this.size = e;
      this.onload();
    }
  }
};
</script>

<style lang="less" scoped>
  :deep(.ant-form-item) {
    width: 100%;
    display: inline-flex;
  }
</style>
<style>
  .work-full-modal .ant-modal-body{
    padding: 24px !important;
  }
</style>
