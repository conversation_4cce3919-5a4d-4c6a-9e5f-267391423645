<template>
  <div class="pa-model" :class="{'pa-model-enabled':!disabled,'pa-model-disabled':disabled}">
    <!-- <a-popover placement="topLeft"> -->
    <!-- <template slot="content"> -->
    <!-- <div>
          <a-row v-if="psNameList.length == 0" >请选择实体电站</a-row>
          <a-row v-else :key="index" v-for="name,index in psNameList">{{name}}</a-row>
        </div> -->
    <!-- </template> -->
    <div class="title" @click="showModel">{{psNameList.join()}}</div>
    <span v-show="psNameList.length && !disabled" class="clear-icon">
      <a-icon @click.prevent="clear" type="close-circle" />
    </span>
    <!-- </a-popover> -->
    <a-modal v-model="open" :maskClosable="false" @cancel="cancel" centered title="选择" width="70%">
      <a-spin :spinning="loading">
        <a-row style="margin-bottom: 10px;">
          <a-col :span="12">
            <a-input-search allow-clear v-model="queryPsName" placeholder="请输入实体电站名称" enter-button @search="clickSearch" :loading="loading" />
          </a-col>
          <a-col :span="8" :offset="4">
            <a-input-search allow-clear v-model="queryTwoPsName" placeholder="请输入已选择实体电站名称" enter-button @search="clickTwoSearch" />
          </a-col>
        </a-row>
        <a-row>
          <a-col :xs='24' :sm='24' :md='12'>
            <vxe-table :data="psaTable" height="457" ref="psaTable" @checkbox-all="handleSelectionChange"
              resizable @checkbox-change="handleSelectionChange" align="center" border show-overflow
              highlight-hover-row size="small">
              <!-- 表格最左边的复选框 -->
              <vxe-table-column type="checkbox" width="50" align="center" fixed="left"></vxe-table-column>
              <vxe-table-column show-overflow="title" :formatter="tabFormatter" field="psName" title="电站名称"></vxe-table-column>
              <template v-slot:empty>
                <span>查询无数据</span>
              </template>
            </vxe-table>
            <!--分页组件-->
            <page-pagination :pageSize="size" :current="page" :total="total" @size-change="sizeChange" />
          </a-col>
          <a-col :xs='24' :sm='12' :md='4' style="height:50vh; display:flex; align-items:center; overflow:auto;">
            <div style="margin: auto;">
              <a-button type="primary" size="default" @click="add" :disabled="selection.length == 0 ">添加<a-icon type="right"></a-icon>
              </a-button>
              <br />
              <a-button style="margin-top: 10px;" type="primary" size="default" @click="remove" :disabled="checkdIds.length == 0">
                <a-icon type="left"></a-icon>移除
              </a-button>
            </div>
          </a-col>
          <a-col :xs='24' :sm='12' :md='8' class="col-item">
            <a-checkbox-group v-model="checkdIds">
              <a-row v-for="item in checkboxDataView" :key="item.psId">
                <a-checkbox :value="item.psId" :title="item.psName">{{ item.psName }}</a-checkbox>
              </a-row>
              <div v-show="checkboxDataView.length == 0" class="no-data">
                <span>暂无数据</span>
              </div>
            </a-checkbox-group>
          </a-col>
        </a-row>
      </a-spin>
      <template slot="footer">
        <div class="modal-footer">
          <throttle-button label="取消" :disabled="loading" @click="cancel()" class='solar-eye-btn-primary-cancel'/>
          <throttle-button label="确定" :loading="loading"  @click="submit()" />
        </div>
      </template>
    </a-modal>
  </div>
</template>

<script>
import {
  selectEntityStation,
  selectCheckedEntityStation
} from '@/api/isolarErp/config/archives';
export default {
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
    psaId: {
      type: [String, Number],
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    'value' (val, old) {
      if (val != this.psIds) {
        this.psIds = val;
        this.getCheckData(val);
      }
    }
  },
  data () {
    return {
      loading: false,
      selection: [],
      psaTable: [],
      page: 1,
      size: 10,
      total: 0,
      checkboxData: [],
      checkboxDataView: [],
      checkdIds: [],
      open: false,
      psNameList: [],
      psIds: '',
      queryPsName: '',
      queryTwoPsName: ''
    };
  },
  mounted () {
    this.psIds = this.value;
    this.queryPsName = '';
    this.queryTwoPsName = '';
  },
  methods: {
    // 数据查询
    async queryData () {
      this.loading = true;
      let $table = this.$refs.psaTable;
      if ($table) {
        await $table.clearScroll();
        $table.clearCheckboxRow(); // 清除选中
        this.selection = [];
      }
      const map = {
        psaId: (this.psaId == null ? '' : this.psaId.toString()),
        curPage: this.page,
        size: this.size,
        psName: this.queryPsName,
        exclusiveIds: this.checkboxData.map(itm => itm.psId)
      };
      selectEntityStation(map).then(res => {
        this.psaTable = (res.result_code == '1' ? res.result_data.rows : []);
        this.total = (res.result_code == '1' ? res.result_data.total : 0);
        this.loading = false;
      });
    },
    // 获取已选的数据
    getCheckData (val) {
      val = (val == null ? '' : val.toString());
      if (!val) {
        this.psNameList = [];
        this.$emit('change', '');
        this.checkboxData = [];
        this.checkboxDataView = [];
        return;
      }
      const map = {
        psIds: val.split(',')
      };
      selectCheckedEntityStation(map).then(res => {
        let data = (res.result_code == '1' ? res.result_data : []);
        this.checkboxData = data;
        this.checkboxDataView = data;
        let names = [];
        data.forEach(item => {
          names.push(item.psName);
        });
        this.psNameList = names;
      });
    },
    add () {
      this.checkboxData = [...this.checkboxData, ...this.selection];
      this.checkboxDataView = JSON.parse(JSON.stringify(this.checkboxData));
      // 清除选中
      this.$refs.psaTable.clearCheckboxRow();
      this.selection = [];
      this.queryData();
    },
    remove () {
      this.checkboxData = this.checkboxData.filter(item => !this.checkdIds.includes(item.psId));
      this.checkboxDataView = JSON.parse(JSON.stringify(this.checkboxData));
      this.checkdIds = [];
      this.queryData();
    },
    // 打开选择实体电站的弹窗
    showModel () {
      // 非编辑状态不查询数据
      if (this.disabled || this.open) {
        return;
      }
      this.open = true;
      this.queryData();
    },
    // 确定
    submit () {
      // if (this.checkboxData.length == 0) {
      //   this.$message.warning('请至少勾选一条数据');
      //   return;
      // }
      let names = [];
      let ids = [];
      this.checkboxData.forEach(item => {
        ids.push(item.psId);
        names.push(item.psName);
      });
      this.psNameList = names;
      this.psIds = ids.join(',');
      this.$emit('change', ids.join(','));
      this.cancel();
    },
    // 清除
    clear () {
      this.psNameList = [];
      this.psIds = '';
      this.$emit('change', '');
    },
    clickSearch () {
      if (this.queryPsName) {
        this.page = 1;
        this.size = 10;
      }
      this.queryData();
    },
    // 模糊查询已经选择的电站
    clickTwoSearch () {
      if (this.queryTwoPsName) {
        this.checkboxDataView = this.checkboxData.filter((item) => item.psName.indexOf(this.queryTwoPsName) != -1);
      } else {
        this.checkboxDataView = JSON.parse(JSON.stringify(this.checkboxData));
      }
    },
    // 分页事件
    pageChange (e) {
      this.page = e;
      this.queryData();
    },
    // 分页事件
    sizeChange (p, e) {
      this.page = p;
      this.size = e;
      this.queryData();
    },
    // 表格-行选中
    handleSelectionChange (val) {
      this.selection = val.records;
    },
    // model关闭回调
    cancel () {
      this.$refs.psaTable.clearCheckboxRow(); // 清除选中
      this.selection = [];
      this.page = 1;
      this.size = 10;
      this.psaTable = [];
      this.psIds = '';
      this.queryPsName = '';
      this.queryTwoPsName = '';
      this.open = false;
    }
  }
};
</script>

<style lang="less" scoped>
  .pa-model {
    width: 100%;
    height: 32px;
    position: relative;
    display: inline-block;
    background-image: none;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    transition: all 0.3s;
    overflow: hidden;
    margin-top: 4px;
  }

  .pa-model-enabled {
    color: rgba(0, 0, 0, 0.65);
  }

  .pa-model-disabled {
    color: rgba(0, 0, 0, 0.25);
  }

  .title {
    padding: 4px 11px;
    width: calc(100% - 22px);
    height: 32px;
    line-height: 1.5;
    // color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    overflow: hidden;
  }

  .pa-model-enabled:hover {
    border-color: #1890FF;
    cursor: pointer;
  }

  .pa-model-disabled:hover {
    cursor: not-allowed;
  }

  .clear-icon {
    position: absolute;
    right: 8px;
    top: 0;
    z-index: 8;
    line-height: 32px;
    display: none;
  }

  .pa-model:hover .clear-icon {
    display: block;
  }

  .clear-icon .anticon {
    font-size: 12px;
    cursor: pointer;
  }

  .col-item {
    height: 457px;

    :deep(.ant-checkbox-group) {
      height: 100%;
      overflow: auto;
      width: 100%;
      border: 1px solid #e8e8e8;
    }

    :deep(.ant-checkbox-wrapper) {
      margin-left: 8px;
      margin-right: 0;
    }

    .no-data {
      height: 100%;
      display: flex;

      span {
        margin: auto;
      }
    }
  }
</style>
