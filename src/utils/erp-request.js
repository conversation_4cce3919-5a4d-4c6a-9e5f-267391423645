// import Vue from 'vue'
import axios from 'axios';
import store from '@/store';
import { Modal, notification } from 'ant-design-vue';
import { ACCESS_TOKEN, TENANT_ID, USER_INFO } from '@/store/mutation-types';
import { isResponseHeaderToken } from './axios';
/**
 * 【指定 axios的 baseURL】
 * 如果手工指定 baseURL: '/solareye'
 * 则映射后端域名，通过 vue.config.js
 * @type {*|string}
 */

// let apiBaseUrl = "http://localhost:8080";
let apiBaseUrl = process.env.VUE_APP_API_ERP_URL;

// let apiBaseUrl = 'http://**************:8080';
// let apiBaseUrl = "https://api-fat.isolareye.com/isolar-erp";'http://***************:8080/' ||

// 创建 axios 实例
const service = axios.create({
  baseURL: apiBaseUrl // api base_url
  // timeout: 9000 // 请求超时时间
});
const CanCelToken = axios.CancelToken;
const source = CanCelToken.source();
const err = (error) => {
  if (error.response || error.data) {
    let data = '';
    let status = '';
    if (error.response) {
      data = error.response.data;
      status = error.response.status;
    } else {
      data = error.data;
      status = error.data.result_code;
    }
    status = (isNaN(status) ? '' : Number(status));
    switch (status) {
      case 403:
        notification.error({ message: '系统提示', description: '拒绝访问', duration: 4 });
        break;
      case 500:
        if (error.response && error.response.request && error.response.request.type === 'blob') {
          blobToJson(data);
        } else {
          notification.error({
            message: '系统提示',
            description: (data.message ? data.message : (data.result_msg ? data.result_msg : '系统异常')),
            duration: 4
          });
        }
        break;
      case 404:
        notification.error({ message: '系统提示', description: '很抱歉，资源未找到!', duration: 4 });
        break;
      case 504:
        notification.error({ message: '系统提示', description: '网络超时' });
        break;
      case 401:
        notification.destroy();
        notification.error({ message: '系统提示', description: (data.message ? data.message : data.result_msg) || 'Token失效，请重新登录!' });
        store.dispatch('Logout').then(() => {
          Vue.ls.remove(ACCESS_TOKEN);
          try {
            let href = window.location.href;
            path = href.split('/#')[1];
            if (path != '/' && path.indexOf('/user/login') == -1) {
              window.location.reload();
            }
          } catch (e) {
            window.location.reload();
          }
        });
        source.cancel();
        // notification.error({ message: '系统提示', description:'未授权，请重新登录',duration: 4})
        // if (token) {
        //   store.dispatch('Logout').then(() => {
        //     setTimeout(() => {
        //       window.location.reload()
        //     }, 1500)
        //   })
        // }
        break;
      default:
        let href = window.location.href;
        let path = href.split('/#')[1];
        if (!Vue.ls.get(USER_INFO) && path != '/' && path.indexOf('/user/login') == -1) {
          source.cancel();
          store.dispatch('Logout').then(() => {
            Vue.ls.remove(ACCESS_TOKEN);
            window.location.reload();
          });
        }
        notification.error({
          message: '系统提示',
          description: data.message || data.result_msg || data.msg || '系统异常',
          duration: 4
        });
        break;
    }
  }
  return new Promise((resolve, reject) => {
    reject(error);
  });
};

// request interceptor
service.interceptors.request.use(config => {
  const token = Vue.ls.get(ACCESS_TOKEN);
  if (token) {
    config.headers['X-Access-Token'] = token; // 让每个请求携带自定义 token 请根据实际情况自行修改
    config.headers['token'] = token;
  }
  config.headers['client-type'] = store.getters.device == 'mobile' ? 'app' : 'web';
  config.headers['Authorization'] = 'Bearer ' + token;
  config.headers['device-id'] = localStorage.getItem('deviceId');
  // update-begin-author:taoyan date:2020707 for:多租户
  let tenantid = Vue.ls.get(TENANT_ID);
  config.headers['tenant_id'] = tenantid;
  if (config.method == 'get') {
    if (config.url.indexOf('sys/dict/getDictItems') < 0) {
      config.params = {
        _t: Date.parse(new Date()) / 1000,
        ...config.params
      };
    }
  }
  let userInfo = Vue.ls.get(USER_INFO);
  if (userInfo) {
    config.data['dataRoles'] = userInfo.dataRoles || [];
    config.data['orgCode'] = (userInfo.orgCode ? userInfo.orgCode : '');
    config.data['userId'] = (userInfo.id ? userInfo.id : '');
    // config.data['empName'] = (userInfo.realname ? userInfo.realname : '');
    config.data['userAccount'] = (userInfo.username ? userInfo.username : '');
    config.data['workNo'] = (userInfo.workNo ? userInfo.workNo : '');
    config.data['needHandover'] = (userInfo.needHandover ? userInfo.needHandover : '');
    config.data['hasAllData'] = (userInfo.hasAllData ? userInfo.hasAllData : '');
    config.data['sysTenantId'] = tenantid; // 测试数据
    config.data['lang'] = '_zh_CN';
  }
  return config;
}, (error) => {
  return new Promise((resolve, reject) => {
    reject(error);
  }).catch((e) => {});
});

// response interceptor
service.interceptors.response.use((response) => {
  isResponseHeaderToken(response);
  let data = response.data;
  if (response.status == 200 && data && (data.result_code == '1' || data.code == '0')) {
    translate.request.listener.delayExecuteTime = 10000;
    translate.request.listener.minIntervalTime = 10000;
    translate.request.listener.start();
    translate.execute();
    return data;
  } else {
    return err(response);
  }
}, err);
/**
     * Blob解析
     * @param data
     */
function blobToJson (data) {
  let fileReader = new FileReader();
  let token = Vue.ls.get(ACCESS_TOKEN);
  fileReader.onload = function () {
    try {
      let jsonData = JSON.parse(this.result); // 说明是普通对象数据，后台转换失败
      if (jsonData.status === 500) {
        console.log('token----------》', token);
        if (token && jsonData.message.includes('Token失效')) {
          Modal.error({
            title: '登录已过期',
            content: '很抱歉，登录已过期，请重新登录',
            okText: '重新登录',
            mask: false,
            onOk: () => {
              store.dispatch('Logout').then(() => {
                Vue.ls.remove(ACCESS_TOKEN);
                window.location.reload();
              });
            }
          });
        }
      }
    } catch (err) {
      // 解析成对象失败，说明是正常的文件流
      console.log('blob解析fileReader返回err', err);
    }
  };
  fileReader.readAsText(data);
}

export {
  service as axios
};
