---
kind: Service
apiVersion: v1
metadata:
  name: {DEPLOY_ENV}-sv-{APP_NAME}
  namespace: isolarerp-{DEPLOY_ENV}
  labels:
    app: {DEPLOY_ENV}-sv-{APP_NAME}
    env: {DEPLOY_ENV}
    idc: {APOLLO_IDC}
spec:
  ports:
    - protocol: TCP
      port: 80
      targetPort: web
  selector:
    app: {DEPLOY_ENV}-dp-{APP_NAME}
    env: {DEPLOY_ENV}
    idc: {APOLLO_IDC}
  sessionAffinity: ClientIP

---
kind: Service
apiVersion: v1
metadata:
  name: {DEPLOY_ENV}-sv-{APP_NAME}-metrics
  namespace: isolarerp-{DEPLOY_ENV}
  labels:
    app: springboot
    component: erp
    release: sv-{APP_NAME}-metrics
spec:
  ports:
    - name: metrics
      protocol: TCP
      port: 80
      targetPort: web
  selector:
    app: {DEPLOY_ENV}-dp-{APP_NAME}
    env: {DEPLOY_ENV}
    idc: {APOLLO_IDC}
  type: ClusterIP

---
kind: Deployment
apiVersion: apps/v1beta2
metadata:
  name: {DEPLOY_ENV}-dp-{APP_NAME}
  namespace: isolarerp-{DEPLOY_ENV}
  labels:
    app: dp-{APP_NAME}
    env: {DEPLOY_ENV}
    idc: {APOLLO_IDC}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: {DEPLOY_ENV}-dp-{APP_NAME}
      env: {DEPLOY_ENV}
      idc: {APOLLO_IDC}
  template:
    metadata:
      labels:
        app: {DEPLOY_ENV}-dp-{APP_NAME}
        env: {DEPLOY_ENV}
        idc: {APOLLO_IDC}
    spec:
      nodeSelector:
        node2: isolarerp
      containers:
        - image: {IMAGE_URL}:{IMAGE_TAG}
          securityContext:
            privileged: true
          imagePullPolicy: Always
          name: {DEPLOY_ENV}-container-{APP_NAME}
          env:
            - name: CHANGE_TIME
              value: "1"
          envFrom:
            - configMapRef:
                name: {DEPLOY_ENV}-solareye-cm
          resources:
            requests:
              cpu: "100m"
              memory: "2Gi"
            limits:
              cpu: "4"
              memory: "8Gi"
          ports:
            - name: web
              protocol: TCP
              containerPort: 80
          readinessProbe:
            tcpSocket:
              port: web
            initialDelaySeconds: 10
            periodSeconds: 5
          livenessProbe:
            tcpSocket:
              port: web
            initialDelaySeconds: 120
            periodSeconds: 10
      imagePullSecrets:
        - name: hz-regcred-isolarerp
      dnsPolicy: ClusterFirst
      restartPolicy: Always