<!DOCTYPE html>
<html lang="zh-cmn-<PERSON>">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="pragma" content="no-cache">
    <meta http-equiv="cache-control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="expires" content="no-cache">
    <meta http-equiv="cache" content="no-cache">
    <title>SolarEye</title>
    <link rel="shortcut icon" href="<%= BASE_URL %>solareye_logo.png">
    <script src="https://staticres.isolareye.com/js/babel-polyfill/polyfill_7_2_5.js"></script>
    <script src="https://staticres.isolareye.com/js/jQuery/jquery.min.js"></script>
    <!-- <script type="text/javascript" src="https://webapi.amap.com/maps?v=1.4.15&key=4b8c94da750af12eae696d3da40a92f7&plugin=AMap.CustomLayer,AMap.Geocoder"></script> -->
    <script type="text/javascript" src="https://webapi.amap.com/maps?v=1.4.15&key=4b8c94da750af12eae696d3da40a92f7&plugin=AMap.CustomLayer,AMap.Geocoder,AMap.MarkerClusterer,AMap.Text,AMap.InfoWindow,AMap.PlaceSearch,AMap.AdvancedInfoWindow,AMap.MouseTool,AMap.DistrictSearch"></script>
    <script src="//webapi.amap.com/ui/1.1/main.js?v=1.1.1"></script>
    <script src="https://staticres.isolareye.com/js/vue/vue.min.js"></script>
    <link href="./js/solareye-vue.css?v=202408202107"  rel="stylesheet" type="text/css">
    <script src="./js/solareye-vue.umd.min.js?v=202408202107"></script>
    <style>
      /* body.bg {
              background: url('https://staticres.isolareye.com/image/public/login_bg_load.png') no-repeat;
              background-size: cover;
          }*/
      [v-cloak] {
        display: none !important;
      }
  
      html,
      body {
        height: 100%;
        margin: 0px;
        padding: 0px;
        background-size: cover;
      }
  
      .third-loading {
        background: none;
      }
  
      .third-loading .loading-container {
        display: none;
      }
  
      .self-loading {
        background: url('./bg.svg') no-repeat;
        background-size: cover;
      }
  
      .self-loading .loading-container {
        display: inline-block;
      }
  
  
      .amap-logo {
        display: none;
        opacity: 0 !important;
      }
  
      .amap-copyright {
        opacity: 0;
      }
  
      .loading-container {
        width: 100%;
        /* background: url('./bg.svg') no-repeat; */
        background-size: cover;
        display: none;
        overflow: hidden;
        position: absolute;
        top: 50%;
        left: 50%;
        text-align: center;
        -webkit-transform: translate(-50%, -50%);
        transform: translate(-50%, -50%);
        color: #000000;
        height: 100%;
      }
  
      div.loading-main {
        display: inline-flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 100%;
      }
  
      .bg-left {
        display: flex;
        flex-direction: column;
        margin: 3% 0 3% 5%;
        align-items: baseline;
        width: 30%;
      }
  
      .bg-left-title,
      .bg-left-device-title {
        font-family: Impact;
        font-size: 60px;
        color: #0C3763;
        letter-spacing: 5.6px;
        line-height: 82px;
      }
  
      .bg-left-title img {
        height: 40px;
        object-fit: cover;
      }
  
      .bg-left-device-title {
        opacity: 0.8;
        font-family: PingFangSC-Medium;
        letter-spacing: 0;
        margin-top: 4px;
      }
  
      .bg-right {
        flex: 1;
        width: fit-content;
        margin-right: 5%;
  
      }
  
      .bg-right img {
        width: 100%;
        height: 100%;
      }
  
      .box {
        width: 80%;
        height: 10px;
        background: #D8D8D8;
        border-radius: 10px;
        margin-top: 16px;
        position: relative;
        z-index: -1;
        box-sizing: content-box;
      }
  
      .logo-pos {
        position: fixed;
        top: 3%;
        left: 4%;
        height: 40px;
        object-fit: cover;
      }
  
      .self-system-loading {
        background: none;
        background-size: cover;
      }
  
      .self-system-loading .loading-container {
        display: inline-block;
      }
  
      .loding-title {
        opacity: 0.7;
        font-family: PingFangSC-Medium;
        font-size: 16px;
        color: #999999;
        ;
        letter-spacing: 0;
        margin-top: 16px;
        text-align: left;
      }
  
      .process-child {
        position: relative;
        height: 10px;
        margin: 0;
        border-radius: inherit;
        background: linear-gradient(270deg, #FF7900 0%, #FF9200 100%);
        width: 0%;
      }
  
      .process-animate {
        background: linear-gradient(270deg, #FF7900 0%, #FF9200 100%);
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        border-radius: inherit;
        animation: process 1s linear forwards;
      }
  
      @keyframes process {
        0% {
          left: 0;
          right: 100%;
        }
  
        20% {
          right: 80%
        }
  
        40% {
          right: 60%;
        }
  
        60% {
          right: 40%;
        }
  
        80% {
          right: 20%;
        }
  
        100% {
          right: 0;
        }
      }
    </style>
    <!-- 全局配置 -->
  <script>
    let width = 0,
      time = "";
    window._CONFIG = {};
    let timeInter = setInterval(() => {
      if (document.getElementsByTagName('body')[0]) {
        let url = window.location.href;
        // 大屏没有大风车loading
        if (!(url && url.indexOf('/screen/jd') != -1)) {
          document.getElementsByTagName('body')[0].classList = 'self-system-loading'
        } else {
          // document.body.classList.add('bg')
        }
        clearInterval(timeInter);
      }
    }, 1);
    time = setInterval(() => {

      width = width + 10;
      let url = window.location.href;
      if (width <= 100) {
        if (document.getElementsByClassName("process-child").length > 0) {
          document.getElementsByClassName("process-child")[0].style.width = width + "%";
        } else {
          if (!(url && url.indexOf('/screen/jd') != -1)) {
            document.body.classList.add('bg')
          }
        }
        clearInterval(time);
      } else {
        width = 0;
      }

    }, 800);
    if (width >= 100) {
      document.body.classList.add('bg')
      clearInterval(time);
    }
  </script>

  <!-- 百度统计埋点
-->
  <script>
    var _hmt = _hmt || [];
    (function () {
      var hm = document.createElement("script");
      hm.src = "https://hm.baidu.com/hm.js?4f03a1068f4b6398d6e78c79f95585e1";
      var s = document.getElementsByTagName("script")[0];
      s.parentNode.insertBefore(hm, s);
    })();
  </script>

  
</head>

<body>
    <div class="loading-container" id="solaryLoading">
        <img src="./jhlg/login_logo.png" alt="" class="logo-pos isVisible">
        <div class="loading-main">
            <div class="bg-left">
                <div class="bg-left-title isVisible">
                    <img src="https://staticres.isolareye.com/image/public/solareye.png" alt="solareye">
                </div>
                <!-- <div class="bg-left-device-title isVisible">
                    智慧能源营维平台
                </div> -->
                <div class="box">
                    <div class="process-child">
                        <p class="process-animate"></p>
                    </div>
                    <div class="loding-title">正在加载，请稍后...</div>
                </div>
            </div>
            <div class="bg-right">
                <img src="https://staticres.isolareye.com/image/public/loading.gif" type="image/png" class="isVisible">
            </div>
        </div>

    </div>
    <div id="app">
    </div>
    <div id="translate"></div>
    <script src="https://cdn.staticfile.net/translate.js/3.5.2/translate.js"></script>
    <script>    
    translate.language.setLocal('chinese_simplified'); //设置本地语种（当前网页的语种）。如果不设置，默认就是 'chinese_simplified' 简体中文。 可填写如 'english'、'chinese_simplified' 等，具体参见文档下方关于此的说明
    translate.service.use('client.edge');
    translate.selectLanguageTag.languages = 'english,chinese_simplified';
    translate.listener.start();    //开启html页面变化的监控，对变化部分会进行自动翻译。注意，这里变化区域，是指使用 translate.setDocuments(...) 设置的区域。如果未设置，那么为监控整个网页的变化
    translate.execute(); //执行翻译初始化操作，显示出select语言选择
    // 隐藏中英文切换
    let translateDom = document.getElementById('translate');
    if (!localStorage.getItem('translate')) {
      localStorage.setItem('translate', 'block')
    }
    translateDom.style.display = localStorage.getItem('translate');
    </script>
    <script src="https://staticres.isolareye.com/js/hkplayer/h5player.min.js"></script>
    <script src="./dll/dllModules.js"></script>
    <script src="./dll/ui.js"></script>
    <script src="./dll/echarts.js"></script>
    <script src="./dll/jspdf.js"></script>
    <script src="https://staticres.isolareye.com/js/vue/axios.min.js"></script>
    <script src="https://staticres.isolareye.com/js/vue/vue-router.min.js"></script>
    <script src="https://staticres.isolareye.com/js/vue/vuex.min.js"></script>

</body>
</html>
<style lang="less" scoped>
  #translate {
    position: absolute;
    color: rgba(0, 0, 0, 0.65) !important;
    right: 270px;
    top: 46px;
    z-index: 2147483647;
  }
  #translateSelectLanguage {
    height: 32px;
    width: 150px !important;   
    border-radius: 5px;
    font-size: 14px !important;
    border-color: #d9d9d9 !important;
    border-width: 1px !important;

  }
</style>