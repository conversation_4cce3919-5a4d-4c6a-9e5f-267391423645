{"name": "editor", "content": {"./node_modules/vue/dist/vue.runtime.esm.js": {"id": 52, "buildMeta": {"exportsType": "namespace", "providedExports": ["EffectScope", "computed", "customRef", "default", "defineAsyncComponent", "defineComponent", "del", "effectScope", "getCurrentInstance", "getCurrentScope", "h", "inject", "isProxy", "isReactive", "is<PERSON><PERSON><PERSON>ly", "isRef", "isShallow", "mark<PERSON>aw", "mergeDefaults", "nextTick", "onActivated", "onBeforeMount", "onBeforeUnmount", "onBeforeUpdate", "onDeactivated", "onErrorCaptured", "onMounted", "onRenderTracked", "onRenderTriggered", "onScopeDispose", "onServerPrefetch", "onUnmounted", "onUpdated", "provide", "proxyRefs", "reactive", "readonly", "ref", "set", "shallowReactive", "shallowReadonly", "shallowRef", "toRaw", "toRef", "toRefs", "triggerRef", "unref", "useAttrs", "useCssModule", "useCssVars", "useSlots", "version", "watch", "watchEffect", "watchPostEffect", "watchSyncEffect"]}}, "./node_modules/webpack/buildin/global.js": {"id": 135, "buildMeta": {"providedExports": true}}, "./node_modules/process/browser.js": {"id": 313, "buildMeta": {"providedExports": true}}, "./node_modules/@wangeditor/editor/dist/index.esm.js": {"id": 456, "buildMeta": {"exportsType": "namespace", "providedExports": ["Boot", "Dom<PERSON><PERSON>or", "SlateEditor", "SlateElement", "SlateLocation", "SlateNode", "<PERSON><PERSON><PERSON><PERSON>", "SlatePoint", "SlateRange", "SlateText", "SlateTransforms", "<PERSON><PERSON><PERSON>", "createEditor", "createToolbar", "createUploader", "default", "genModalButtonElems", "genModalInputElems", "genModalTextareaElems", "i18nAddResources", "i18nChangeLanguage", "i18nGetResources", "t"]}}, "./node_modules/timers-browserify/main.js": {"id": 615, "buildMeta": {"providedExports": true}}, "./node_modules/setimmediate/setImmediate.js": {"id": 616, "buildMeta": {"providedExports": true}}, "./node_modules/@wangeditor/editor-for-vue/dist/index.esm.js": {"id": 1447, "buildMeta": {"exportsType": "namespace", "providedExports": ["Editor", "<PERSON><PERSON><PERSON>"]}}}}