{"name": "jspdf", "content": {"./node_modules/@babel/runtime/helpers/typeof.js": {"id": 85, "buildMeta": {"providedExports": true}}, "./node_modules/webpack/buildin/global.js": {"id": 135, "buildMeta": {"providedExports": true}}, "./node_modules/fflate/esm/browser.js": {"id": 471, "buildMeta": {"exportsType": "namespace", "providedExports": ["Deflate", "AsyncDeflate", "deflate", "deflateSync", "Inflate", "AsyncInflate", "inflate", "inflateSync", "Gzip", "AsyncGzip", "gzip", "gzipSync", "<PERSON><PERSON><PERSON>", "AsyncGunzip", "gunzip", "gunzipSync", "<PERSON><PERSON><PERSON>", "AsyncZlib", "zlib", "zlibSync", "<PERSON><PERSON><PERSON><PERSON>", "AsyncUnzlib", "unz<PERSON>b", "unzlibSync", "compress", "AsyncCompress", "compressSync", "Compress", "Decompress", "AsyncDecompress", "decompress", "decompressSync", "strToU8", "strFromU8", "zip", "zipSync", "unzip", "unzipSync"]}}, "./node_modules/html2canvas/dist/html2canvas.js": {"id": 1027, "buildMeta": {"providedExports": true}}, "./node_modules/jspdf/dist/jspdf.es.min.js": {"id": 1840, "buildMeta": {"exportsType": "namespace", "providedExports": ["default", "AcroForm", "AcroFormAppearance", "AcroFormButton", "AcroFormCheckBox", "AcroFormChoiceField", "AcroFormComboBox", "AcroFormEditBox", "AcroFormListBox", "AcroFormPasswordField", "AcroFormPushButton", "AcroFormRadioButton", "AcroFormTextField", "GState", "ShadingPattern", "TilingPattern", "jsPDF"]}}}}