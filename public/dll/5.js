(window.webpackJsonp_name_=window.webpackJsonp_name_||[]).push([[5],{1424:function(t,e,r){(function(e){(function(){var r,n,i,a,s,o;"undefined"!=typeof performance&&null!==performance&&performance.now?t.exports=function(){return performance.now()}:null!=e&&e.hrtime?(t.exports=function(){return(r()-s)/1e6},n=e.hrtime,a=(r=function(){var t;return 1e9*(t=n())[0]+t[1]})(),o=1e9*e.uptime(),s=a-o):Date.now?(t.exports=function(){return Date.now()-i},i=Date.now()):(t.exports=function(){return(new Date).getTime()-i},i=(new Date).getTime())}).call(this)}).call(this,r(313))},1892:function(t,e,r){"use strict";r.r(e),function(t){r.d(e,"AElement",(function(){return Ut})),r.d(e,"AnimateColorElement",(function(){return Lt})),r.d(e,"AnimateElement",(function(){return kt})),r.d(e,"AnimateTransformElement",(function(){return Dt})),r.d(e,"BoundingBox",(function(){return ft})),r.d(e,"CB1",(function(){return k})),r.d(e,"CB2",(function(){return L})),r.d(e,"CB3",(function(){return D})),r.d(e,"CB4",(function(){return B})),r.d(e,"Canvg",(function(){return xe})),r.d(e,"CircleElement",(function(){return wt})),r.d(e,"ClipPathElement",(function(){return ae})),r.d(e,"DefsElement",(function(){return Mt})),r.d(e,"DescElement",(function(){return fe})),r.d(e,"Document",(function(){return ve})),r.d(e,"Element",(function(){return st})),r.d(e,"EllipseElement",(function(){return Tt})),r.d(e,"FeColorMatrixElement",(function(){return re})),r.d(e,"FeCompositeElement",(function(){return he})),r.d(e,"FeDropShadowElement",(function(){return oe})),r.d(e,"FeGaussianBlurElement",(function(){return ce})),r.d(e,"FeMorphologyElement",(function(){return ue})),r.d(e,"FilterElement",(function(){return se})),r.d(e,"Font",(function(){return lt})),r.d(e,"FontElement",(function(){return Bt})),r.d(e,"FontFaceElement",(function(){return jt})),r.d(e,"GElement",(function(){return Nt})),r.d(e,"GlyphElement",(function(){return vt})),r.d(e,"GradientElement",(function(){return Rt})),r.d(e,"ImageElement",(function(){return Wt})),r.d(e,"LineElement",(function(){return At})),r.d(e,"LinearGradientElement",(function(){return _t})),r.d(e,"MarkerElement",(function(){return Et})),r.d(e,"MaskElement",(function(){return ne})),r.d(e,"Matrix",(function(){return et})),r.d(e,"MissingGlyphElement",(function(){return Ft})),r.d(e,"Mouse",(function(){return Y})),r.d(e,"PSEUDO_ZERO",(function(){return R})),r.d(e,"Parser",(function(){return Z})),r.d(e,"PathElement",(function(){return dt})),r.d(e,"PathParser",(function(){return gt})),r.d(e,"PatternElement",(function(){return Ct})),r.d(e,"Point",(function(){return X})),r.d(e,"PolygonElement",(function(){return Pt})),r.d(e,"PolylineElement",(function(){return Ot})),r.d(e,"Property",(function(){return U})),r.d(e,"QB1",(function(){return j})),r.d(e,"QB2",(function(){return F})),r.d(e,"QB3",(function(){return z})),r.d(e,"RadialGradientElement",(function(){return Vt})),r.d(e,"RectElement",(function(){return St})),r.d(e,"RenderedElement",(function(){return pt})),r.d(e,"Rotate",(function(){return J})),r.d(e,"SVGElement",(function(){return bt})),r.d(e,"SVGFontLoader",(function(){return Qt})),r.d(e,"Scale",(function(){return tt})),r.d(e,"Screen",(function(){return q})),r.d(e,"Skew",(function(){return rt})),r.d(e,"SkewX",(function(){return nt})),r.d(e,"SkewY",(function(){return it})),r.d(e,"StopElement",(function(){return It})),r.d(e,"StyleElement",(function(){return $t})),r.d(e,"SymbolElement",(function(){return qt})),r.d(e,"TRefElement",(function(){return zt})),r.d(e,"TSpanElement",(function(){return mt})),r.d(e,"TextElement",(function(){return yt})),r.d(e,"TextPathElement",(function(){return Yt})),r.d(e,"TitleElement",(function(){return le})),r.d(e,"Transform",(function(){return at})),r.d(e,"Translate",(function(){return K})),r.d(e,"UnknownElement",(function(){return ot})),r.d(e,"UseElement",(function(){return Zt})),r.d(e,"ViewPort",(function(){return H})),r.d(e,"compressSpaces",(function(){return p})),r.d(e,"default",(function(){return xe})),r.d(e,"getSelectorSpecificity",(function(){return N})),r.d(e,"normalizeAttributeName",(function(){return x})),r.d(e,"normalizeColor",(function(){return S})),r.d(e,"parseExternalUrl",(function(){return b})),r.d(e,"presets",(function(){return g})),r.d(e,"toNumbers",(function(){return y})),r.d(e,"trimLeft",(function(){return d})),r.d(e,"trimRight",(function(){return v})),r.d(e,"vectorMagnitude",(function(){return _})),r.d(e,"vectorsAngle",(function(){return I})),r.d(e,"vectorsRatio",(function(){return V}));r(1988);var n=r(2024),i=r.n(n),a=(r(2025),r(2032),r(2034),r(1983),r(2039),r(2042)),s=r.n(a),o=(r(2043),r(2045),r(2046),r(67)),u=r.n(o),h=(r(2049),r(2052)),c=r.n(h),l=(r(2053),r(2054),r(2055),r(2057)),f=(r(2058),r(2060));var g=Object.freeze({__proto__:null,offscreen:function(){var{DOMParser:t}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e={window:null,ignoreAnimation:!0,ignoreMouse:!0,DOMParser:t,createCanvas:(t,e)=>new OffscreenCanvas(t,e),createImage:t=>i()((function*(){var e=yield fetch(t),r=yield e.blob();return yield createImageBitmap(r)}))()};return"undefined"==typeof DOMParser&&void 0!==t||Reflect.deleteProperty(e,"DOMParser"),e},node:function(t){var{DOMParser:e,canvas:r,fetch:n}=t;return{window:null,ignoreAnimation:!0,ignoreMouse:!0,DOMParser:e,fetch:n,createCanvas:r.createCanvas,createImage:r.loadImage}}});function p(t){return t.replace(/(?!\u3000)\s+/gm," ")}function d(t){return t.replace(/^[\n \t]+/,"")}function v(t){return t.replace(/[\n \t]+$/,"")}function y(t){return((t||"").match(/-?(\d+(?:\.\d*(?:[eE][+-]?\d+)?)?|\.\d+)(?=\D|$)/gm)||[]).map(parseFloat)}var m=/^[A-Z-]+$/;function x(t){return m.test(t)?t.toLowerCase():t}function b(t){var e=/url\(('([^']+)'|"([^"]+)"|([^'")]+))\)/.exec(t)||[];return e[2]||e[3]||e[4]}function S(t){if(!t.startsWith("rgb"))return t;var e=3;return t.replace(/\d+(\.\d+)?/g,(t,r)=>e--&&r?String(Math.round(parseFloat(t))):t)}var w=/(\[[^\]]+\])/g,T=/(#[^\s+>~.[:]+)/g,A=/(\.[^\s+>~.[:]+)/g,O=/(::[^\s+>~.[:]+|:first-line|:first-letter|:before|:after)/gi,P=/(:[\w-]+\([^)]*\))/gi,C=/(:[^\s+>~.[:]+)/g,E=/([^\s+>~.[:]+)/g;function M(t,e){var r=e.exec(t);return r?[t.replace(e," "),r.length]:[t,0]}function N(t){var e=[0,0,0],r=t.replace(/:not\(([^)]*)\)/g,"     $1 ").replace(/{[\s\S]*/gm," "),n=0;return[r,n]=M(r,w),e[1]+=n,[r,n]=M(r,T),e[0]+=n,[r,n]=M(r,A),e[1]+=n,[r,n]=M(r,O),e[2]+=n,[r,n]=M(r,P),e[1]+=n,[r,n]=M(r,C),e[1]+=n,r=r.replace(/[*\s+>~]/g," ").replace(/[#.]/g," "),[r,n]=M(r,E),e[2]+=n,e.join("")}var R=1e-8;function _(t){return Math.sqrt(Math.pow(t[0],2)+Math.pow(t[1],2))}function V(t,e){return(t[0]*e[0]+t[1]*e[1])/(_(t)*_(e))}function I(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(V(t,e))}function k(t){return t*t*t}function L(t){return 3*t*t*(1-t)}function D(t){return 3*t*(1-t)*(1-t)}function B(t){return(1-t)*(1-t)*(1-t)}function j(t){return t*t}function F(t){return 2*t*(1-t)}function z(t){return(1-t)*(1-t)}class U{constructor(t,e,r){this.document=t,this.name=e,this.value=r,this.isNormalizedColor=!1}static empty(t){return new U(t,"EMPTY","")}split(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:" ",{document:e,name:r}=this;return p(this.getString()).trim().split(t).map(t=>new U(e,r,t))}hasValue(t){var{value:e}=this;return null!==e&&""!==e&&(t||0!==e)&&void 0!==e}isString(t){var{value:e}=this,r="string"==typeof e;return r&&t?t.test(e):r}isUrlDefinition(){return this.isString(/^url\(/)}isPixels(){if(!this.hasValue())return!1;var t=this.getString();switch(!0){case t.endsWith("px"):case/^[0-9]+$/.test(t):return!0;default:return!1}}setValue(t){return this.value=t,this}getValue(t){return void 0===t||this.hasValue()?this.value:t}getNumber(t){if(!this.hasValue())return void 0===t?0:parseFloat(t);var{value:e}=this,r=parseFloat(e);return this.isString(/%$/)&&(r/=100),r}getString(t){return void 0===t||this.hasValue()?void 0===this.value?"":String(this.value):String(t)}getColor(t){var e=this.getString(t);return this.isNormalizedColor||(this.isNormalizedColor=!0,e=S(e),this.value=e),e}getDpi(){return 96}getRem(){return this.document.rootEmSize}getEm(){return this.document.emSize}getUnits(){return this.getString().replace(/[0-9.-]/g,"")}getPixels(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!this.hasValue())return 0;var[r,n]="boolean"==typeof t?[void 0,t]:[t],{viewPort:i}=this.document.screen;switch(!0){case this.isString(/vmin$/):return this.getNumber()/100*Math.min(i.computeSize("x"),i.computeSize("y"));case this.isString(/vmax$/):return this.getNumber()/100*Math.max(i.computeSize("x"),i.computeSize("y"));case this.isString(/vw$/):return this.getNumber()/100*i.computeSize("x");case this.isString(/vh$/):return this.getNumber()/100*i.computeSize("y");case this.isString(/rem$/):return this.getNumber()*this.getRem();case this.isString(/em$/):return this.getNumber()*this.getEm();case this.isString(/ex$/):return this.getNumber()*this.getEm()/2;case this.isString(/px$/):return this.getNumber();case this.isString(/pt$/):return this.getNumber()*this.getDpi()*(1/72);case this.isString(/pc$/):return 15*this.getNumber();case this.isString(/cm$/):return this.getNumber()*this.getDpi()/2.54;case this.isString(/mm$/):return this.getNumber()*this.getDpi()/25.4;case this.isString(/in$/):return this.getNumber()*this.getDpi();case this.isString(/%$/)&&n:return this.getNumber()*this.getEm();case this.isString(/%$/):return this.getNumber()*i.computeSize(r);default:var a=this.getNumber();return e&&a<1?a*i.computeSize(r):a}}getMilliseconds(){return this.hasValue()?this.isString(/ms$/)?this.getNumber():1e3*this.getNumber():0}getRadians(){if(!this.hasValue())return 0;switch(!0){case this.isString(/deg$/):return this.getNumber()*(Math.PI/180);case this.isString(/grad$/):return this.getNumber()*(Math.PI/200);case this.isString(/rad$/):return this.getNumber();default:return this.getNumber()*(Math.PI/180)}}getDefinition(){var t=this.getString(),e=/#([^)'"]+)/.exec(t);return e&&(e=e[1]),e||(e=t),this.document.definitions[e]}getFillStyleDefinition(t,e){var r=this.getDefinition();if(!r)return null;if("function"==typeof r.createGradient)return r.createGradient(this.document.ctx,t,e);if("function"==typeof r.createPattern){if(r.getHrefAttribute().hasValue()){var n=r.getAttribute("patternTransform");r=r.getHrefAttribute().getDefinition(),n.hasValue()&&r.getAttribute("patternTransform",!0).setValue(n.value)}return r.createPattern(this.document.ctx,t,e)}return null}getTextBaseline(){return this.hasValue()?U.textBaselineMapping[this.getString()]:null}addOpacity(t){for(var e=this.getColor(),r=e.length,n=0,i=0;i<r&&(","===e[i]&&n++,3!==n);i++);if(t.hasValue()&&this.isString()&&3!==n){var a=new c.a(e);a.ok&&(a.alpha=t.getNumber(),e=a.toRGBA())}return new U(this.document,this.name,e)}}U.textBaselineMapping={baseline:"alphabetic","before-edge":"top","text-before-edge":"top",middle:"middle",central:"middle","after-edge":"bottom","text-after-edge":"bottom",ideographic:"ideographic",alphabetic:"alphabetic",hanging:"hanging",mathematical:"alphabetic"};class H{constructor(){this.viewPorts=[]}clear(){this.viewPorts=[]}setCurrent(t,e){this.viewPorts.push({width:t,height:e})}removeCurrent(){this.viewPorts.pop()}getCurrent(){var{viewPorts:t}=this;return t[t.length-1]}get width(){return this.getCurrent().width}get height(){return this.getCurrent().height}computeSize(t){return"number"==typeof t?t:"x"===t?this.width:"y"===t?this.height:Math.sqrt(Math.pow(this.width,2)+Math.pow(this.height,2))/Math.sqrt(2)}}class X{constructor(t,e){this.x=t,this.y=e}static parse(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,[r=e,n=e]=y(t);return new X(r,n)}static parseScale(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,[r=e,n=r]=y(t);return new X(r,n)}static parsePath(t){for(var e=y(t),r=e.length,n=[],i=0;i<r;i+=2)n.push(new X(e[i],e[i+1]));return n}angleTo(t){return Math.atan2(t.y-this.y,t.x-this.x)}applyTransform(t){var{x:e,y:r}=this,n=e*t[0]+r*t[2]+t[4],i=e*t[1]+r*t[3]+t[5];this.x=n,this.y=i}}class Y{constructor(t){this.screen=t,this.working=!1,this.events=[],this.eventElements=[],this.onClick=this.onClick.bind(this),this.onMouseMove=this.onMouseMove.bind(this)}isWorking(){return this.working}start(){if(!this.working){var{screen:t,onClick:e,onMouseMove:r}=this,n=t.ctx.canvas;n.onclick=e,n.onmousemove=r,this.working=!0}}stop(){if(this.working){var t=this.screen.ctx.canvas;this.working=!1,t.onclick=null,t.onmousemove=null}}hasEvents(){return this.working&&this.events.length>0}runEvents(){if(this.working){var{screen:t,events:e,eventElements:r}=this,{style:n}=t.ctx.canvas;n&&(n.cursor=""),e.forEach((t,e)=>{for(var{run:n}=t,i=r[e];i;)n(i),i=i.parent}),this.events=[],this.eventElements=[]}}checkPath(t,e){if(this.working&&e){var{events:r,eventElements:n}=this;r.forEach((r,i)=>{var{x:a,y:s}=r;!n[i]&&e.isPointInPath&&e.isPointInPath(a,s)&&(n[i]=t)})}}checkBoundingBox(t,e){if(this.working&&e){var{events:r,eventElements:n}=this;r.forEach((r,i)=>{var{x:a,y:s}=r;!n[i]&&e.isPointInBox(a,s)&&(n[i]=t)})}}mapXY(t,e){for(var{window:r,ctx:n}=this.screen,i=new X(t,e),a=n.canvas;a;)i.x-=a.offsetLeft,i.y-=a.offsetTop,a=a.offsetParent;return r.scrollX&&(i.x+=r.scrollX),r.scrollY&&(i.y+=r.scrollY),i}onClick(t){var{x:e,y:r}=this.mapXY(t.clientX,t.clientY);this.events.push({type:"onclick",x:e,y:r,run(t){t.onClick&&t.onClick()}})}onMouseMove(t){var{x:e,y:r}=this.mapXY(t.clientX,t.clientY);this.events.push({type:"onmousemove",x:e,y:r,run(t){t.onMouseMove&&t.onMouseMove()}})}}var G="undefined"!=typeof window?window:null,W="undefined"!=typeof fetch?fetch.bind(void 0):null;class q{constructor(t){var{fetch:e=W,window:r=G}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.ctx=t,this.FRAMERATE=30,this.MAX_VIRTUAL_PIXELS=3e4,this.CLIENT_WIDTH=800,this.CLIENT_HEIGHT=600,this.viewPort=new H,this.mouse=new Y(this),this.animations=[],this.waits=[],this.frameDuration=0,this.isReadyLock=!1,this.isFirstRender=!0,this.intervalId=null,this.window=r,this.fetch=e}wait(t){this.waits.push(t)}ready(){return this.readyPromise?this.readyPromise:Promise.resolve()}isReady(){if(this.isReadyLock)return!0;var t=this.waits.every(t=>t());return t&&(this.waits=[],this.resolveReady&&this.resolveReady()),this.isReadyLock=t,t}setDefaults(t){t.strokeStyle="rgba(0,0,0,0)",t.lineCap="butt",t.lineJoin="miter",t.miterLimit=4}setViewBox(t){var{document:e,ctx:r,aspectRatio:n,width:i,desiredWidth:a,height:s,desiredHeight:o,minX:u=0,minY:h=0,refX:c,refY:l,clip:f=!1,clipX:g=0,clipY:d=0}=t,v=p(n).replace(/^defer\s/,""),[y,m]=v.split(" "),x=y||"xMidYMid",b=m||"meet",S=i/a,w=s/o,T=Math.min(S,w),A=Math.max(S,w),O=a,P=o;"meet"===b&&(O*=T,P*=T),"slice"===b&&(O*=A,P*=A);var C=new U(e,"refX",c),E=new U(e,"refY",l),M=C.hasValue()&&E.hasValue();if(M&&r.translate(-T*C.getPixels("x"),-T*E.getPixels("y")),f){var N=T*g,R=T*d;r.beginPath(),r.moveTo(N,R),r.lineTo(i,R),r.lineTo(i,s),r.lineTo(N,s),r.closePath(),r.clip()}if(!M){var _="meet"===b&&T===w,V="slice"===b&&A===w,I="meet"===b&&T===S,k="slice"===b&&A===S;x.startsWith("xMid")&&(_||V)&&r.translate(i/2-O/2,0),x.endsWith("YMid")&&(I||k)&&r.translate(0,s/2-P/2),x.startsWith("xMax")&&(_||V)&&r.translate(i-O,0),x.endsWith("YMax")&&(I||k)&&r.translate(0,s-P)}switch(!0){case"none"===x:r.scale(S,w);break;case"meet"===b:r.scale(T,T);break;case"slice"===b:r.scale(A,A)}r.translate(-u,-h)}start(t){var{enableRedraw:e=!1,ignoreMouse:r=!1,ignoreAnimation:n=!1,ignoreDimensions:i=!1,ignoreClear:a=!1,forceRedraw:s,scaleWidth:o,scaleHeight:h,offsetX:c,offsetY:l}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{FRAMERATE:f,mouse:g}=this,p=1e3/f;if(this.frameDuration=p,this.readyPromise=new Promise(t=>{this.resolveReady=t}),this.isReady()&&this.render(t,i,a,o,h,c,l),e){var d=Date.now(),v=d,y=0,m=()=>{d=Date.now(),(y=d-v)>=p&&(v=d-y%p,this.shouldUpdate(n,s)&&(this.render(t,i,a,o,h,c,l),g.runEvents())),this.intervalId=u()(m)};r||g.start(),this.intervalId=u()(m)}}stop(){this.intervalId&&(u.a.cancel(this.intervalId),this.intervalId=null),this.mouse.stop()}shouldUpdate(t,e){if(!t){var{frameDuration:r}=this;if(this.animations.reduce((t,e)=>e.update(r)||t,!1))return!0}return!("function"!=typeof e||!e())||(!(this.isReadyLock||!this.isReady())||!!this.mouse.hasEvents())}render(t,e,r,n,i,a,s){var{CLIENT_WIDTH:o,CLIENT_HEIGHT:u,viewPort:h,ctx:c,isFirstRender:l}=this,f=c.canvas;h.clear(),f.width&&f.height?h.setCurrent(f.width,f.height):h.setCurrent(o,u);var g=t.getStyle("width"),p=t.getStyle("height");!e&&(l||"number"!=typeof n&&"number"!=typeof i)&&(g.hasValue()&&(f.width=g.getPixels("x"),f.style&&(f.style.width="".concat(f.width,"px"))),p.hasValue()&&(f.height=p.getPixels("y"),f.style&&(f.style.height="".concat(f.height,"px"))));var d=f.clientWidth||f.width,v=f.clientHeight||f.height;if(e&&g.hasValue()&&p.hasValue()&&(d=g.getPixels("x"),v=p.getPixels("y")),h.setCurrent(d,v),"number"==typeof a&&t.getAttribute("x",!0).setValue(a),"number"==typeof s&&t.getAttribute("y",!0).setValue(s),"number"==typeof n||"number"==typeof i){var m=y(t.getAttribute("viewBox").getString()),x=0,b=0;if("number"==typeof n){var S=t.getStyle("width");S.hasValue()?x=S.getPixels("x")/n:isNaN(m[2])||(x=m[2]/n)}if("number"==typeof i){var w=t.getStyle("height");w.hasValue()?b=w.getPixels("y")/i:isNaN(m[3])||(b=m[3]/i)}x||(x=b),b||(b=x),t.getAttribute("width",!0).setValue(n),t.getAttribute("height",!0).setValue(i);var T=t.getStyle("transform",!0,!0);T.setValue("".concat(T.getString()," scale(").concat(1/x,", ").concat(1/b,")"))}r||c.clearRect(0,0,d,v),t.render(c),l&&(this.isFirstRender=!1)}}q.defaultWindow=G,q.defaultFetch=W;var{defaultFetch:Q}=q,$="undefined"!=typeof DOMParser?DOMParser:null;class Z{constructor(){var{fetch:t=Q,DOMParser:e=$}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.fetch=t,this.DOMParser=e}parse(t){var e=this;return i()((function*(){return t.startsWith("<")?e.parseFromString(t):e.load(t)}))()}parseFromString(t){var e=new this.DOMParser;try{return this.checkDocument(e.parseFromString(t,"image/svg+xml"))}catch(r){return this.checkDocument(e.parseFromString(t,"text/xml"))}}checkDocument(t){var e=t.getElementsByTagName("parsererror")[0];if(e)throw new Error(e.textContent);return t}load(t){var e=this;return i()((function*(){var r=yield e.fetch(t),n=yield r.text();return e.parseFromString(n)}))()}}class K{constructor(t,e){this.type="translate",this.point=null,this.point=X.parse(e)}apply(t){var{x:e,y:r}=this.point;t.translate(e||0,r||0)}unapply(t){var{x:e,y:r}=this.point;t.translate(-1*e||0,-1*r||0)}applyToPoint(t){var{x:e,y:r}=this.point;t.applyTransform([1,0,0,1,e||0,r||0])}}class J{constructor(t,e,r){this.type="rotate",this.angle=null,this.originX=null,this.originY=null,this.cx=0,this.cy=0;var n=y(e);this.angle=new U(t,"angle",n[0]),this.originX=r[0],this.originY=r[1],this.cx=n[1]||0,this.cy=n[2]||0}apply(t){var{cx:e,cy:r,originX:n,originY:i,angle:a}=this,s=e+n.getPixels("x"),o=r+i.getPixels("y");t.translate(s,o),t.rotate(a.getRadians()),t.translate(-s,-o)}unapply(t){var{cx:e,cy:r,originX:n,originY:i,angle:a}=this,s=e+n.getPixels("x"),o=r+i.getPixels("y");t.translate(s,o),t.rotate(-1*a.getRadians()),t.translate(-s,-o)}applyToPoint(t){var{cx:e,cy:r,angle:n}=this,i=n.getRadians();t.applyTransform([1,0,0,1,e||0,r||0]),t.applyTransform([Math.cos(i),Math.sin(i),-Math.sin(i),Math.cos(i),0,0]),t.applyTransform([1,0,0,1,-e||0,-r||0])}}class tt{constructor(t,e,r){this.type="scale",this.scale=null,this.originX=null,this.originY=null;var n=X.parseScale(e);0!==n.x&&0!==n.y||(n.x=R,n.y=R),this.scale=n,this.originX=r[0],this.originY=r[1]}apply(t){var{scale:{x:e,y:r},originX:n,originY:i}=this,a=n.getPixels("x"),s=i.getPixels("y");t.translate(a,s),t.scale(e,r||e),t.translate(-a,-s)}unapply(t){var{scale:{x:e,y:r},originX:n,originY:i}=this,a=n.getPixels("x"),s=i.getPixels("y");t.translate(a,s),t.scale(1/e,1/r||e),t.translate(-a,-s)}applyToPoint(t){var{x:e,y:r}=this.scale;t.applyTransform([e||0,0,0,r||0,0,0])}}class et{constructor(t,e,r){this.type="matrix",this.matrix=[],this.originX=null,this.originY=null,this.matrix=y(e),this.originX=r[0],this.originY=r[1]}apply(t){var{originX:e,originY:r,matrix:n}=this,i=e.getPixels("x"),a=r.getPixels("y");t.translate(i,a),t.transform(n[0],n[1],n[2],n[3],n[4],n[5]),t.translate(-i,-a)}unapply(t){var{originX:e,originY:r,matrix:n}=this,i=n[0],a=n[2],s=n[4],o=n[1],u=n[3],h=n[5],c=1/(i*(1*u-0*h)-a*(1*o-0*h)+s*(0*o-0*u)),l=e.getPixels("x"),f=r.getPixels("y");t.translate(l,f),t.transform(c*(1*u-0*h),c*(0*h-1*o),c*(0*s-1*a),c*(1*i-0*s),c*(a*h-s*u),c*(s*o-i*h)),t.translate(-l,-f)}applyToPoint(t){t.applyTransform(this.matrix)}}class rt extends et{constructor(t,e,r){super(t,e,r),this.type="skew",this.angle=null,this.angle=new U(t,"angle",e)}}class nt extends rt{constructor(t,e,r){super(t,e,r),this.type="skewX",this.matrix=[1,0,Math.tan(this.angle.getRadians()),1,0,0]}}class it extends rt{constructor(t,e,r){super(t,e,r),this.type="skewY",this.matrix=[1,Math.tan(this.angle.getRadians()),0,1,0,0]}}class at{constructor(t,e,r){this.document=t,this.transforms=[],function(t){return p(t).trim().replace(/\)([a-zA-Z])/g,") $1").replace(/\)(\s?,\s?)/g,") ").split(/\s(?=[a-z])/)}(e).forEach(t=>{if("none"!==t){var[e,n]=function(t){var[e,r]=t.split("(");return[e.trim(),r.trim().replace(")","")]}(t),i=at.transformTypes[e];void 0!==i&&this.transforms.push(new i(this.document,n,r))}})}static fromElement(t,e){var r=e.getStyle("transform",!1,!0),[n,i=n]=e.getStyle("transform-origin",!1,!0).split(),a=[n,i];return r.hasValue()?new at(t,r.getString(),a):null}apply(t){for(var{transforms:e}=this,r=e.length,n=0;n<r;n++)e[n].apply(t)}unapply(t){for(var{transforms:e}=this,r=e.length-1;r>=0;r--)e[r].unapply(t)}applyToPoint(t){for(var{transforms:e}=this,r=e.length,n=0;n<r;n++)e[n].applyToPoint(t)}}at.transformTypes={translate:K,rotate:J,scale:tt,matrix:et,skewX:nt,skewY:it};class st{constructor(t,e){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(this.document=t,this.node=e,this.captureTextNodes=r,this.attributes={},this.styles={},this.stylesSpecificity={},this.animationFrozen=!1,this.animationFrozenValue="",this.parent=null,this.children=[],e&&1===e.nodeType){if(Array.from(e.attributes).forEach(e=>{var r=x(e.nodeName);this.attributes[r]=new U(t,r,e.value)}),this.addStylesFromStyleDefinition(),this.getAttribute("style").hasValue())this.getAttribute("style").getString().split(";").map(t=>t.trim()).forEach(e=>{if(e){var[r,n]=e.split(":").map(t=>t.trim());this.styles[r]=new U(t,r,n)}});var{definitions:n}=t,i=this.getAttribute("id");i.hasValue()&&(n[i.getString()]||(n[i.getString()]=this)),Array.from(e.childNodes).forEach(e=>{if(1===e.nodeType)this.addChild(e);else if(r&&(3===e.nodeType||4===e.nodeType)){var n=t.createTextNode(e);n.getText().length>0&&this.addChild(n)}})}}getAttribute(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=this.attributes[t];if(!r&&e){var n=new U(this.document,t,"");return this.attributes[t]=n,n}return r||U.empty(this.document)}getHrefAttribute(){for(var t in this.attributes)if("href"===t||t.endsWith(":href"))return this.attributes[t];return U.empty(this.document)}getStyle(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],n=this.styles[t];if(n)return n;var i=this.getAttribute(t);if(null!=i&&i.hasValue())return this.styles[t]=i,i;if(!r){var{parent:a}=this;if(a){var s=a.getStyle(t);if(null!=s&&s.hasValue())return s}}if(e){var o=new U(this.document,t,"");return this.styles[t]=o,o}return n||U.empty(this.document)}render(t){if("none"!==this.getStyle("display").getString()&&"hidden"!==this.getStyle("visibility").getString()){if(t.save(),this.getStyle("mask").hasValue()){var e=this.getStyle("mask").getDefinition();e&&(this.applyEffects(t),e.apply(t,this))}else if("none"!==this.getStyle("filter").getValue("none")){var r=this.getStyle("filter").getDefinition();r&&(this.applyEffects(t),r.apply(t,this))}else this.setContext(t),this.renderChildren(t),this.clearContext(t);t.restore()}}setContext(t){}applyEffects(t){var e=at.fromElement(this.document,this);e&&e.apply(t);var r=this.getStyle("clip-path",!1,!0);if(r.hasValue()){var n=r.getDefinition();n&&n.apply(t)}}clearContext(t){}renderChildren(t){this.children.forEach(e=>{e.render(t)})}addChild(t){var e=t instanceof st?t:this.document.createElement(t);e.parent=this,st.ignoreChildTypes.includes(e.type)||this.children.push(e)}matchesSelector(t){var e,{node:r}=this;if("function"==typeof r.matches)return r.matches(t);var n=null===(e=r.getAttribute)||void 0===e?void 0:e.call(r,"class");return!(!n||""===n)&&n.split(" ").some(e=>".".concat(e)===t)}addStylesFromStyleDefinition(){var{styles:t,stylesSpecificity:e}=this.document;for(var r in t)if(!r.startsWith("@")&&this.matchesSelector(r)){var n=t[r],i=e[r];if(n)for(var a in n){var s=this.stylesSpecificity[a];void 0===s&&(s="000"),i>=s&&(this.styles[a]=n[a],this.stylesSpecificity[a]=i)}}}removeStyles(t,e){return e.reduce((e,r)=>{var n=t.getStyle(r);if(!n.hasValue())return e;var i=n.getString();return n.setValue(""),[...e,[r,i]]},[])}restoreStyles(t,e){e.forEach(e=>{var[r,n]=e;t.getStyle(r,!0).setValue(n)})}isFirstChild(){var t;return 0===(null===(t=this.parent)||void 0===t?void 0:t.children.indexOf(this))}}st.ignoreChildTypes=["title"];class ot extends st{constructor(t,e,r){super(t,e,r)}}function ut(t){var e=t.trim();return/^('|")/.test(e)?e:'"'.concat(e,'"')}function ht(t){if(!t)return"";var e=t.trim().toLowerCase();switch(e){case"normal":case"italic":case"oblique":case"inherit":case"initial":case"unset":return e;default:return/^oblique\s+(-|)\d+deg$/.test(e)?e:""}}function ct(t){if(!t)return"";var e=t.trim().toLowerCase();switch(e){case"normal":case"bold":case"lighter":case"bolder":case"inherit":case"initial":case"unset":return e;default:return/^[\d.]+$/.test(e)?e:""}}class lt{constructor(t,e,r,n,i,a){var s=a?"string"==typeof a?lt.parse(a):a:{};this.fontFamily=i||s.fontFamily,this.fontSize=n||s.fontSize,this.fontStyle=t||s.fontStyle,this.fontWeight=r||s.fontWeight,this.fontVariant=e||s.fontVariant}static parse(){var t=arguments.length>1?arguments[1]:void 0,e="",r="",n="",i="",a="",s=p(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"").trim().split(" "),o={fontSize:!1,fontStyle:!1,fontWeight:!1,fontVariant:!1};return s.forEach(t=>{switch(!0){case!o.fontStyle&&lt.styles.includes(t):"inherit"!==t&&(e=t),o.fontStyle=!0;break;case!o.fontVariant&&lt.variants.includes(t):"inherit"!==t&&(r=t),o.fontStyle=!0,o.fontVariant=!0;break;case!o.fontWeight&&lt.weights.includes(t):"inherit"!==t&&(n=t),o.fontStyle=!0,o.fontVariant=!0,o.fontWeight=!0;break;case!o.fontSize:"inherit"!==t&&([i]=t.split("/")),o.fontStyle=!0,o.fontVariant=!0,o.fontWeight=!0,o.fontSize=!0;break;default:"inherit"!==t&&(a+=t)}}),new lt(e,r,n,i,a,t)}toString(){return[ht(this.fontStyle),this.fontVariant,ct(this.fontWeight),this.fontSize,(e=this.fontFamily,void 0===t?e:e.trim().split(",").map(ut).join(","))].join(" ").trim();var e}}lt.styles="normal|italic|oblique|inherit",lt.variants="normal|small-caps|inherit",lt.weights="normal|bold|bolder|lighter|100|200|300|400|500|600|700|800|900|inherit";class ft{constructor(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Number.NaN,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Number.NaN,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Number.NaN,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:Number.NaN;this.x1=t,this.y1=e,this.x2=r,this.y2=n,this.addPoint(t,e),this.addPoint(r,n)}get x(){return this.x1}get y(){return this.y1}get width(){return this.x2-this.x1}get height(){return this.y2-this.y1}addPoint(t,e){void 0!==t&&((isNaN(this.x1)||isNaN(this.x2))&&(this.x1=t,this.x2=t),t<this.x1&&(this.x1=t),t>this.x2&&(this.x2=t)),void 0!==e&&((isNaN(this.y1)||isNaN(this.y2))&&(this.y1=e,this.y2=e),e<this.y1&&(this.y1=e),e>this.y2&&(this.y2=e))}addX(t){this.addPoint(t,null)}addY(t){this.addPoint(null,t)}addBoundingBox(t){if(t){var{x1:e,y1:r,x2:n,y2:i}=t;this.addPoint(e,r),this.addPoint(n,i)}}sumCubic(t,e,r,n,i){return Math.pow(1-t,3)*e+3*Math.pow(1-t,2)*t*r+3*(1-t)*Math.pow(t,2)*n+Math.pow(t,3)*i}bezierCurveAdd(t,e,r,n,i){var a=6*e-12*r+6*n,s=-3*e+9*r-9*n+3*i,o=3*r-3*e;if(0!==s){var u=Math.pow(a,2)-4*o*s;if(!(u<0)){var h=(-a+Math.sqrt(u))/(2*s);0<h&&h<1&&(t?this.addX(this.sumCubic(h,e,r,n,i)):this.addY(this.sumCubic(h,e,r,n,i)));var c=(-a-Math.sqrt(u))/(2*s);0<c&&c<1&&(t?this.addX(this.sumCubic(c,e,r,n,i)):this.addY(this.sumCubic(c,e,r,n,i)))}}else{if(0===a)return;var l=-o/a;0<l&&l<1&&(t?this.addX(this.sumCubic(l,e,r,n,i)):this.addY(this.sumCubic(l,e,r,n,i)))}}addBezierCurve(t,e,r,n,i,a,s,o){this.addPoint(t,e),this.addPoint(s,o),this.bezierCurveAdd(!0,t,r,i,s),this.bezierCurveAdd(!1,e,n,a,o)}addQuadraticCurve(t,e,r,n,i,a){var s=t+2/3*(r-t),o=e+2/3*(n-e),u=s+1/3*(i-t),h=o+1/3*(a-e);this.addBezierCurve(t,e,s,u,o,h,i,a)}isPointInBox(t,e){var{x1:r,y1:n,x2:i,y2:a}=this;return r<=t&&t<=i&&n<=e&&e<=a}}class gt extends l.SVGPathData{constructor(t){super(t.replace(/([+\-.])\s+/gm,"$1").replace(/[^MmZzLlHhVvCcSsQqTtAae\d\s.,+-].*/g,"")),this.control=null,this.start=null,this.current=null,this.command=null,this.commands=this.commands,this.i=-1,this.previousCommand=null,this.points=[],this.angles=[]}reset(){this.i=-1,this.command=null,this.previousCommand=null,this.start=new X(0,0),this.control=new X(0,0),this.current=new X(0,0),this.points=[],this.angles=[]}isEnd(){var{i:t,commands:e}=this;return t>=e.length-1}next(){var t=this.commands[++this.i];return this.previousCommand=this.command,this.command=t,t}getPoint(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"x",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"y",r=new X(this.command[t],this.command[e]);return this.makeAbsolute(r)}getAsControlPoint(t,e){var r=this.getPoint(t,e);return this.control=r,r}getAsCurrentPoint(t,e){var r=this.getPoint(t,e);return this.current=r,r}getReflectedControlPoint(){var t=this.previousCommand.type;if(t!==l.SVGPathData.CURVE_TO&&t!==l.SVGPathData.SMOOTH_CURVE_TO&&t!==l.SVGPathData.QUAD_TO&&t!==l.SVGPathData.SMOOTH_QUAD_TO)return this.current;var{current:{x:e,y:r},control:{x:n,y:i}}=this;return new X(2*e-n,2*r-i)}makeAbsolute(t){if(this.command.relative){var{x:e,y:r}=this.current;t.x+=e,t.y+=r}return t}addMarker(t,e,r){var{points:n,angles:i}=this;r&&i.length>0&&!i[i.length-1]&&(i[i.length-1]=n[n.length-1].angleTo(r)),this.addMarkerAngle(t,e?e.angleTo(t):null)}addMarkerAngle(t,e){this.points.push(t),this.angles.push(e)}getMarkerPoints(){return this.points}getMarkerAngles(){for(var{angles:t}=this,e=t.length,r=0;r<e;r++)if(!t[r])for(var n=r+1;n<e;n++)if(t[n]){t[r]=t[n];break}return t}}class pt extends st{constructor(){super(...arguments),this.modifiedEmSizeStack=!1}calculateOpacity(){for(var t=1,e=this;e;){var r=e.getStyle("opacity",!1,!0);r.hasValue(!0)&&(t*=r.getNumber()),e=e.parent}return t}setContext(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e){var r=this.getStyle("fill"),n=this.getStyle("fill-opacity"),i=this.getStyle("stroke"),a=this.getStyle("stroke-opacity");if(r.isUrlDefinition()){var s=r.getFillStyleDefinition(this,n);s&&(t.fillStyle=s)}else if(r.hasValue()){"currentColor"===r.getString()&&r.setValue(this.getStyle("color").getColor());var o=r.getColor();"inherit"!==o&&(t.fillStyle="none"===o?"rgba(0,0,0,0)":o)}if(n.hasValue()){var u=new U(this.document,"fill",t.fillStyle).addOpacity(n).getColor();t.fillStyle=u}if(i.isUrlDefinition()){var h=i.getFillStyleDefinition(this,a);h&&(t.strokeStyle=h)}else if(i.hasValue()){"currentColor"===i.getString()&&i.setValue(this.getStyle("color").getColor());var c=i.getString();"inherit"!==c&&(t.strokeStyle="none"===c?"rgba(0,0,0,0)":c)}if(a.hasValue()){var l=new U(this.document,"stroke",t.strokeStyle).addOpacity(a).getString();t.strokeStyle=l}var f=this.getStyle("stroke-width");if(f.hasValue()){var g=f.getPixels();t.lineWidth=g||R}var p=this.getStyle("stroke-linecap"),d=this.getStyle("stroke-linejoin"),v=this.getStyle("stroke-miterlimit"),m=this.getStyle("stroke-dasharray"),x=this.getStyle("stroke-dashoffset");if(p.hasValue()&&(t.lineCap=p.getString()),d.hasValue()&&(t.lineJoin=d.getString()),v.hasValue()&&(t.miterLimit=v.getNumber()),m.hasValue()&&"none"!==m.getString()){var b=y(m.getString());void 0!==t.setLineDash?t.setLineDash(b):void 0!==t.webkitLineDash?t.webkitLineDash=b:void 0===t.mozDash||1===b.length&&0===b[0]||(t.mozDash=b);var S=x.getPixels();void 0!==t.lineDashOffset?t.lineDashOffset=S:void 0!==t.webkitLineDashOffset?t.webkitLineDashOffset=S:void 0!==t.mozDashOffset&&(t.mozDashOffset=S)}}if(this.modifiedEmSizeStack=!1,void 0!==t.font){var w=this.getStyle("font"),T=this.getStyle("font-style"),A=this.getStyle("font-variant"),O=this.getStyle("font-weight"),P=this.getStyle("font-size"),C=this.getStyle("font-family"),E=new lt(T.getString(),A.getString(),O.getString(),P.hasValue()?"".concat(P.getPixels(!0),"px"):"",C.getString(),lt.parse(w.getString(),t.font));T.setValue(E.fontStyle),A.setValue(E.fontVariant),O.setValue(E.fontWeight),P.setValue(E.fontSize),C.setValue(E.fontFamily),t.font=E.toString(),P.isPixels()&&(this.document.emSize=P.getPixels(),this.modifiedEmSizeStack=!0)}e||(this.applyEffects(t),t.globalAlpha=this.calculateOpacity())}clearContext(t){super.clearContext(t),this.modifiedEmSizeStack&&this.document.popEmSize()}}class dt extends pt{constructor(t,e,r){super(t,e,r),this.type="path",this.pathParser=null,this.pathParser=new gt(this.getAttribute("d").getString())}path(t){var{pathParser:e}=this,r=new ft;for(e.reset(),t&&t.beginPath();!e.isEnd();)switch(e.next().type){case gt.MOVE_TO:this.pathM(t,r);break;case gt.LINE_TO:this.pathL(t,r);break;case gt.HORIZ_LINE_TO:this.pathH(t,r);break;case gt.VERT_LINE_TO:this.pathV(t,r);break;case gt.CURVE_TO:this.pathC(t,r);break;case gt.SMOOTH_CURVE_TO:this.pathS(t,r);break;case gt.QUAD_TO:this.pathQ(t,r);break;case gt.SMOOTH_QUAD_TO:this.pathT(t,r);break;case gt.ARC:this.pathA(t,r);break;case gt.CLOSE_PATH:this.pathZ(t,r)}return r}getBoundingBox(t){return this.path()}getMarkers(){var{pathParser:t}=this,e=t.getMarkerPoints(),r=t.getMarkerAngles();return e.map((t,e)=>[t,r[e]])}renderChildren(t){this.path(t),this.document.screen.mouse.checkPath(this,t);var e=this.getStyle("fill-rule");""!==t.fillStyle&&("inherit"!==e.getString("inherit")?t.fill(e.getString()):t.fill()),""!==t.strokeStyle&&("non-scaling-stroke"===this.getAttribute("vector-effect").getString()?(t.save(),t.setTransform(1,0,0,1,0,0),t.stroke(),t.restore()):t.stroke());var r=this.getMarkers();if(r){var n=r.length-1,i=this.getStyle("marker-start"),a=this.getStyle("marker-mid"),s=this.getStyle("marker-end");if(i.isUrlDefinition()){var o=i.getDefinition(),[u,h]=r[0];o.render(t,u,h)}if(a.isUrlDefinition())for(var c=a.getDefinition(),l=1;l<n;l++){var[f,g]=r[l];c.render(t,f,g)}if(s.isUrlDefinition()){var p=s.getDefinition(),[d,v]=r[n];p.render(t,d,v)}}}static pathM(t){var e=t.getAsCurrentPoint();return t.start=t.current,{point:e}}pathM(t,e){var{pathParser:r}=this,{point:n}=dt.pathM(r),{x:i,y:a}=n;r.addMarker(n),e.addPoint(i,a),t&&t.moveTo(i,a)}static pathL(t){var{current:e}=t;return{current:e,point:t.getAsCurrentPoint()}}pathL(t,e){var{pathParser:r}=this,{current:n,point:i}=dt.pathL(r),{x:a,y:s}=i;r.addMarker(i,n),e.addPoint(a,s),t&&t.lineTo(a,s)}static pathH(t){var{current:e,command:r}=t,n=new X((r.relative?e.x:0)+r.x,e.y);return t.current=n,{current:e,point:n}}pathH(t,e){var{pathParser:r}=this,{current:n,point:i}=dt.pathH(r),{x:a,y:s}=i;r.addMarker(i,n),e.addPoint(a,s),t&&t.lineTo(a,s)}static pathV(t){var{current:e,command:r}=t,n=new X(e.x,(r.relative?e.y:0)+r.y);return t.current=n,{current:e,point:n}}pathV(t,e){var{pathParser:r}=this,{current:n,point:i}=dt.pathV(r),{x:a,y:s}=i;r.addMarker(i,n),e.addPoint(a,s),t&&t.lineTo(a,s)}static pathC(t){var{current:e}=t;return{current:e,point:t.getPoint("x1","y1"),controlPoint:t.getAsControlPoint("x2","y2"),currentPoint:t.getAsCurrentPoint()}}pathC(t,e){var{pathParser:r}=this,{current:n,point:i,controlPoint:a,currentPoint:s}=dt.pathC(r);r.addMarker(s,a,i),e.addBezierCurve(n.x,n.y,i.x,i.y,a.x,a.y,s.x,s.y),t&&t.bezierCurveTo(i.x,i.y,a.x,a.y,s.x,s.y)}static pathS(t){var{current:e}=t;return{current:e,point:t.getReflectedControlPoint(),controlPoint:t.getAsControlPoint("x2","y2"),currentPoint:t.getAsCurrentPoint()}}pathS(t,e){var{pathParser:r}=this,{current:n,point:i,controlPoint:a,currentPoint:s}=dt.pathS(r);r.addMarker(s,a,i),e.addBezierCurve(n.x,n.y,i.x,i.y,a.x,a.y,s.x,s.y),t&&t.bezierCurveTo(i.x,i.y,a.x,a.y,s.x,s.y)}static pathQ(t){var{current:e}=t;return{current:e,controlPoint:t.getAsControlPoint("x1","y1"),currentPoint:t.getAsCurrentPoint()}}pathQ(t,e){var{pathParser:r}=this,{current:n,controlPoint:i,currentPoint:a}=dt.pathQ(r);r.addMarker(a,i,i),e.addQuadraticCurve(n.x,n.y,i.x,i.y,a.x,a.y),t&&t.quadraticCurveTo(i.x,i.y,a.x,a.y)}static pathT(t){var{current:e}=t,r=t.getReflectedControlPoint();return t.control=r,{current:e,controlPoint:r,currentPoint:t.getAsCurrentPoint()}}pathT(t,e){var{pathParser:r}=this,{current:n,controlPoint:i,currentPoint:a}=dt.pathT(r);r.addMarker(a,i,i),e.addQuadraticCurve(n.x,n.y,i.x,i.y,a.x,a.y),t&&t.quadraticCurveTo(i.x,i.y,a.x,a.y)}static pathA(t){var{current:e,command:r}=t,{rX:n,rY:i,xRot:a,lArcFlag:s,sweepFlag:o}=r,u=a*(Math.PI/180),h=t.getAsCurrentPoint(),c=new X(Math.cos(u)*(e.x-h.x)/2+Math.sin(u)*(e.y-h.y)/2,-Math.sin(u)*(e.x-h.x)/2+Math.cos(u)*(e.y-h.y)/2),l=Math.pow(c.x,2)/Math.pow(n,2)+Math.pow(c.y,2)/Math.pow(i,2);l>1&&(n*=Math.sqrt(l),i*=Math.sqrt(l));var f=(s===o?-1:1)*Math.sqrt((Math.pow(n,2)*Math.pow(i,2)-Math.pow(n,2)*Math.pow(c.y,2)-Math.pow(i,2)*Math.pow(c.x,2))/(Math.pow(n,2)*Math.pow(c.y,2)+Math.pow(i,2)*Math.pow(c.x,2)));isNaN(f)&&(f=0);var g=new X(f*n*c.y/i,f*-i*c.x/n),p=new X((e.x+h.x)/2+Math.cos(u)*g.x-Math.sin(u)*g.y,(e.y+h.y)/2+Math.sin(u)*g.x+Math.cos(u)*g.y),d=I([1,0],[(c.x-g.x)/n,(c.y-g.y)/i]),v=[(c.x-g.x)/n,(c.y-g.y)/i],y=[(-c.x-g.x)/n,(-c.y-g.y)/i],m=I(v,y);return V(v,y)<=-1&&(m=Math.PI),V(v,y)>=1&&(m=0),{currentPoint:h,rX:n,rY:i,sweepFlag:o,xAxisRotation:u,centp:p,a1:d,ad:m}}pathA(t,e){var{pathParser:r}=this,{currentPoint:n,rX:i,rY:a,sweepFlag:s,xAxisRotation:o,centp:u,a1:h,ad:c}=dt.pathA(r),l=1-s?1:-1,f=h+l*(c/2),g=new X(u.x+i*Math.cos(f),u.y+a*Math.sin(f));if(r.addMarkerAngle(g,f-l*Math.PI/2),r.addMarkerAngle(n,f-l*Math.PI),e.addPoint(n.x,n.y),t&&!isNaN(h)&&!isNaN(c)){var p=i>a?i:a,d=i>a?1:i/a,v=i>a?a/i:1;t.translate(u.x,u.y),t.rotate(o),t.scale(d,v),t.arc(0,0,p,h,h+c,Boolean(1-s)),t.scale(1/d,1/v),t.rotate(-o),t.translate(-u.x,-u.y)}}static pathZ(t){t.current=t.start}pathZ(t,e){dt.pathZ(this.pathParser),t&&e.x1!==e.x2&&e.y1!==e.y2&&t.closePath()}}class vt extends dt{constructor(t,e,r){super(t,e,r),this.type="glyph",this.horizAdvX=this.getAttribute("horiz-adv-x").getNumber(),this.unicode=this.getAttribute("unicode").getString(),this.arabicForm=this.getAttribute("arabic-form").getString()}}class yt extends pt{constructor(t,e,r){super(t,e,new.target===yt||r),this.type="text",this.x=0,this.y=0,this.measureCache=-1}setContext(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];super.setContext(t,e);var r=this.getStyle("dominant-baseline").getTextBaseline()||this.getStyle("alignment-baseline").getTextBaseline();r&&(t.textBaseline=r)}initializeCoordinates(){this.x=0,this.y=0,this.leafTexts=[],this.textChunkStart=0,this.minX=Number.POSITIVE_INFINITY,this.maxX=Number.NEGATIVE_INFINITY}getBoundingBox(t){if("text"!==this.type)return this.getTElementBoundingBox(t);this.initializeCoordinates(),this.adjustChildCoordinatesRecursive(t);var e=null;return this.children.forEach((r,n)=>{var i=this.getChildBoundingBox(t,this,this,n);e?e.addBoundingBox(i):e=i}),e}getFontSize(){var{document:t,parent:e}=this,r=lt.parse(t.ctx.font).fontSize;return e.getStyle("font-size").getNumber(r)}getTElementBoundingBox(t){var e=this.getFontSize();return new ft(this.x,this.y-e,this.x+this.measureText(t),this.y)}getGlyph(t,e,r){var n=e[r],i=null;if(t.isArabic){var a=e.length,s=e[r-1],o=e[r+1],u="isolated";if((0===r||" "===s)&&r<a-1&&" "!==o&&(u="terminal"),r>0&&" "!==s&&r<a-1&&" "!==o&&(u="medial"),r>0&&" "!==s&&(r===a-1||" "===o)&&(u="initial"),void 0!==t.glyphs[n]){var h=t.glyphs[n];i=h instanceof vt?h:h[u]}}else i=t.glyphs[n];return i||(i=t.missingGlyph),i}getText(){return""}getTextFromNode(t){var e=t||this.node,r=Array.from(e.parentNode.childNodes),n=r.indexOf(e),i=r.length-1,a=p(e.textContent||"");return 0===n&&(a=d(a)),n===i&&(a=v(a)),a}renderChildren(t){if("text"===this.type){this.initializeCoordinates(),this.adjustChildCoordinatesRecursive(t),this.children.forEach((e,r)=>{this.renderChild(t,this,this,r)});var{mouse:e}=this.document.screen;e.isWorking()&&e.checkBoundingBox(this,this.getBoundingBox(t))}else this.renderTElementChildren(t)}renderTElementChildren(t){var{document:e,parent:r}=this,n=this.getText(),i=r.getStyle("font-family").getDefinition();if(i)for(var{unitsPerEm:a}=i.fontFace,s=lt.parse(e.ctx.font),o=r.getStyle("font-size").getNumber(s.fontSize),u=r.getStyle("font-style").getString(s.fontStyle),h=o/a,c=i.isRTL?n.split("").reverse().join(""):n,l=y(r.getAttribute("dx").getString()),f=c.length,g=0;g<f;g++){var p=this.getGlyph(i,c,g);t.translate(this.x,this.y),t.scale(h,-h);var d=t.lineWidth;t.lineWidth=t.lineWidth*a/o,"italic"===u&&t.transform(1,0,.4,1,0,0),p.render(t),"italic"===u&&t.transform(1,0,-.4,1,0,0),t.lineWidth=d,t.scale(1/h,-1/h),t.translate(-this.x,-this.y),this.x+=o*(p.horizAdvX||i.horizAdvX)/a,void 0===l[g]||isNaN(l[g])||(this.x+=l[g])}else{var{x:v,y:m}=this;t.fillStyle&&t.fillText(n,v,m),t.strokeStyle&&t.strokeText(n,v,m)}}applyAnchoring(){if(!(this.textChunkStart>=this.leafTexts.length)){var t=this.leafTexts[this.textChunkStart],e=t.getStyle("text-anchor").getString("start"),r=0;r="start"===e?t.x-this.minX:"end"===e?t.x-this.maxX:t.x-(this.minX+this.maxX)/2;for(var n=this.textChunkStart;n<this.leafTexts.length;n++)this.leafTexts[n].x+=r;this.minX=Number.POSITIVE_INFINITY,this.maxX=Number.NEGATIVE_INFINITY,this.textChunkStart=this.leafTexts.length}}adjustChildCoordinatesRecursive(t){this.children.forEach((e,r)=>{this.adjustChildCoordinatesRecursiveCore(t,this,this,r)}),this.applyAnchoring()}adjustChildCoordinatesRecursiveCore(t,e,r,n){var i=r.children[n];i.children.length>0?i.children.forEach((r,n)=>{e.adjustChildCoordinatesRecursiveCore(t,e,i,n)}):this.adjustChildCoordinates(t,e,r,n)}adjustChildCoordinates(t,e,r,n){var i=r.children[n];if("function"!=typeof i.measureText)return i;t.save(),i.setContext(t,!0);var a=i.getAttribute("x"),s=i.getAttribute("y"),o=i.getAttribute("dx"),u=i.getAttribute("dy"),h=i.getStyle("font-family").getDefinition(),c=Boolean(h)&&h.isRTL;0===n&&(a.hasValue()||a.setValue(i.getInheritedAttribute("x")),s.hasValue()||s.setValue(i.getInheritedAttribute("y")),o.hasValue()||o.setValue(i.getInheritedAttribute("dx")),u.hasValue()||u.setValue(i.getInheritedAttribute("dy")));var l=i.measureText(t);return c&&(e.x-=l),a.hasValue()?(e.applyAnchoring(),i.x=a.getPixels("x"),o.hasValue()&&(i.x+=o.getPixels("x"))):(o.hasValue()&&(e.x+=o.getPixels("x")),i.x=e.x),e.x=i.x,c||(e.x+=l),s.hasValue()?(i.y=s.getPixels("y"),u.hasValue()&&(i.y+=u.getPixels("y"))):(u.hasValue()&&(e.y+=u.getPixels("y")),i.y=e.y),e.y=i.y,e.leafTexts.push(i),e.minX=Math.min(e.minX,i.x,i.x+l),e.maxX=Math.max(e.maxX,i.x,i.x+l),i.clearContext(t),t.restore(),i}getChildBoundingBox(t,e,r,n){var i=r.children[n];if("function"!=typeof i.getBoundingBox)return null;var a=i.getBoundingBox(t);return a?(i.children.forEach((r,n)=>{var s=e.getChildBoundingBox(t,e,i,n);a.addBoundingBox(s)}),a):null}renderChild(t,e,r,n){var i=r.children[n];i.render(t),i.children.forEach((r,n)=>{e.renderChild(t,e,i,n)})}measureText(t){var{measureCache:e}=this;if(~e)return e;var r=this.getText(),n=this.measureTargetText(t,r);return this.measureCache=n,n}measureTargetText(t,e){if(!e.length)return 0;var{parent:r}=this,n=r.getStyle("font-family").getDefinition();if(n){for(var i=this.getFontSize(),a=n.isRTL?e.split("").reverse().join(""):e,s=y(r.getAttribute("dx").getString()),o=a.length,u=0,h=0;h<o;h++){u+=(this.getGlyph(n,a,h).horizAdvX||n.horizAdvX)*i/n.fontFace.unitsPerEm,void 0===s[h]||isNaN(s[h])||(u+=s[h])}return u}if(!t.measureText)return 10*e.length;t.save(),this.setContext(t,!0);var{width:c}=t.measureText(e);return this.clearContext(t),t.restore(),c}getInheritedAttribute(t){for(var e=this;e instanceof yt&&e.isFirstChild();){var r=e.parent.getAttribute(t);if(r.hasValue(!0))return r.getValue("0");e=e.parent}return null}}class mt extends yt{constructor(t,e,r){super(t,e,new.target===mt||r),this.type="tspan",this.text=this.children.length>0?"":this.getTextFromNode()}getText(){return this.text}}class xt extends mt{constructor(){super(...arguments),this.type="textNode"}}class bt extends pt{constructor(){super(...arguments),this.type="svg",this.root=!1}setContext(t){var e,{document:r}=this,{screen:n,window:i}=r,a=t.canvas;if(n.setDefaults(t),a.style&&void 0!==t.font&&i&&void 0!==i.getComputedStyle){t.font=i.getComputedStyle(a).getPropertyValue("font");var s=new U(r,"fontSize",lt.parse(t.font).fontSize);s.hasValue()&&(r.rootEmSize=s.getPixels("y"),r.emSize=r.rootEmSize)}this.getAttribute("x").hasValue()||this.getAttribute("x",!0).setValue(0),this.getAttribute("y").hasValue()||this.getAttribute("y",!0).setValue(0);var{width:o,height:u}=n.viewPort;this.getStyle("width").hasValue()||this.getStyle("width",!0).setValue("100%"),this.getStyle("height").hasValue()||this.getStyle("height",!0).setValue("100%"),this.getStyle("color").hasValue()||this.getStyle("color",!0).setValue("black");var h=this.getAttribute("refX"),c=this.getAttribute("refY"),l=this.getAttribute("viewBox"),f=l.hasValue()?y(l.getString()):null,g=!this.root&&"visible"!==this.getStyle("overflow").getValue("hidden"),p=0,d=0,v=0,m=0;f&&(p=f[0],d=f[1]),this.root||(o=this.getStyle("width").getPixels("x"),u=this.getStyle("height").getPixels("y"),"marker"===this.type&&(v=p,m=d,p=0,d=0)),n.viewPort.setCurrent(o,u),!this.node||this.parent&&"foreignObject"!==(null===(e=this.node.parentNode)||void 0===e?void 0:e.nodeName)||!this.getStyle("transform",!1,!0).hasValue()||this.getStyle("transform-origin",!1,!0).hasValue()||this.getStyle("transform-origin",!0,!0).setValue("50% 50%"),super.setContext(t),t.translate(this.getAttribute("x").getPixels("x"),this.getAttribute("y").getPixels("y")),f&&(o=f[2],u=f[3]),r.setViewBox({ctx:t,aspectRatio:this.getAttribute("preserveAspectRatio").getString(),width:n.viewPort.width,desiredWidth:o,height:n.viewPort.height,desiredHeight:u,minX:p,minY:d,refX:h.getValue(),refY:c.getValue(),clip:g,clipX:v,clipY:m}),f&&(n.viewPort.removeCurrent(),n.viewPort.setCurrent(o,u))}clearContext(t){super.clearContext(t),this.document.screen.viewPort.removeCurrent()}resize(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],n=this.getAttribute("width",!0),i=this.getAttribute("height",!0),a=this.getAttribute("viewBox"),s=this.getAttribute("style"),o=n.getNumber(0),u=i.getNumber(0);if(r)if("string"==typeof r)this.getAttribute("preserveAspectRatio",!0).setValue(r);else{var h=this.getAttribute("preserveAspectRatio");h.hasValue()&&h.setValue(h.getString().replace(/^\s*(\S.*\S)\s*$/,"$1"))}if(n.setValue(t),i.setValue(e),a.hasValue()||a.setValue("0 0 ".concat(o||t," ").concat(u||e)),s.hasValue()){var c=this.getStyle("width"),l=this.getStyle("height");c.hasValue()&&c.setValue("".concat(t,"px")),l.hasValue()&&l.setValue("".concat(e,"px"))}}}class St extends dt{constructor(){super(...arguments),this.type="rect"}path(t){var e=this.getAttribute("x").getPixels("x"),r=this.getAttribute("y").getPixels("y"),n=this.getStyle("width",!1,!0).getPixels("x"),i=this.getStyle("height",!1,!0).getPixels("y"),a=this.getAttribute("rx"),s=this.getAttribute("ry"),o=a.getPixels("x"),u=s.getPixels("y");if(a.hasValue()&&!s.hasValue()&&(u=o),s.hasValue()&&!a.hasValue()&&(o=u),o=Math.min(o,n/2),u=Math.min(u,i/2),t){var h=(Math.sqrt(2)-1)/3*4;t.beginPath(),i>0&&n>0&&(t.moveTo(e+o,r),t.lineTo(e+n-o,r),t.bezierCurveTo(e+n-o+h*o,r,e+n,r+u-h*u,e+n,r+u),t.lineTo(e+n,r+i-u),t.bezierCurveTo(e+n,r+i-u+h*u,e+n-o+h*o,r+i,e+n-o,r+i),t.lineTo(e+o,r+i),t.bezierCurveTo(e+o-h*o,r+i,e,r+i-u+h*u,e,r+i-u),t.lineTo(e,r+u),t.bezierCurveTo(e,r+u-h*u,e+o-h*o,r,e+o,r),t.closePath())}return new ft(e,r,e+n,r+i)}getMarkers(){return null}}class wt extends dt{constructor(){super(...arguments),this.type="circle"}path(t){var e=this.getAttribute("cx").getPixels("x"),r=this.getAttribute("cy").getPixels("y"),n=this.getAttribute("r").getPixels();return t&&n>0&&(t.beginPath(),t.arc(e,r,n,0,2*Math.PI,!1),t.closePath()),new ft(e-n,r-n,e+n,r+n)}getMarkers(){return null}}class Tt extends dt{constructor(){super(...arguments),this.type="ellipse"}path(t){var e=(Math.sqrt(2)-1)/3*4,r=this.getAttribute("rx").getPixels("x"),n=this.getAttribute("ry").getPixels("y"),i=this.getAttribute("cx").getPixels("x"),a=this.getAttribute("cy").getPixels("y");return t&&r>0&&n>0&&(t.beginPath(),t.moveTo(i+r,a),t.bezierCurveTo(i+r,a+e*n,i+e*r,a+n,i,a+n),t.bezierCurveTo(i-e*r,a+n,i-r,a+e*n,i-r,a),t.bezierCurveTo(i-r,a-e*n,i-e*r,a-n,i,a-n),t.bezierCurveTo(i+e*r,a-n,i+r,a-e*n,i+r,a),t.closePath()),new ft(i-r,a-n,i+r,a+n)}getMarkers(){return null}}class At extends dt{constructor(){super(...arguments),this.type="line"}getPoints(){return[new X(this.getAttribute("x1").getPixels("x"),this.getAttribute("y1").getPixels("y")),new X(this.getAttribute("x2").getPixels("x"),this.getAttribute("y2").getPixels("y"))]}path(t){var[{x:e,y:r},{x:n,y:i}]=this.getPoints();return t&&(t.beginPath(),t.moveTo(e,r),t.lineTo(n,i)),new ft(e,r,n,i)}getMarkers(){var[t,e]=this.getPoints(),r=t.angleTo(e);return[[t,r],[e,r]]}}class Ot extends dt{constructor(t,e,r){super(t,e,r),this.type="polyline",this.points=[],this.points=X.parsePath(this.getAttribute("points").getString())}path(t){var{points:e}=this,[{x:r,y:n}]=e,i=new ft(r,n);return t&&(t.beginPath(),t.moveTo(r,n)),e.forEach(e=>{var{x:r,y:n}=e;i.addPoint(r,n),t&&t.lineTo(r,n)}),i}getMarkers(){var{points:t}=this,e=t.length-1,r=[];return t.forEach((n,i)=>{i!==e&&r.push([n,n.angleTo(t[i+1])])}),r.length>0&&r.push([t[t.length-1],r[r.length-1][1]]),r}}class Pt extends Ot{constructor(){super(...arguments),this.type="polygon"}path(t){var e=super.path(t),[{x:r,y:n}]=this.points;return t&&(t.lineTo(r,n),t.closePath()),e}}class Ct extends st{constructor(){super(...arguments),this.type="pattern"}createPattern(t,e,r){var n=this.getStyle("width").getPixels("x",!0),i=this.getStyle("height").getPixels("y",!0),a=new bt(this.document,null);a.attributes.viewBox=new U(this.document,"viewBox",this.getAttribute("viewBox").getValue()),a.attributes.width=new U(this.document,"width","".concat(n,"px")),a.attributes.height=new U(this.document,"height","".concat(i,"px")),a.attributes.transform=new U(this.document,"transform",this.getAttribute("patternTransform").getValue()),a.children=this.children;var s=this.document.createCanvas(n,i),o=s.getContext("2d"),u=this.getAttribute("x"),h=this.getAttribute("y");u.hasValue()&&h.hasValue()&&o.translate(u.getPixels("x",!0),h.getPixels("y",!0)),r.hasValue()?this.styles["fill-opacity"]=r:Reflect.deleteProperty(this.styles,"fill-opacity");for(var c=-1;c<=1;c++)for(var l=-1;l<=1;l++)o.save(),a.attributes.x=new U(this.document,"x",c*s.width),a.attributes.y=new U(this.document,"y",l*s.height),a.render(o),o.restore();return t.createPattern(s,"repeat")}}class Et extends st{constructor(){super(...arguments),this.type="marker"}render(t,e,r){if(e){var{x:n,y:i}=e,a=this.getAttribute("orient").getString("auto"),s=this.getAttribute("markerUnits").getString("strokeWidth");t.translate(n,i),"auto"===a&&t.rotate(r),"strokeWidth"===s&&t.scale(t.lineWidth,t.lineWidth),t.save();var o=new bt(this.document,null);o.type=this.type,o.attributes.viewBox=new U(this.document,"viewBox",this.getAttribute("viewBox").getValue()),o.attributes.refX=new U(this.document,"refX",this.getAttribute("refX").getValue()),o.attributes.refY=new U(this.document,"refY",this.getAttribute("refY").getValue()),o.attributes.width=new U(this.document,"width",this.getAttribute("markerWidth").getValue()),o.attributes.height=new U(this.document,"height",this.getAttribute("markerHeight").getValue()),o.attributes.overflow=new U(this.document,"overflow",this.getAttribute("overflow").getValue()),o.attributes.fill=new U(this.document,"fill",this.getAttribute("fill").getColor("black")),o.attributes.stroke=new U(this.document,"stroke",this.getAttribute("stroke").getValue("none")),o.children=this.children,o.render(t),t.restore(),"strokeWidth"===s&&t.scale(1/t.lineWidth,1/t.lineWidth),"auto"===a&&t.rotate(-r),t.translate(-n,-i)}}}class Mt extends st{constructor(){super(...arguments),this.type="defs"}render(){}}class Nt extends pt{constructor(){super(...arguments),this.type="g"}getBoundingBox(t){var e=new ft;return this.children.forEach(r=>{e.addBoundingBox(r.getBoundingBox(t))}),e}}class Rt extends st{constructor(t,e,r){super(t,e,r),this.attributesToInherit=["gradientUnits"],this.stops=[];var{stops:n,children:i}=this;i.forEach(t=>{"stop"===t.type&&n.push(t)})}getGradientUnits(){return this.getAttribute("gradientUnits").getString("objectBoundingBox")}createGradient(t,e,r){var n=this;this.getHrefAttribute().hasValue()&&(n=this.getHrefAttribute().getDefinition(),this.inheritStopContainer(n));var{stops:i}=n,a=this.getGradient(t,e);if(!a)return this.addParentOpacity(r,i[i.length-1].color);if(i.forEach(t=>{a.addColorStop(t.offset,this.addParentOpacity(r,t.color))}),this.getAttribute("gradientTransform").hasValue()){var{document:s}=this,{MAX_VIRTUAL_PIXELS:o,viewPort:u}=s.screen,[h]=u.viewPorts,c=new St(s,null);c.attributes.x=new U(s,"x",-o/3),c.attributes.y=new U(s,"y",-o/3),c.attributes.width=new U(s,"width",o),c.attributes.height=new U(s,"height",o);var l=new Nt(s,null);l.attributes.transform=new U(s,"transform",this.getAttribute("gradientTransform").getValue()),l.children=[c];var f=new bt(s,null);f.attributes.x=new U(s,"x",0),f.attributes.y=new U(s,"y",0),f.attributes.width=new U(s,"width",h.width),f.attributes.height=new U(s,"height",h.height),f.children=[l];var g=s.createCanvas(h.width,h.height),p=g.getContext("2d");return p.fillStyle=a,f.render(p),p.createPattern(g,"no-repeat")}return a}inheritStopContainer(t){this.attributesToInherit.forEach(e=>{!this.getAttribute(e).hasValue()&&t.getAttribute(e).hasValue()&&this.getAttribute(e,!0).setValue(t.getAttribute(e).getValue())})}addParentOpacity(t,e){return t.hasValue()?new U(this.document,"color",e).addOpacity(t).getColor():e}}class _t extends Rt{constructor(t,e,r){super(t,e,r),this.type="linearGradient",this.attributesToInherit.push("x1","y1","x2","y2")}getGradient(t,e){var r="objectBoundingBox"===this.getGradientUnits(),n=r?e.getBoundingBox(t):null;if(r&&!n)return null;this.getAttribute("x1").hasValue()||this.getAttribute("y1").hasValue()||this.getAttribute("x2").hasValue()||this.getAttribute("y2").hasValue()||(this.getAttribute("x1",!0).setValue(0),this.getAttribute("y1",!0).setValue(0),this.getAttribute("x2",!0).setValue(1),this.getAttribute("y2",!0).setValue(0));var i=r?n.x+n.width*this.getAttribute("x1").getNumber():this.getAttribute("x1").getPixels("x"),a=r?n.y+n.height*this.getAttribute("y1").getNumber():this.getAttribute("y1").getPixels("y"),s=r?n.x+n.width*this.getAttribute("x2").getNumber():this.getAttribute("x2").getPixels("x"),o=r?n.y+n.height*this.getAttribute("y2").getNumber():this.getAttribute("y2").getPixels("y");return i===s&&a===o?null:t.createLinearGradient(i,a,s,o)}}class Vt extends Rt{constructor(t,e,r){super(t,e,r),this.type="radialGradient",this.attributesToInherit.push("cx","cy","r","fx","fy","fr")}getGradient(t,e){var r="objectBoundingBox"===this.getGradientUnits(),n=e.getBoundingBox(t);if(r&&!n)return null;this.getAttribute("cx").hasValue()||this.getAttribute("cx",!0).setValue("50%"),this.getAttribute("cy").hasValue()||this.getAttribute("cy",!0).setValue("50%"),this.getAttribute("r").hasValue()||this.getAttribute("r",!0).setValue("50%");var i=r?n.x+n.width*this.getAttribute("cx").getNumber():this.getAttribute("cx").getPixels("x"),a=r?n.y+n.height*this.getAttribute("cy").getNumber():this.getAttribute("cy").getPixels("y"),s=i,o=a;this.getAttribute("fx").hasValue()&&(s=r?n.x+n.width*this.getAttribute("fx").getNumber():this.getAttribute("fx").getPixels("x")),this.getAttribute("fy").hasValue()&&(o=r?n.y+n.height*this.getAttribute("fy").getNumber():this.getAttribute("fy").getPixels("y"));var u=r?(n.width+n.height)/2*this.getAttribute("r").getNumber():this.getAttribute("r").getPixels(),h=this.getAttribute("fr").getPixels();return t.createRadialGradient(s,o,h,i,a,u)}}class It extends st{constructor(t,e,r){super(t,e,r),this.type="stop";var n=Math.max(0,Math.min(1,this.getAttribute("offset").getNumber())),i=this.getStyle("stop-opacity"),a=this.getStyle("stop-color",!0);""===a.getString()&&a.setValue("#000"),i.hasValue()&&(a=a.addOpacity(i)),this.offset=n,this.color=a.getColor()}}class kt extends st{constructor(t,e,r){super(t,e,r),this.type="animate",this.duration=0,this.initialValue=null,this.initialUnits="",this.removed=!1,this.frozen=!1,t.screen.animations.push(this),this.begin=this.getAttribute("begin").getMilliseconds(),this.maxDuration=this.begin+this.getAttribute("dur").getMilliseconds(),this.from=this.getAttribute("from"),this.to=this.getAttribute("to"),this.values=new U(t,"values",null);var n=this.getAttribute("values");n.hasValue()&&this.values.setValue(n.getString().split(";"))}getProperty(){var t=this.getAttribute("attributeType").getString(),e=this.getAttribute("attributeName").getString();return"CSS"===t?this.parent.getStyle(e,!0):this.parent.getAttribute(e,!0)}calcValue(){var{initialUnits:t}=this,{progress:e,from:r,to:n}=this.getProgress(),i=r.getNumber()+(n.getNumber()-r.getNumber())*e;return"%"===t&&(i*=100),"".concat(i).concat(t)}update(t){var{parent:e}=this,r=this.getProperty();if(this.initialValue||(this.initialValue=r.getString(),this.initialUnits=r.getUnits()),this.duration>this.maxDuration){var n=this.getAttribute("fill").getString("remove");if("indefinite"===this.getAttribute("repeatCount").getString()||"indefinite"===this.getAttribute("repeatDur").getString())this.duration=0;else if("freeze"!==n||this.frozen){if("remove"===n&&!this.removed)return this.removed=!0,r.setValue(e.animationFrozen?e.animationFrozenValue:this.initialValue),!0}else this.frozen=!0,e.animationFrozen=!0,e.animationFrozenValue=r.getString();return!1}this.duration+=t;var i=!1;if(this.begin<this.duration){var a=this.calcValue(),s=this.getAttribute("type");if(s.hasValue()){var o=s.getString();a="".concat(o,"(").concat(a,")")}r.setValue(a),i=!0}return i}getProgress(){var{document:t,values:e}=this,r={progress:(this.duration-this.begin)/(this.maxDuration-this.begin)};if(e.hasValue()){var n=r.progress*(e.getValue().length-1),i=Math.floor(n),a=Math.ceil(n);r.from=new U(t,"from",parseFloat(e.getValue()[i])),r.to=new U(t,"to",parseFloat(e.getValue()[a])),r.progress=(n-i)/(a-i)}else r.from=this.from,r.to=this.to;return r}}class Lt extends kt{constructor(){super(...arguments),this.type="animateColor"}calcValue(){var{progress:t,from:e,to:r}=this.getProgress(),n=new c.a(e.getColor()),i=new c.a(r.getColor());if(n.ok&&i.ok){var a=n.r+(i.r-n.r)*t,s=n.g+(i.g-n.g)*t,o=n.b+(i.b-n.b)*t;return"rgb(".concat(Math.floor(a),", ").concat(Math.floor(s),", ").concat(Math.floor(o),")")}return this.getAttribute("from").getColor()}}class Dt extends kt{constructor(){super(...arguments),this.type="animateTransform"}calcValue(){var{progress:t,from:e,to:r}=this.getProgress(),n=y(e.getString()),i=y(r.getString());return n.map((e,r)=>e+(i[r]-e)*t).join(" ")}}class Bt extends st{constructor(t,e,r){super(t,e,r),this.type="font",this.glyphs={},this.horizAdvX=this.getAttribute("horiz-adv-x").getNumber();var{definitions:n}=t,{children:i}=this;for(var a of i)switch(a.type){case"font-face":this.fontFace=a;var s=a.getStyle("font-family");s.hasValue()&&(n[s.getString()]=this);break;case"missing-glyph":this.missingGlyph=a;break;case"glyph":var o=a;o.arabicForm?(this.isRTL=!0,this.isArabic=!0,void 0===this.glyphs[o.unicode]&&(this.glyphs[o.unicode]={}),this.glyphs[o.unicode][o.arabicForm]=o):this.glyphs[o.unicode]=o}}render(){}}class jt extends st{constructor(t,e,r){super(t,e,r),this.type="font-face",this.ascent=this.getAttribute("ascent").getNumber(),this.descent=this.getAttribute("descent").getNumber(),this.unitsPerEm=this.getAttribute("units-per-em").getNumber()}}class Ft extends dt{constructor(){super(...arguments),this.type="missing-glyph",this.horizAdvX=0}}class zt extends yt{constructor(){super(...arguments),this.type="tref"}getText(){var t=this.getHrefAttribute().getDefinition();if(t){var e=t.children[0];if(e)return e.getText()}return""}}class Ut extends yt{constructor(t,e,r){super(t,e,r),this.type="a";var{childNodes:n}=e,i=n[0],a=n.length>0&&Array.from(n).every(t=>3===t.nodeType);this.hasText=a,this.text=a?this.getTextFromNode(i):""}getText(){return this.text}renderChildren(t){if(this.hasText){super.renderChildren(t);var{document:e,x:r,y:n}=this,{mouse:i}=e.screen,a=new U(e,"fontSize",lt.parse(e.ctx.font).fontSize);i.isWorking()&&i.checkBoundingBox(this,new ft(r,n-a.getPixels("y"),r+this.measureText(t),n))}else if(this.children.length>0){var s=new Nt(this.document,null);s.children=this.children,s.parent=this,s.render(t)}}onClick(){var{window:t}=this.document;t&&t.open(this.getHrefAttribute().getString())}onMouseMove(){this.document.ctx.canvas.style.cursor="pointer"}}function Ht(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Xt(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Ht(Object(r),!0).forEach((function(e){s()(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Ht(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}class Yt extends yt{constructor(t,e,r){super(t,e,r),this.type="textPath",this.textWidth=0,this.textHeight=0,this.pathLength=-1,this.glyphInfo=null,this.letterSpacingCache=[],this.measuresCache=new Map([["",0]]);var n=this.getHrefAttribute().getDefinition();this.text=this.getTextFromNode(),this.dataArray=this.parsePathData(n)}getText(){return this.text}path(t){var{dataArray:e}=this;t&&t.beginPath(),e.forEach(e=>{var{type:r,points:n}=e;switch(r){case gt.LINE_TO:t&&t.lineTo(n[0],n[1]);break;case gt.MOVE_TO:t&&t.moveTo(n[0],n[1]);break;case gt.CURVE_TO:t&&t.bezierCurveTo(n[0],n[1],n[2],n[3],n[4],n[5]);break;case gt.QUAD_TO:t&&t.quadraticCurveTo(n[0],n[1],n[2],n[3]);break;case gt.ARC:var[i,a,s,o,u,h,c,l]=n,f=s>o?s:o,g=s>o?1:s/o,p=s>o?o/s:1;t&&(t.translate(i,a),t.rotate(c),t.scale(g,p),t.arc(0,0,f,u,u+h,Boolean(1-l)),t.scale(1/g,1/p),t.rotate(-c),t.translate(-i,-a));break;case gt.CLOSE_PATH:t&&t.closePath()}})}renderChildren(t){this.setTextData(t),t.save();var e=this.parent.getStyle("text-decoration").getString(),r=this.getFontSize(),{glyphInfo:n}=this,i=t.fillStyle;"underline"===e&&t.beginPath(),n.forEach((n,i)=>{var{p0:a,p1:s,rotation:o,text:u}=n;t.save(),t.translate(a.x,a.y),t.rotate(o),t.fillStyle&&t.fillText(u,0,0),t.strokeStyle&&t.strokeText(u,0,0),t.restore(),"underline"===e&&(0===i&&t.moveTo(a.x,a.y+r/8),t.lineTo(s.x,s.y+r/5))}),"underline"===e&&(t.lineWidth=r/20,t.strokeStyle=i,t.stroke(),t.closePath()),t.restore()}getLetterSpacingAt(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return this.letterSpacingCache[t]||0}findSegmentToFitChar(t,e,r,n,i,a,s,o,u){var h=a,c=this.measureText(t,o);" "===o&&"justify"===e&&r<n&&(c+=(n-r)/i),u>-1&&(h+=this.getLetterSpacingAt(u));var l=this.textHeight/20,f=this.getEquidistantPointOnPath(h,l,0),g=this.getEquidistantPointOnPath(h+c,l,0),p={p0:f,p1:g},d=f&&g?Math.atan2(g.y-f.y,g.x-f.x):0;if(s){var v=Math.cos(Math.PI/2+d)*s,y=Math.cos(-d)*s;p.p0=Xt(Xt({},f),{},{x:f.x+v,y:f.y+y}),p.p1=Xt(Xt({},g),{},{x:g.x+v,y:g.y+y})}return{offset:h+=c,segment:p,rotation:d}}measureText(t,e){var{measuresCache:r}=this,n=e||this.getText();if(r.has(n))return r.get(n);var i=this.measureTargetText(t,n);return r.set(n,i),i}setTextData(t){if(!this.glyphInfo){var e=this.getText(),r=e.split(""),n=e.split(" ").length-1,i=this.parent.getAttribute("dx").split().map(t=>t.getPixels("x")),a=this.parent.getAttribute("dy").getPixels("y"),s=this.parent.getStyle("text-anchor").getString("start"),o=this.getStyle("letter-spacing"),u=this.parent.getStyle("letter-spacing"),h=0;o.hasValue()&&"inherit"!==o.getValue()?o.hasValue()&&"initial"!==o.getValue()&&"unset"!==o.getValue()&&(h=o.getPixels()):h=u.getPixels();var c=[],l=e.length;this.letterSpacingCache=c;for(var f=0;f<l;f++)c.push(void 0!==i[f]?i[f]:h);var g=c.reduce((t,e,r)=>0===r?0:t+e||0,0),p=this.measureText(t),d=Math.max(p+g,0);this.textWidth=p,this.textHeight=this.getFontSize(),this.glyphInfo=[];var v=this.getPathLength(),y=this.getStyle("startOffset").getNumber(0)*v,m=0;"middle"!==s&&"center"!==s||(m=-d/2),"end"!==s&&"right"!==s||(m=-d),m+=y,r.forEach((e,i)=>{var{offset:o,segment:u,rotation:h}=this.findSegmentToFitChar(t,s,d,v,n,m,a,e,i);m=o,u.p0&&u.p1&&this.glyphInfo.push({text:r[i],p0:u.p0,p1:u.p1,rotation:h})})}}parsePathData(t){if(this.pathLength=-1,!t)return[];var e=[],{pathParser:r}=t;for(r.reset();!r.isEnd();){var{current:n}=r,i=n?n.x:0,a=n?n.y:0,s=r.next(),o=s.type,u=[];switch(s.type){case gt.MOVE_TO:this.pathM(r,u);break;case gt.LINE_TO:o=this.pathL(r,u);break;case gt.HORIZ_LINE_TO:o=this.pathH(r,u);break;case gt.VERT_LINE_TO:o=this.pathV(r,u);break;case gt.CURVE_TO:this.pathC(r,u);break;case gt.SMOOTH_CURVE_TO:o=this.pathS(r,u);break;case gt.QUAD_TO:this.pathQ(r,u);break;case gt.SMOOTH_QUAD_TO:o=this.pathT(r,u);break;case gt.ARC:u=this.pathA(r);break;case gt.CLOSE_PATH:dt.pathZ(r)}s.type!==gt.CLOSE_PATH?e.push({type:o,points:u,start:{x:i,y:a},pathLength:this.calcLength(i,a,o,u)}):e.push({type:gt.CLOSE_PATH,points:[],pathLength:0})}return e}pathM(t,e){var{x:r,y:n}=dt.pathM(t).point;e.push(r,n)}pathL(t,e){var{x:r,y:n}=dt.pathL(t).point;return e.push(r,n),gt.LINE_TO}pathH(t,e){var{x:r,y:n}=dt.pathH(t).point;return e.push(r,n),gt.LINE_TO}pathV(t,e){var{x:r,y:n}=dt.pathV(t).point;return e.push(r,n),gt.LINE_TO}pathC(t,e){var{point:r,controlPoint:n,currentPoint:i}=dt.pathC(t);e.push(r.x,r.y,n.x,n.y,i.x,i.y)}pathS(t,e){var{point:r,controlPoint:n,currentPoint:i}=dt.pathS(t);return e.push(r.x,r.y,n.x,n.y,i.x,i.y),gt.CURVE_TO}pathQ(t,e){var{controlPoint:r,currentPoint:n}=dt.pathQ(t);e.push(r.x,r.y,n.x,n.y)}pathT(t,e){var{controlPoint:r,currentPoint:n}=dt.pathT(t);return e.push(r.x,r.y,n.x,n.y),gt.QUAD_TO}pathA(t){var{rX:e,rY:r,sweepFlag:n,xAxisRotation:i,centp:a,a1:s,ad:o}=dt.pathA(t);return 0===n&&o>0&&(o-=2*Math.PI),1===n&&o<0&&(o+=2*Math.PI),[a.x,a.y,e,r,s,o,i,n]}calcLength(t,e,r,n){var i=0,a=null,s=null,o=0;switch(r){case gt.LINE_TO:return this.getLineLength(t,e,n[0],n[1]);case gt.CURVE_TO:for(i=0,a=this.getPointOnCubicBezier(0,t,e,n[0],n[1],n[2],n[3],n[4],n[5]),o=.01;o<=1;o+=.01)s=this.getPointOnCubicBezier(o,t,e,n[0],n[1],n[2],n[3],n[4],n[5]),i+=this.getLineLength(a.x,a.y,s.x,s.y),a=s;return i;case gt.QUAD_TO:for(i=0,a=this.getPointOnQuadraticBezier(0,t,e,n[0],n[1],n[2],n[3]),o=.01;o<=1;o+=.01)s=this.getPointOnQuadraticBezier(o,t,e,n[0],n[1],n[2],n[3]),i+=this.getLineLength(a.x,a.y,s.x,s.y),a=s;return i;case gt.ARC:i=0;var u=n[4],h=n[5],c=n[4]+h,l=Math.PI/180;if(Math.abs(u-c)<l&&(l=Math.abs(u-c)),a=this.getPointOnEllipticalArc(n[0],n[1],n[2],n[3],u,0),h<0)for(o=u-l;o>c;o-=l)s=this.getPointOnEllipticalArc(n[0],n[1],n[2],n[3],o,0),i+=this.getLineLength(a.x,a.y,s.x,s.y),a=s;else for(o=u+l;o<c;o+=l)s=this.getPointOnEllipticalArc(n[0],n[1],n[2],n[3],o,0),i+=this.getLineLength(a.x,a.y,s.x,s.y),a=s;return s=this.getPointOnEllipticalArc(n[0],n[1],n[2],n[3],c,0),i+=this.getLineLength(a.x,a.y,s.x,s.y)}return 0}getPointOnLine(t,e,r,n,i){var a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:e,s=arguments.length>6&&void 0!==arguments[6]?arguments[6]:r,o=(i-r)/(n-e+R),u=Math.sqrt(t*t/(1+o*o));n<e&&(u*=-1);var h=o*u,c=null;if(n===e)c={x:a,y:s+h};else if((s-r)/(a-e+R)===o)c={x:a+u,y:s+h};else{var l,f,g=this.getLineLength(e,r,n,i);if(g<R)return null;var p=(a-e)*(n-e)+(s-r)*(i-r);l=e+(p/=g*g)*(n-e),f=r+p*(i-r);var d=this.getLineLength(a,s,l,f),v=Math.sqrt(t*t-d*d);u=Math.sqrt(v*v/(1+o*o)),n<e&&(u*=-1),c={x:l+u,y:f+(h=o*u)}}return c}getPointOnPath(t){var e=this.getPathLength(),r=0,n=null;if(t<-5e-5||t-5e-5>e)return null;var{dataArray:i}=this;for(var a of i){if(!a||!(a.pathLength<5e-5||r+a.pathLength+5e-5<t)){var s=t-r,o=0;switch(a.type){case gt.LINE_TO:n=this.getPointOnLine(s,a.start.x,a.start.y,a.points[0],a.points[1],a.start.x,a.start.y);break;case gt.ARC:var u=a.points[4],h=a.points[5],c=a.points[4]+h;if(o=u+s/a.pathLength*h,h<0&&o<c||h>=0&&o>c)break;n=this.getPointOnEllipticalArc(a.points[0],a.points[1],a.points[2],a.points[3],o,a.points[6]);break;case gt.CURVE_TO:(o=s/a.pathLength)>1&&(o=1),n=this.getPointOnCubicBezier(o,a.start.x,a.start.y,a.points[0],a.points[1],a.points[2],a.points[3],a.points[4],a.points[5]);break;case gt.QUAD_TO:(o=s/a.pathLength)>1&&(o=1),n=this.getPointOnQuadraticBezier(o,a.start.x,a.start.y,a.points[0],a.points[1],a.points[2],a.points[3])}if(n)return n;break}r+=a.pathLength}return null}getLineLength(t,e,r,n){return Math.sqrt((r-t)*(r-t)+(n-e)*(n-e))}getPathLength(){return-1===this.pathLength&&(this.pathLength=this.dataArray.reduce((t,e)=>e.pathLength>0?t+e.pathLength:t,0)),this.pathLength}getPointOnCubicBezier(t,e,r,n,i,a,s,o,u){return{x:o*k(t)+a*L(t)+n*D(t)+e*B(t),y:u*k(t)+s*L(t)+i*D(t)+r*B(t)}}getPointOnQuadraticBezier(t,e,r,n,i,a,s){return{x:a*j(t)+n*F(t)+e*z(t),y:s*j(t)+i*F(t)+r*z(t)}}getPointOnEllipticalArc(t,e,r,n,i,a){var s=Math.cos(a),o=Math.sin(a),u=r*Math.cos(i),h=n*Math.sin(i);return{x:t+(u*s-h*o),y:e+(u*o+h*s)}}buildEquidistantCache(t,e){var r=this.getPathLength(),n=e||.25,i=t||r/100;if(!this.equidistantCache||this.equidistantCache.step!==i||this.equidistantCache.precision!==n){this.equidistantCache={step:i,precision:n,points:[]};for(var a=0,s=0;s<=r;s+=n){var o=this.getPointOnPath(s),u=this.getPointOnPath(s+n);o&&u&&((a+=this.getLineLength(o.x,o.y,u.x,u.y))>=i&&(this.equidistantCache.points.push({x:o.x,y:o.y,distance:s}),a-=i))}}}getEquidistantPointOnPath(t,e,r){if(this.buildEquidistantCache(e,r),t<0||t-this.getPathLength()>5e-5)return null;var n=Math.round(t/this.getPathLength()*(this.equidistantCache.points.length-1));return this.equidistantCache.points[n]||null}}var Gt=/^\s*data:(([^/,;]+\/[^/,;]+)(?:;([^,;=]+=[^,;=]+))?)?(?:;(base64))?,(.*)$/i;class Wt extends pt{constructor(t,e,r){super(t,e,r),this.type="image",this.loaded=!1;var n=this.getHrefAttribute().getString();if(n){var i=n.endsWith(".svg")||/^\s*data:image\/svg\+xml/i.test(n);t.images.push(this),i?this.loadSvg(n):this.loadImage(n),this.isSvg=i}}loadImage(t){var e=this;return i()((function*(){try{var r=yield e.document.createImage(t);e.image=r}catch(e){console.error('Error while loading image "'.concat(t,'":'),e)}e.loaded=!0}))()}loadSvg(t){var e=this;return i()((function*(){var r=Gt.exec(t);if(r){var n=r[5];"base64"===r[4]?e.image=atob(n):e.image=decodeURIComponent(n)}else try{var i=yield e.document.fetch(t),a=yield i.text();e.image=a}catch(e){console.error('Error while loading image "'.concat(t,'":'),e)}e.loaded=!0}))()}renderChildren(t){var{document:e,image:r,loaded:n}=this,i=this.getAttribute("x").getPixels("x"),a=this.getAttribute("y").getPixels("y"),s=this.getStyle("width").getPixels("x"),o=this.getStyle("height").getPixels("y");if(n&&r&&s&&o){if(t.save(),t.translate(i,a),this.isSvg){var u=e.canvg.forkString(t,this.image,{ignoreMouse:!0,ignoreAnimation:!0,ignoreDimensions:!0,ignoreClear:!0,offsetX:0,offsetY:0,scaleWidth:s,scaleHeight:o});u.document.documentElement.parent=this,u.render()}else{var h=this.image;e.setViewBox({ctx:t,aspectRatio:this.getAttribute("preserveAspectRatio").getString(),width:s,desiredWidth:h.width,height:o,desiredHeight:h.height}),this.loaded&&(void 0===h.complete||h.complete)&&t.drawImage(h,0,0)}t.restore()}}getBoundingBox(){var t=this.getAttribute("x").getPixels("x"),e=this.getAttribute("y").getPixels("y"),r=this.getStyle("width").getPixels("x"),n=this.getStyle("height").getPixels("y");return new ft(t,e,t+r,e+n)}}class qt extends pt{constructor(){super(...arguments),this.type="symbol"}render(t){}}class Qt{constructor(t){this.document=t,this.loaded=!1,t.fonts.push(this)}load(t,e){var r=this;return i()((function*(){try{var{document:n}=r,i=(yield n.canvg.parser.load(e)).getElementsByTagName("font");Array.from(i).forEach(e=>{var r=n.createElement(e);n.definitions[t]=r})}catch(t){console.error('Error while loading font "'.concat(e,'":'),t)}r.loaded=!0}))()}}class $t extends st{constructor(t,e,r){super(t,e,r),this.type="style",p(Array.from(e.childNodes).map(t=>t.textContent).join("").replace(/(\/\*([^*]|[\r\n]|(\*+([^*/]|[\r\n])))*\*+\/)|(^[\s]*\/\/.*)/gm,"").replace(/@import.*;/g,"")).split("}").forEach(e=>{var r=e.trim();if(r){var n=r.split("{"),i=n[0].split(","),a=n[1].split(";");i.forEach(e=>{var r=e.trim();if(r){var n=t.styles[r]||{};if(a.forEach(e=>{var r=e.indexOf(":"),i=e.substr(0,r).trim(),a=e.substr(r+1,e.length-r).trim();i&&a&&(n[i]=new U(t,i,a))}),t.styles[r]=n,t.stylesSpecificity[r]=N(r),"@font-face"===r){var i=n["font-family"].getString().replace(/"|'/g,"");n.src.getString().split(",").forEach(e=>{if(e.indexOf('format("svg")')>0){var r=b(e);r&&new Qt(t).load(i,r)}})}}})}})}}$t.parseExternalUrl=b;class Zt extends pt{constructor(){super(...arguments),this.type="use"}setContext(t){super.setContext(t);var e=this.getAttribute("x"),r=this.getAttribute("y");e.hasValue()&&t.translate(e.getPixels("x"),0),r.hasValue()&&t.translate(0,r.getPixels("y"))}path(t){var{element:e}=this;e&&e.path(t)}renderChildren(t){var{document:e,element:r}=this;if(r){var n=r;if("symbol"===r.type&&((n=new bt(e,null)).attributes.viewBox=new U(e,"viewBox",r.getAttribute("viewBox").getString()),n.attributes.preserveAspectRatio=new U(e,"preserveAspectRatio",r.getAttribute("preserveAspectRatio").getString()),n.attributes.overflow=new U(e,"overflow",r.getAttribute("overflow").getString()),n.children=r.children,r.styles.opacity=new U(e,"opacity",this.calculateOpacity())),"svg"===n.type){var i=this.getStyle("width",!1,!0),a=this.getStyle("height",!1,!0);i.hasValue()&&(n.attributes.width=new U(e,"width",i.getString())),a.hasValue()&&(n.attributes.height=new U(e,"height",a.getString()))}var s=n.parent;n.parent=this,n.render(t),n.parent=s}}getBoundingBox(t){var{element:e}=this;return e?e.getBoundingBox(t):null}elementTransform(){var{document:t,element:e}=this;return at.fromElement(t,e)}get element(){return this.cachedElement||(this.cachedElement=this.getHrefAttribute().getDefinition()),this.cachedElement}}function Kt(t,e,r,n,i,a){return t[r*n*4+4*e+a]}function Jt(t,e,r,n,i,a,s){t[r*n*4+4*e+a]=s}function te(t,e,r){return t[e]*r}function ee(t,e,r,n){return e+Math.cos(t)*r+Math.sin(t)*n}class re extends st{constructor(t,e,r){super(t,e,r),this.type="feColorMatrix";var n=y(this.getAttribute("values").getString());switch(this.getAttribute("type").getString("matrix")){case"saturate":var i=n[0];n=[.213+.787*i,.715-.715*i,.072-.072*i,0,0,.213-.213*i,.715+.285*i,.072-.072*i,0,0,.213-.213*i,.715-.715*i,.072+.928*i,0,0,0,0,0,1,0,0,0,0,0,1];break;case"hueRotate":var a=n[0]*Math.PI/180;n=[ee(a,.213,.787,-.213),ee(a,.715,-.715,-.715),ee(a,.072,-.072,.928),0,0,ee(a,.213,-.213,.143),ee(a,.715,.285,.14),ee(a,.072,-.072,-.283),0,0,ee(a,.213,-.213,-.787),ee(a,.715,-.715,.715),ee(a,.072,.928,.072),0,0,0,0,0,1,0,0,0,0,0,1];break;case"luminanceToAlpha":n=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,.2125,.7154,.0721,0,0,0,0,0,0,1]}this.matrix=n,this.includeOpacity=this.getAttribute("includeOpacity").hasValue()}apply(t,e,r,n,i){for(var{includeOpacity:a,matrix:s}=this,o=t.getImageData(0,0,n,i),u=0;u<i;u++)for(var h=0;h<n;h++){var c=Kt(o.data,h,u,n,0,0),l=Kt(o.data,h,u,n,0,1),f=Kt(o.data,h,u,n,0,2),g=Kt(o.data,h,u,n,0,3),p=te(s,0,c)+te(s,1,l)+te(s,2,f)+te(s,3,g)+te(s,4,1),d=te(s,5,c)+te(s,6,l)+te(s,7,f)+te(s,8,g)+te(s,9,1),v=te(s,10,c)+te(s,11,l)+te(s,12,f)+te(s,13,g)+te(s,14,1),y=te(s,15,c)+te(s,16,l)+te(s,17,f)+te(s,18,g)+te(s,19,1);a&&(p=0,d=0,v=0,y*=g/255),Jt(o.data,h,u,n,0,0,p),Jt(o.data,h,u,n,0,1,d),Jt(o.data,h,u,n,0,2,v),Jt(o.data,h,u,n,0,3,y)}t.clearRect(0,0,n,i),t.putImageData(o,0,0)}}class ne extends st{constructor(){super(...arguments),this.type="mask"}apply(t,e){var{document:r}=this,n=this.getAttribute("x").getPixels("x"),i=this.getAttribute("y").getPixels("y"),a=this.getStyle("width").getPixels("x"),s=this.getStyle("height").getPixels("y");if(!a&&!s){var o=new ft;this.children.forEach(e=>{o.addBoundingBox(e.getBoundingBox(t))}),n=Math.floor(o.x1),i=Math.floor(o.y1),a=Math.floor(o.width),s=Math.floor(o.height)}var u=this.removeStyles(e,ne.ignoreStyles),h=r.createCanvas(n+a,i+s),c=h.getContext("2d");r.screen.setDefaults(c),this.renderChildren(c),new re(r,{nodeType:1,childNodes:[],attributes:[{nodeName:"type",value:"luminanceToAlpha"},{nodeName:"includeOpacity",value:"true"}]}).apply(c,0,0,n+a,i+s);var l=r.createCanvas(n+a,i+s),f=l.getContext("2d");r.screen.setDefaults(f),e.render(f),f.globalCompositeOperation="destination-in",f.fillStyle=c.createPattern(h,"no-repeat"),f.fillRect(0,0,n+a,i+s),t.fillStyle=f.createPattern(l,"no-repeat"),t.fillRect(0,0,n+a,i+s),this.restoreStyles(e,u)}render(t){}}ne.ignoreStyles=["mask","transform","clip-path"];var ie=()=>{};class ae extends st{constructor(){super(...arguments),this.type="clipPath"}apply(t){var{document:e}=this,r=Reflect.getPrototypeOf(t),{beginPath:n,closePath:i}=t;r&&(r.beginPath=ie,r.closePath=ie),Reflect.apply(n,t,[]),this.children.forEach(n=>{if(void 0!==n.path){var a=void 0!==n.elementTransform?n.elementTransform():null;a||(a=at.fromElement(e,n)),a&&a.apply(t),n.path(t),r&&(r.closePath=i),a&&a.unapply(t)}}),Reflect.apply(i,t,[]),t.clip(),r&&(r.beginPath=n,r.closePath=i)}render(t){}}class se extends st{constructor(){super(...arguments),this.type="filter"}apply(t,e){var{document:r,children:n}=this,i=e.getBoundingBox(t);if(i){var a=0,s=0;n.forEach(t=>{var e=t.extraFilterDistance||0;a=Math.max(a,e),s=Math.max(s,e)});var o=Math.floor(i.width),u=Math.floor(i.height),h=o+2*a,c=u+2*s;if(!(h<1||c<1)){var l=Math.floor(i.x),f=Math.floor(i.y),g=this.removeStyles(e,se.ignoreStyles),p=r.createCanvas(h,c),d=p.getContext("2d");r.screen.setDefaults(d),d.translate(-l+a,-f+s),e.render(d),n.forEach(t=>{"function"==typeof t.apply&&t.apply(d,0,0,h,c)}),t.drawImage(p,0,0,h,c,l-a,f-s,h,c),this.restoreStyles(e,g)}}}render(t){}}se.ignoreStyles=["filter","transform","clip-path"];class oe extends st{constructor(t,e,r){super(t,e,r),this.type="feDropShadow",this.addStylesFromStyleDefinition()}apply(t,e,r,n,i){}}class ue extends st{constructor(){super(...arguments),this.type="feMorphology"}apply(t,e,r,n,i){}}class he extends st{constructor(){super(...arguments),this.type="feComposite"}apply(t,e,r,n,i){}}class ce extends st{constructor(t,e,r){super(t,e,r),this.type="feGaussianBlur",this.blurRadius=Math.floor(this.getAttribute("stdDeviation").getNumber()),this.extraFilterDistance=this.blurRadius}apply(t,e,r,n,i){var{document:a,blurRadius:s}=this,o=a.window?a.window.document.body:null,u=t.canvas;u.id=a.getUniqueId(),o&&(u.style.display="none",o.appendChild(u)),Object(f.canvasRGBA)(u,e,r,n,i,s),o&&o.removeChild(u)}}class le extends st{constructor(){super(...arguments),this.type="title"}}class fe extends st{constructor(){super(...arguments),this.type="desc"}}var ge={svg:bt,rect:St,circle:wt,ellipse:Tt,line:At,polyline:Ot,polygon:Pt,path:dt,pattern:Ct,marker:Et,defs:Mt,linearGradient:_t,radialGradient:Vt,stop:It,animate:kt,animateColor:Lt,animateTransform:Dt,font:Bt,"font-face":jt,"missing-glyph":Ft,glyph:vt,text:yt,tspan:mt,tref:zt,a:Ut,textPath:Yt,image:Wt,g:Nt,symbol:qt,style:$t,use:Zt,mask:ne,clipPath:ae,filter:se,feDropShadow:oe,feMorphology:ue,feComposite:he,feColorMatrix:re,feGaussianBlur:ce,title:le,desc:fe};function pe(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function de(){return(de=i()((function*(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.createElement("img");return e&&(r.crossOrigin="Anonymous"),new Promise((e,n)=>{r.onload=()=>{e(r)},r.onerror=(t,e,r,i,a)=>{n(a)},r.src=t})}))).apply(this,arguments)}class ve{constructor(t){var{rootEmSize:e=12,emSize:r=12,createCanvas:n=ve.createCanvas,createImage:i=ve.createImage,anonymousCrossOrigin:a}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.canvg=t,this.definitions={},this.styles={},this.stylesSpecificity={},this.images=[],this.fonts=[],this.emSizeStack=[],this.uniqueId=0,this.screen=t.screen,this.rootEmSize=e,this.emSize=r,this.createCanvas=n,this.createImage=this.bindCreateImage(i,a),this.screen.wait(this.isImagesLoaded.bind(this)),this.screen.wait(this.isFontsLoaded.bind(this))}bindCreateImage(t,e){return"boolean"==typeof e?(r,n)=>t(r,"boolean"==typeof n?n:e):t}get window(){return this.screen.window}get fetch(){return this.screen.fetch}get ctx(){return this.screen.ctx}get emSize(){var{emSizeStack:t}=this;return t[t.length-1]}set emSize(t){var{emSizeStack:e}=this;e.push(t)}popEmSize(){var{emSizeStack:t}=this;t.pop()}getUniqueId(){return"canvg".concat(++this.uniqueId)}isImagesLoaded(){return this.images.every(t=>t.loaded)}isFontsLoaded(){return this.fonts.every(t=>t.loaded)}createDocumentElement(t){var e=this.createElement(t.documentElement);return e.root=!0,e.addStylesFromStyleDefinition(),this.documentElement=e,e}createElement(t){var e=t.nodeName.replace(/^[^:]+:/,""),r=ve.elementTypes[e];return void 0!==r?new r(this,t):new ot(this,t)}createTextNode(t){return new xt(this,t)}setViewBox(t){this.screen.setViewBox(function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?pe(Object(r),!0).forEach((function(e){s()(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):pe(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}({document:this},t))}}function ye(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function me(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ye(Object(r),!0).forEach((function(e){s()(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ye(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}ve.createCanvas=function(t,e){var r=document.createElement("canvas");return r.width=t,r.height=e,r},ve.createImage=function(t){return de.apply(this,arguments)},ve.elementTypes=ge;class xe{constructor(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};this.parser=new Z(r),this.screen=new q(t,r),this.options=r;var n=new ve(this,r),i=n.createDocumentElement(e);this.document=n,this.documentElement=i}static from(t,e){var r=arguments;return i()((function*(){var n=r.length>2&&void 0!==r[2]?r[2]:{},i=new Z(n),a=yield i.parse(e);return new xe(t,a,n)}))()}static fromString(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=new Z(r).parseFromString(e);return new xe(t,n,r)}fork(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return xe.from(t,e,me(me({},this.options),r))}forkString(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return xe.fromString(t,e,me(me({},this.options),r))}ready(){return this.screen.ready()}isReady(){return this.screen.isReady()}render(){var t=arguments,e=this;return i()((function*(){var r=t.length>0&&void 0!==t[0]?t[0]:{};e.start(me({enableRedraw:!0,ignoreAnimation:!0,ignoreMouse:!0},r)),yield e.ready(),e.stop()}))()}start(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{documentElement:e,screen:r,options:n}=this;r.start(e,me(me({enableRedraw:!0},n),t))}stop(){this.screen.stop()}resize(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this.documentElement.resize(t,e,r)}}}.call(this,r(313))},1900:function(t,e,r){var n=r(1928),i=Function.prototype,a=i.bind,s=i.call,o=n&&a.bind(s,s);t.exports=n?function(t){return t&&o(t)}:function(t){return t&&function(){return s.apply(t,arguments)}}},1901:function(t,e){t.exports=function(t){return"function"==typeof t}},1902:function(t,e,r){(function(e){var r=function(t){return t&&t.Math==Math&&t};t.exports=r("object"==typeof globalThis&&globalThis)||r("object"==typeof window&&window)||r("object"==typeof self&&self)||r("object"==typeof e&&e)||function(){return this}()||Function("return this")()}).call(this,r(135))},1903:function(t,e){t.exports=function(t){try{return!!t()}catch(t){return!0}}},1904:function(t,e,r){var n=r(1902),i=r(1945),a=r(1908),s=r(1965),o=r(1964),u=r(1963),h=i("wks"),c=n.Symbol,l=c&&c.for,f=u?c:c&&c.withoutSetter||s;t.exports=function(t){if(!a(h,t)||!o&&"string"!=typeof h[t]){var e="Symbol."+t;o&&a(c,t)?h[t]=c[t]:h[t]=u&&l?l(e):f(e)}return h[t]}},1905:function(t,e,r){var n=r(1928),i=Function.prototype.call;t.exports=n?i.bind(i):function(){return i.apply(i,arguments)}},1906:function(t,e,r){var n=r(1914),i=String,a=TypeError;t.exports=function(t){if(n(t))return t;throw a(i(t)+" is not an object")}},1907:function(t,e,r){var n=r(1902),i=r(1921).f,a=r(1923),s=r(1917),o=r(1947),u=r(1995),h=r(1971);t.exports=function(t,e){var r,c,l,f,g,p=t.target,d=t.global,v=t.stat;if(r=d?n:v?n[p]||o(p,{}):(n[p]||{}).prototype)for(c in e){if(f=e[c],l=t.dontCallGetSet?(g=i(r,c))&&g.value:r[c],!h(d?c:p+(v?".":"#")+c,t.forced)&&void 0!==l){if(typeof f==typeof l)continue;u(f,l)}(t.sham||l&&l.sham)&&a(f,"sham",!0),s(r,c,f,t)}}},1908:function(t,e,r){var n=r(1900),i=r(1933),a=n({}.hasOwnProperty);t.exports=Object.hasOwn||function(t,e){return a(i(t),e)}},1909:function(t,e,r){var n=r(1903);t.exports=!n((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]}))},1910:function(t,e){var r=TypeError;t.exports=function(t){if(null==t)throw r("Can't call method on "+t);return t}},1911:function(t,e){t.exports=!1},1912:function(t,e,r){var n=r(1952),i=String;t.exports=function(t){if("Symbol"===n(t))throw TypeError("Cannot convert a Symbol value to a string");return i(t)}},1913:function(t,e,r){var n=r(1909),i=r(1966),a=r(1967),s=r(1906),o=r(1943),u=TypeError,h=Object.defineProperty,c=Object.getOwnPropertyDescriptor;e.f=n?a?function(t,e,r){if(s(t),e=o(e),s(r),"function"==typeof t&&"prototype"===e&&"value"in r&&"writable"in r&&!r.writable){var n=c(t,e);n&&n.writable&&(t[e]=r.value,r={configurable:"configurable"in r?r.configurable:n.configurable,enumerable:"enumerable"in r?r.enumerable:n.enumerable,writable:!1})}return h(t,e,r)}:h:function(t,e,r){if(s(t),e=o(e),s(r),i)try{return h(t,e,r)}catch(t){}if("get"in r||"set"in r)throw u("Accessors not supported");return"value"in r&&(t[e]=r.value),t}},1914:function(t,e,r){var n=r(1901);t.exports=function(t){return"object"==typeof t?null!==t:n(t)}},1915:function(t,e,r){var n=r(1902),i=r(1901),a=function(t){return i(t)?t:void 0};t.exports=function(t,e){return arguments.length<2?a(n[t]):n[t]&&n[t][e]}},1916:function(t,e,r){var n=r(1901),i=r(1932),a=TypeError;t.exports=function(t){if(n(t))return t;throw a(i(t)+" is not a function")}},1917:function(t,e,r){var n=r(1901),i=r(1913),a=r(1993),s=r(1947);t.exports=function(t,e,r,o){o||(o={});var u=o.enumerable,h=void 0!==o.name?o.name:e;if(n(r)&&a(r,h,o),o.global)u?t[e]=r:s(e,r);else{try{o.unsafe?t[e]&&(u=!0):delete t[e]}catch(t){}u?t[e]=r:i.f(t,e,{value:r,enumerable:!1,configurable:!o.nonConfigurable,writable:!o.nonWritable})}return t}},1918:function(t,e,r){var n=r(1900),i=n({}.toString),a=n("".slice);t.exports=function(t){return a(i(t),8,-1)}},1919:function(t,e,r){var n=r(1916);t.exports=function(t,e){var r=t[e];return null==r?void 0:n(r)}},1920:function(t,e,r){var n=r(1938),i=Math.min;t.exports=function(t){return t>0?i(n(t),9007199254740991):0}},1921:function(t,e,r){var n=r(1909),i=r(1905),a=r(1990),s=r(1929),o=r(1922),u=r(1943),h=r(1908),c=r(1966),l=Object.getOwnPropertyDescriptor;e.f=n?l:function(t,e){if(t=o(t),e=u(e),c)try{return l(t,e)}catch(t){}if(h(t,e))return s(!i(a.f,t,e),t[e])}},1922:function(t,e,r){var n=r(1961),i=r(1910);t.exports=function(t){return n(i(t))}},1923:function(t,e,r){var n=r(1909),i=r(1913),a=r(1929);t.exports=n?function(t,e,r){return i.f(t,e,a(1,r))}:function(t,e,r){return t[e]=r,t}},1924:function(t,e,r){var n=r(1902);t.exports=n.Promise},1925:function(t,e,r){var n=r(1902),i=r(1924),a=r(1901),s=r(1971),o=r(1936),u=r(1904),h=r(2013),c=r(1911),l=r(1944),f=i&&i.prototype,g=u("species"),p=!1,d=a(n.PromiseRejectionEvent),v=s("Promise",(function(){var t=o(i),e=t!==String(i);if(!e&&66===l)return!0;if(c&&(!f.catch||!f.finally))return!0;if(l>=51&&/native code/.test(t))return!1;var r=new i((function(t){t(1)})),n=function(t){t((function(){}),(function(){}))};return(r.constructor={})[g]=n,!(p=r.then((function(){}))instanceof n)||!e&&h&&!d}));t.exports={CONSTRUCTOR:v,REJECTION_EVENT:d,SUBCLASSING:p}},1926:function(t,e,r){"use strict";var n=r(1916),i=function(t){var e,r;this.promise=new t((function(t,n){if(void 0!==e||void 0!==r)throw TypeError("Bad Promise constructor");e=t,r=n})),this.resolve=n(e),this.reject=n(r)};t.exports.f=function(t){return new i(t)}},1927:function(t,e){t.exports={}},1928:function(t,e,r){var n=r(1903);t.exports=!n((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")}))},1929:function(t,e){t.exports=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}}},1930:function(t,e,r){var n=r(1900);t.exports=n({}.isPrototypeOf)},1931:function(t,e,r){var n=r(1915);t.exports=n("navigator","userAgent")||""},1932:function(t,e){var r=String;t.exports=function(t){try{return r(t)}catch(t){return"Object"}}},1933:function(t,e,r){var n=r(1910),i=Object;t.exports=function(t){return i(n(t))}},1934:function(t,e,r){var n=r(1902),i=r(1914),a=n.document,s=i(a)&&i(a.createElement);t.exports=function(t){return s?a.createElement(t):{}}},1935:function(t,e,r){var n=r(1909),i=r(1908),a=Function.prototype,s=n&&Object.getOwnPropertyDescriptor,o=i(a,"name"),u=o&&"something"===function(){}.name,h=o&&(!n||n&&s(a,"name").configurable);t.exports={EXISTS:o,PROPER:u,CONFIGURABLE:h}},1936:function(t,e,r){var n=r(1900),i=r(1901),a=r(1946),s=n(Function.toString);i(a.inspectSource)||(a.inspectSource=function(t){return s(t)}),t.exports=a.inspectSource},1937:function(t,e,r){var n,i,a,s=r(1994),o=r(1902),u=r(1900),h=r(1914),c=r(1923),l=r(1908),f=r(1946),g=r(1948),p=r(1949),d=o.TypeError,v=o.WeakMap;if(s||f.state){var y=f.state||(f.state=new v),m=u(y.get),x=u(y.has),b=u(y.set);n=function(t,e){if(x(y,t))throw new d("Object already initialized");return e.facade=t,b(y,t,e),e},i=function(t){return m(y,t)||{}},a=function(t){return x(y,t)}}else{var S=g("state");p[S]=!0,n=function(t,e){if(l(t,S))throw new d("Object already initialized");return e.facade=t,c(t,S,e),e},i=function(t){return l(t,S)?t[S]:{}},a=function(t){return l(t,S)}}t.exports={set:n,get:i,has:a,enforce:function(t){return a(t)?i(t):n(t,{})},getterFor:function(t){return function(e){var r;if(!h(e)||(r=i(e)).type!==t)throw d("Incompatible receiver, "+t+" required");return r}}}},1938:function(t,e,r){var n=r(1998);t.exports=function(t){var e=+t;return e!=e||0===e?0:n(e)}},1939:function(t,e,r){var n=r(1920);t.exports=function(t){return n(t.length)}},1940:function(t,e,r){var n=r(1918),i=r(1902);t.exports="process"==n(i.process)},1941:function(t,e,r){"use strict";var n,i,a=r(1905),s=r(1900),o=r(1912),u=r(1980),h=r(1981),c=r(1945),l=r(1942),f=r(1937).get,g=r(2029),p=r(2030),d=c("native-string-replace",String.prototype.replace),v=RegExp.prototype.exec,y=v,m=s("".charAt),x=s("".indexOf),b=s("".replace),S=s("".slice),w=(i=/b*/g,a(v,n=/a/,"a"),a(v,i,"a"),0!==n.lastIndex||0!==i.lastIndex),T=h.BROKEN_CARET,A=void 0!==/()??/.exec("")[1];(w||A||T||g||p)&&(y=function(t){var e,r,n,i,s,h,c,g=this,p=f(g),O=o(t),P=p.raw;if(P)return P.lastIndex=g.lastIndex,e=a(y,P,O),g.lastIndex=P.lastIndex,e;var C=p.groups,E=T&&g.sticky,M=a(u,g),N=g.source,R=0,_=O;if(E&&(M=b(M,"y",""),-1===x(M,"g")&&(M+="g"),_=S(O,g.lastIndex),g.lastIndex>0&&(!g.multiline||g.multiline&&"\n"!==m(O,g.lastIndex-1))&&(N="(?: "+N+")",_=" "+_,R++),r=new RegExp("^(?:"+N+")",M)),A&&(r=new RegExp("^"+N+"$(?!\\s)",M)),w&&(n=g.lastIndex),i=a(v,E?r:g,_),E?i?(i.input=S(i.input,R),i[0]=S(i[0],R),i.index=g.lastIndex,g.lastIndex+=i[0].length):g.lastIndex=0:w&&i&&(g.lastIndex=g.global?i.index+i[0].length:n),A&&i&&i.length>1&&a(d,i[0],r,(function(){for(s=1;s<arguments.length-2;s++)void 0===arguments[s]&&(i[s]=void 0)})),i&&C)for(i.groups=h=l(null),s=0;s<C.length;s++)h[(c=C[s])[0]]=i[c[1]];return i}),t.exports=y},1942:function(t,e,r){var n,i=r(1906),a=r(2027),s=r(1950),o=r(1949),u=r(1975),h=r(1934),c=r(1948),l=c("IE_PROTO"),f=function(){},g=function(t){return"<script>"+t+"<\/script>"},p=function(t){t.write(g("")),t.close();var e=t.parentWindow.Object;return t=null,e},d=function(){try{n=new ActiveXObject("htmlfile")}catch(t){}var t,e;d="undefined"!=typeof document?document.domain&&n?p(n):((e=h("iframe")).style.display="none",u.appendChild(e),e.src=String("javascript:"),(t=e.contentWindow.document).open(),t.write(g("document.F=Object")),t.close(),t.F):p(n);for(var r=s.length;r--;)delete d.prototype[s[r]];return d()};o[l]=!0,t.exports=Object.create||function(t,e){var r;return null!==t?(f.prototype=i(t),r=new f,f.prototype=null,r[l]=t):r=d(),void 0===e?r:a.f(r,e)}},1943:function(t,e,r){var n=r(1991),i=r(1962);t.exports=function(t){var e=n(t,"string");return i(e)?e:e+""}},1944:function(t,e,r){var n,i,a=r(1902),s=r(1931),o=a.process,u=a.Deno,h=o&&o.versions||u&&u.version,c=h&&h.v8;c&&(i=(n=c.split("."))[0]>0&&n[0]<4?1:+(n[0]+n[1])),!i&&s&&(!(n=s.match(/Edge\/(\d+)/))||n[1]>=74)&&(n=s.match(/Chrome\/(\d+)/))&&(i=+n[1]),t.exports=i},1945:function(t,e,r){var n=r(1911),i=r(1946);(t.exports=function(t,e){return i[t]||(i[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.23.4",mode:n?"pure":"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.23.4/LICENSE",source:"https://github.com/zloirock/core-js"})},1946:function(t,e,r){var n=r(1902),i=r(1947),a=n["__core-js_shared__"]||i("__core-js_shared__",{});t.exports=a},1947:function(t,e,r){var n=r(1902),i=Object.defineProperty;t.exports=function(t,e){try{i(n,t,{value:e,configurable:!0,writable:!0})}catch(r){n[t]=e}return e}},1948:function(t,e,r){var n=r(1945),i=r(1965),a=n("keys");t.exports=function(t){return a[t]||(a[t]=i(t))}},1949:function(t,e){t.exports={}},1950:function(t,e){t.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},1951:function(t,e,r){var n=r(1913).f,i=r(1908),a=r(1904)("toStringTag");t.exports=function(t,e,r){t&&!r&&(t=t.prototype),t&&!i(t,a)&&n(t,a,{configurable:!0,value:e})}},1952:function(t,e,r){var n=r(2005),i=r(1901),a=r(1918),s=r(1904)("toStringTag"),o=Object,u="Arguments"==a(function(){return arguments}());t.exports=n?a:function(t){var e,r,n;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,e){try{return t[e]}catch(t){}}(e=o(t),s))?r:u?a(e):"Object"==(n=a(e))&&i(e.callee)?"Arguments":n}},1953:function(t,e,r){var n=r(1928),i=Function.prototype,a=i.apply,s=i.call;t.exports="object"==typeof Reflect&&Reflect.apply||(n?s.bind(a):function(){return s.apply(a,arguments)})},1954:function(t,e,r){var n=r(1900),i=r(1916),a=r(1928),s=n(n.bind);t.exports=function(t,e){return i(t),void 0===e?t:a?s(t,e):function(){return t.apply(e,arguments)}}},1955:function(t,e){t.exports=function(t){try{return{error:!1,value:t()}}catch(t){return{error:!0,value:t}}}},1956:function(t,e,r){"use strict";r(2026);var n=r(1900),i=r(1917),a=r(1941),s=r(1903),o=r(1904),u=r(1923),h=o("species"),c=RegExp.prototype;t.exports=function(t,e,r,l){var f=o(t),g=!s((function(){var e={};return e[f]=function(){return 7},7!=""[t](e)})),p=g&&!s((function(){var e=!1,r=/a/;return"split"===t&&((r={}).constructor={},r.constructor[h]=function(){return r},r.flags="",r[f]=/./[f]),r.exec=function(){return e=!0,null},r[f](""),!e}));if(!g||!p||r){var d=n(/./[f]),v=e(f,""[t],(function(t,e,r,i,s){var o=n(t),u=e.exec;return u===a||u===c.exec?g&&!s?{done:!0,value:d(e,r,i)}:{done:!0,value:o(r,e,i)}:{done:!1}}));i(String.prototype,t,v[0]),i(c,f,v[1])}l&&u(c[f],"sham",!0)}},1957:function(t,e,r){"use strict";var n=r(2031).charAt;t.exports=function(t,e,r){return e+(r?n(t,e).length:1)}},1958:function(t,e,r){var n=r(1905),i=r(1906),a=r(1901),s=r(1918),o=r(1941),u=TypeError;t.exports=function(t,e){var r=t.exec;if(a(r)){var h=n(r,t,e);return null!==h&&i(h),h}if("RegExp"===s(t))return n(o,t,e);throw u("RegExp#exec called on incompatible receiver")}},1959:function(t,e,r){var n=r(1982),i=TypeError;t.exports=function(t){if(n(t))throw i("The method doesn't accept regular expressions");return t}},1960:function(t,e,r){var n=r(1904)("match");t.exports=function(t){var e=/./;try{"/./"[t](e)}catch(r){try{return e[n]=!1,"/./"[t](e)}catch(t){}}return!1}},1961:function(t,e,r){var n=r(1900),i=r(1903),a=r(1918),s=Object,o=n("".split);t.exports=i((function(){return!s("z").propertyIsEnumerable(0)}))?function(t){return"String"==a(t)?o(t,""):s(t)}:s},1962:function(t,e,r){var n=r(1915),i=r(1901),a=r(1930),s=r(1963),o=Object;t.exports=s?function(t){return"symbol"==typeof t}:function(t){var e=n("Symbol");return i(e)&&a(e.prototype,o(t))}},1963:function(t,e,r){var n=r(1964);t.exports=n&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},1964:function(t,e,r){var n=r(1944),i=r(1903);t.exports=!!Object.getOwnPropertySymbols&&!i((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&n&&n<41}))},1965:function(t,e,r){var n=r(1900),i=0,a=Math.random(),s=n(1..toString);t.exports=function(t){return"Symbol("+(void 0===t?"":t)+")_"+s(++i+a,36)}},1966:function(t,e,r){var n=r(1909),i=r(1903),a=r(1934);t.exports=!n&&!i((function(){return 7!=Object.defineProperty(a("div"),"a",{get:function(){return 7}}).a}))},1967:function(t,e,r){var n=r(1909),i=r(1903);t.exports=n&&i((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))},1968:function(t,e,r){var n=r(1900),i=r(1908),a=r(1922),s=r(1969).indexOf,o=r(1949),u=n([].push);t.exports=function(t,e){var r,n=a(t),h=0,c=[];for(r in n)!i(o,r)&&i(n,r)&&u(c,r);for(;e.length>h;)i(n,r=e[h++])&&(~s(c,r)||u(c,r));return c}},1969:function(t,e,r){var n=r(1922),i=r(1970),a=r(1939),s=function(t){return function(e,r,s){var o,u=n(e),h=a(u),c=i(s,h);if(t&&r!=r){for(;h>c;)if((o=u[c++])!=o)return!0}else for(;h>c;c++)if((t||c in u)&&u[c]===r)return t||c||0;return!t&&-1}};t.exports={includes:s(!0),indexOf:s(!1)}},1970:function(t,e,r){var n=r(1938),i=Math.max,a=Math.min;t.exports=function(t,e){var r=n(t);return r<0?i(r+e,0):a(r,e)}},1971:function(t,e,r){var n=r(1903),i=r(1901),a=/#|\.prototype\./,s=function(t,e){var r=u[o(t)];return r==c||r!=h&&(i(e)?n(e):!!e)},o=s.normalize=function(t){return String(t).replace(a,".").toLowerCase()},u=s.data={},h=s.NATIVE="N",c=s.POLYFILL="P";t.exports=s},1972:function(t,e,r){var n=r(1900),i=r(1906),a=r(2e3);t.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var t,e=!1,r={};try{(t=n(Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set))(r,[]),e=r instanceof Array}catch(t){}return function(r,n){return i(r),a(n),e?t(r,n):r.__proto__=n,r}}():void 0)},1973:function(t,e,r){var n=r(1906),i=r(2003),a=r(1904)("species");t.exports=function(t,e){var r,s=n(t).constructor;return void 0===s||null==(r=n(s)[a])?e:i(r)}},1974:function(t,e,r){var n,i,a,s,o=r(1902),u=r(1953),h=r(1954),c=r(1901),l=r(1908),f=r(1903),g=r(1975),p=r(2006),d=r(1934),v=r(2007),y=r(1976),m=r(1940),x=o.setImmediate,b=o.clearImmediate,S=o.process,w=o.Dispatch,T=o.Function,A=o.MessageChannel,O=o.String,P=0,C={};try{n=o.location}catch(t){}var E=function(t){if(l(C,t)){var e=C[t];delete C[t],e()}},M=function(t){return function(){E(t)}},N=function(t){E(t.data)},R=function(t){o.postMessage(O(t),n.protocol+"//"+n.host)};x&&b||(x=function(t){v(arguments.length,1);var e=c(t)?t:T(t),r=p(arguments,1);return C[++P]=function(){u(e,void 0,r)},i(P),P},b=function(t){delete C[t]},m?i=function(t){S.nextTick(M(t))}:w&&w.now?i=function(t){w.now(M(t))}:A&&!y?(s=(a=new A).port2,a.port1.onmessage=N,i=h(s.postMessage,s)):o.addEventListener&&c(o.postMessage)&&!o.importScripts&&n&&"file:"!==n.protocol&&!f(R)?(i=R,o.addEventListener("message",N,!1)):i="onreadystatechange"in d("script")?function(t){g.appendChild(d("script")).onreadystatechange=function(){g.removeChild(this),E(t)}}:function(t){setTimeout(M(t),0)}),t.exports={set:x,clear:b}},1975:function(t,e,r){var n=r(1915);t.exports=n("document","documentElement")},1976:function(t,e,r){var n=r(1931);t.exports=/(?:ipad|iphone|ipod).*applewebkit/i.test(n)},1977:function(t,e,r){var n=r(1954),i=r(1905),a=r(1906),s=r(1932),o=r(2015),u=r(1939),h=r(1930),c=r(2016),l=r(1978),f=r(2017),g=TypeError,p=function(t,e){this.stopped=t,this.result=e},d=p.prototype;t.exports=function(t,e,r){var v,y,m,x,b,S,w,T=r&&r.that,A=!(!r||!r.AS_ENTRIES),O=!(!r||!r.IS_RECORD),P=!(!r||!r.IS_ITERATOR),C=!(!r||!r.INTERRUPTED),E=n(e,T),M=function(t){return v&&f(v,"normal",t),new p(!0,t)},N=function(t){return A?(a(t),C?E(t[0],t[1],M):E(t[0],t[1])):C?E(t,M):E(t)};if(O)v=t.iterator;else if(P)v=t;else{if(!(y=l(t)))throw g(s(t)+" is not iterable");if(o(y)){for(m=0,x=u(t);x>m;m++)if((b=N(t[m]))&&h(d,b))return b;return new p(!1)}v=c(t,y)}for(S=O?t.next:v.next;!(w=i(S,v)).done;){try{b=N(w.value)}catch(t){f(v,"throw",t)}if("object"==typeof b&&b&&h(d,b))return b}return new p(!1)}},1978:function(t,e,r){var n=r(1952),i=r(1919),a=r(1927),s=r(1904)("iterator");t.exports=function(t){if(null!=t)return i(t,s)||i(t,"@@iterator")||a[n(t)]}},1979:function(t,e,r){var n=r(1924),i=r(2018),a=r(1925).CONSTRUCTOR;t.exports=a||!i((function(t){n.all(t).then(void 0,(function(){}))}))},1980:function(t,e,r){"use strict";var n=r(1906);t.exports=function(){var t=n(this),e="";return t.hasIndices&&(e+="d"),t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.dotAll&&(e+="s"),t.unicode&&(e+="u"),t.unicodeSets&&(e+="v"),t.sticky&&(e+="y"),e}},1981:function(t,e,r){var n=r(1903),i=r(1902).RegExp,a=n((function(){var t=i("a","y");return t.lastIndex=2,null!=t.exec("abcd")})),s=a||n((function(){return!i("a","y").sticky})),o=a||n((function(){var t=i("^r","gy");return t.lastIndex=2,null!=t.exec("str")}));t.exports={BROKEN_CARET:o,MISSED_STICKY:s,UNSUPPORTED_Y:a}},1982:function(t,e,r){var n=r(1914),i=r(1918),a=r(1904)("match");t.exports=function(t){var e;return n(t)&&(void 0!==(e=t[a])?!!e:"RegExp"==i(t))}},1983:function(t,e,r){"use strict";var n=r(1922),i=r(2035),a=r(1927),s=r(1937),o=r(1913).f,u=r(2036),h=r(1911),c=r(1909),l=s.set,f=s.getterFor("Array Iterator");t.exports=u(Array,"Array",(function(t,e){l(this,{type:"Array Iterator",target:n(t),index:0,kind:e})}),(function(){var t=f(this),e=t.target,r=t.kind,n=t.index++;return!e||n>=e.length?(t.target=void 0,{value:void 0,done:!0}):"keys"==r?{value:n,done:!1}:"values"==r?{value:e[n],done:!1}:{value:[n,e[n]],done:!1}}),"values");var g=a.Arguments=a.Array;if(i("keys"),i("values"),i("entries"),!h&&c&&"values"!==g.name)try{o(g,"name",{value:"values"})}catch(t){}},1984:function(t,e,r){"use strict";var n,i,a,s=r(1903),o=r(1901),u=r(1942),h=r(1985),c=r(1917),l=r(1904),f=r(1911),g=l("iterator"),p=!1;[].keys&&("next"in(a=[].keys())?(i=h(h(a)))!==Object.prototype&&(n=i):p=!0),null==n||s((function(){var t={};return n[g].call(t)!==t}))?n={}:f&&(n=u(n)),o(n[g])||c(n,g,(function(){return this})),t.exports={IteratorPrototype:n,BUGGY_SAFARI_ITERATORS:p}},1985:function(t,e,r){var n=r(1908),i=r(1901),a=r(1933),s=r(1948),o=r(2038),u=s("IE_PROTO"),h=Object,c=h.prototype;t.exports=o?h.getPrototypeOf:function(t){var e=a(t);if(n(e,u))return e[u];var r=e.constructor;return i(r)&&e instanceof r?r.prototype:e instanceof h?c:null}},1986:function(t,e,r){"use strict";var n=r(1903);t.exports=function(t,e){var r=[][t];return!!r&&n((function(){r.call(null,e||function(){return 1},1)}))}},1987:function(t,e){t.exports="\t\n\v\f\r                　\u2028\u2029\ufeff"},1988:function(t,e,r){r(1989),r(2014),r(2019),r(2020),r(2021),r(2022)},1989:function(t,e,r){"use strict";var n,i,a,s=r(1907),o=r(1911),u=r(1940),h=r(1902),c=r(1905),l=r(1917),f=r(1972),g=r(1951),p=r(2001),d=r(1916),v=r(1901),y=r(1914),m=r(2002),x=r(1973),b=r(1974).set,S=r(2008),w=r(2011),T=r(1955),A=r(2012),O=r(1937),P=r(1924),C=r(1925),E=r(1926),M=C.CONSTRUCTOR,N=C.REJECTION_EVENT,R=C.SUBCLASSING,_=O.getterFor("Promise"),V=O.set,I=P&&P.prototype,k=P,L=I,D=h.TypeError,B=h.document,j=h.process,F=E.f,z=F,U=!!(B&&B.createEvent&&h.dispatchEvent),H=function(t){var e;return!(!y(t)||!v(e=t.then))&&e},X=function(t,e){var r,n,i,a=e.value,s=1==e.state,o=s?t.ok:t.fail,u=t.resolve,h=t.reject,l=t.domain;try{o?(s||(2===e.rejection&&Q(e),e.rejection=1),!0===o?r=a:(l&&l.enter(),r=o(a),l&&(l.exit(),i=!0)),r===t.promise?h(D("Promise-chain cycle")):(n=H(r))?c(n,r,u,h):u(r)):h(a)}catch(t){l&&!i&&l.exit(),h(t)}},Y=function(t,e){t.notified||(t.notified=!0,S((function(){for(var r,n=t.reactions;r=n.get();)X(r,t);t.notified=!1,e&&!t.rejection&&W(t)})))},G=function(t,e,r){var n,i;U?((n=B.createEvent("Event")).promise=e,n.reason=r,n.initEvent(t,!1,!0),h.dispatchEvent(n)):n={promise:e,reason:r},!N&&(i=h["on"+t])?i(n):"unhandledrejection"===t&&w("Unhandled promise rejection",r)},W=function(t){c(b,h,(function(){var e,r=t.facade,n=t.value;if(q(t)&&(e=T((function(){u?j.emit("unhandledRejection",n,r):G("unhandledrejection",r,n)})),t.rejection=u||q(t)?2:1,e.error))throw e.value}))},q=function(t){return 1!==t.rejection&&!t.parent},Q=function(t){c(b,h,(function(){var e=t.facade;u?j.emit("rejectionHandled",e):G("rejectionhandled",e,t.value)}))},$=function(t,e,r){return function(n){t(e,n,r)}},Z=function(t,e,r){t.done||(t.done=!0,r&&(t=r),t.value=e,t.state=2,Y(t,!0))},K=function(t,e,r){if(!t.done){t.done=!0,r&&(t=r);try{if(t.facade===e)throw D("Promise can't be resolved itself");var n=H(e);n?S((function(){var r={done:!1};try{c(n,e,$(K,r,t),$(Z,r,t))}catch(e){Z(r,e,t)}})):(t.value=e,t.state=1,Y(t,!1))}catch(e){Z({done:!1},e,t)}}};if(M&&(L=(k=function(t){m(this,L),d(t),c(n,this);var e=_(this);try{t($(K,e),$(Z,e))}catch(t){Z(e,t)}}).prototype,(n=function(t){V(this,{type:"Promise",done:!1,notified:!1,parent:!1,reactions:new A,rejection:!1,state:0,value:void 0})}).prototype=l(L,"then",(function(t,e){var r=_(this),n=F(x(this,k));return r.parent=!0,n.ok=!v(t)||t,n.fail=v(e)&&e,n.domain=u?j.domain:void 0,0==r.state?r.reactions.add(n):S((function(){X(n,r)})),n.promise})),i=function(){var t=new n,e=_(t);this.promise=t,this.resolve=$(K,e),this.reject=$(Z,e)},E.f=F=function(t){return t===k||void 0===t?new i(t):z(t)},!o&&v(P)&&I!==Object.prototype)){a=I.then,R||l(I,"then",(function(t,e){var r=this;return new k((function(t,e){c(a,r,t,e)})).then(t,e)}),{unsafe:!0});try{delete I.constructor}catch(t){}f&&f(I,L)}s({global:!0,constructor:!0,wrap:!0,forced:M},{Promise:k}),g(k,"Promise",!1,!0),p("Promise")},1990:function(t,e,r){"use strict";var n={}.propertyIsEnumerable,i=Object.getOwnPropertyDescriptor,a=i&&!n.call({1:2},1);e.f=a?function(t){var e=i(this,t);return!!e&&e.enumerable}:n},1991:function(t,e,r){var n=r(1905),i=r(1914),a=r(1962),s=r(1919),o=r(1992),u=r(1904),h=TypeError,c=u("toPrimitive");t.exports=function(t,e){if(!i(t)||a(t))return t;var r,u=s(t,c);if(u){if(void 0===e&&(e="default"),r=n(u,t,e),!i(r)||a(r))return r;throw h("Can't convert object to primitive value")}return void 0===e&&(e="number"),o(t,e)}},1992:function(t,e,r){var n=r(1905),i=r(1901),a=r(1914),s=TypeError;t.exports=function(t,e){var r,o;if("string"===e&&i(r=t.toString)&&!a(o=n(r,t)))return o;if(i(r=t.valueOf)&&!a(o=n(r,t)))return o;if("string"!==e&&i(r=t.toString)&&!a(o=n(r,t)))return o;throw s("Can't convert object to primitive value")}},1993:function(t,e,r){var n=r(1903),i=r(1901),a=r(1908),s=r(1909),o=r(1935).CONFIGURABLE,u=r(1936),h=r(1937),c=h.enforce,l=h.get,f=Object.defineProperty,g=s&&!n((function(){return 8!==f((function(){}),"length",{value:8}).length})),p=String(String).split("String"),d=t.exports=function(t,e,r){"Symbol("===String(e).slice(0,7)&&(e="["+String(e).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),r&&r.getter&&(e="get "+e),r&&r.setter&&(e="set "+e),(!a(t,"name")||o&&t.name!==e)&&(s?f(t,"name",{value:e,configurable:!0}):t.name=e),g&&r&&a(r,"arity")&&t.length!==r.arity&&f(t,"length",{value:r.arity});try{r&&a(r,"constructor")&&r.constructor?s&&f(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var n=c(t);return a(n,"source")||(n.source=p.join("string"==typeof e?e:"")),t};Function.prototype.toString=d((function(){return i(this)&&l(this).source||u(this)}),"toString")},1994:function(t,e,r){var n=r(1902),i=r(1901),a=r(1936),s=n.WeakMap;t.exports=i(s)&&/native code/.test(a(s))},1995:function(t,e,r){var n=r(1908),i=r(1996),a=r(1921),s=r(1913);t.exports=function(t,e,r){for(var o=i(e),u=s.f,h=a.f,c=0;c<o.length;c++){var l=o[c];n(t,l)||r&&n(r,l)||u(t,l,h(e,l))}}},1996:function(t,e,r){var n=r(1915),i=r(1900),a=r(1997),s=r(1999),o=r(1906),u=i([].concat);t.exports=n("Reflect","ownKeys")||function(t){var e=a.f(o(t)),r=s.f;return r?u(e,r(t)):e}},1997:function(t,e,r){var n=r(1968),i=r(1950).concat("length","prototype");e.f=Object.getOwnPropertyNames||function(t){return n(t,i)}},1998:function(t,e){var r=Math.ceil,n=Math.floor;t.exports=Math.trunc||function(t){var e=+t;return(e>0?n:r)(e)}},1999:function(t,e){e.f=Object.getOwnPropertySymbols},2e3:function(t,e,r){var n=r(1901),i=String,a=TypeError;t.exports=function(t){if("object"==typeof t||n(t))return t;throw a("Can't set "+i(t)+" as a prototype")}},2001:function(t,e,r){"use strict";var n=r(1915),i=r(1913),a=r(1904),s=r(1909),o=a("species");t.exports=function(t){var e=n(t),r=i.f;s&&e&&!e[o]&&r(e,o,{configurable:!0,get:function(){return this}})}},2002:function(t,e,r){var n=r(1930),i=TypeError;t.exports=function(t,e){if(n(e,t))return t;throw i("Incorrect invocation")}},2003:function(t,e,r){var n=r(2004),i=r(1932),a=TypeError;t.exports=function(t){if(n(t))return t;throw a(i(t)+" is not a constructor")}},2004:function(t,e,r){var n=r(1900),i=r(1903),a=r(1901),s=r(1952),o=r(1915),u=r(1936),h=function(){},c=[],l=o("Reflect","construct"),f=/^\s*(?:class|function)\b/,g=n(f.exec),p=!f.exec(h),d=function(t){if(!a(t))return!1;try{return l(h,c,t),!0}catch(t){return!1}},v=function(t){if(!a(t))return!1;switch(s(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return p||!!g(f,u(t))}catch(t){return!0}};v.sham=!0,t.exports=!l||i((function(){var t;return d(d.call)||!d(Object)||!d((function(){t=!0}))||t}))?v:d},2005:function(t,e,r){var n={};n[r(1904)("toStringTag")]="z",t.exports="[object z]"===String(n)},2006:function(t,e,r){var n=r(1900);t.exports=n([].slice)},2007:function(t,e){var r=TypeError;t.exports=function(t,e){if(t<e)throw r("Not enough arguments");return t}},2008:function(t,e,r){var n,i,a,s,o,u,h,c,l=r(1902),f=r(1954),g=r(1921).f,p=r(1974).set,d=r(1976),v=r(2009),y=r(2010),m=r(1940),x=l.MutationObserver||l.WebKitMutationObserver,b=l.document,S=l.process,w=l.Promise,T=g(l,"queueMicrotask"),A=T&&T.value;A||(n=function(){var t,e;for(m&&(t=S.domain)&&t.exit();i;){e=i.fn,i=i.next;try{e()}catch(t){throw i?s():a=void 0,t}}a=void 0,t&&t.enter()},d||m||y||!x||!b?!v&&w&&w.resolve?((h=w.resolve(void 0)).constructor=w,c=f(h.then,h),s=function(){c(n)}):m?s=function(){S.nextTick(n)}:(p=f(p,l),s=function(){p(n)}):(o=!0,u=b.createTextNode(""),new x(n).observe(u,{characterData:!0}),s=function(){u.data=o=!o})),t.exports=A||function(t){var e={fn:t,next:void 0};a&&(a.next=e),i||(i=e,s()),a=e}},2009:function(t,e,r){var n=r(1931),i=r(1902);t.exports=/ipad|iphone|ipod/i.test(n)&&void 0!==i.Pebble},2010:function(t,e,r){var n=r(1931);t.exports=/web0s(?!.*chrome)/i.test(n)},2011:function(t,e,r){var n=r(1902);t.exports=function(t,e){var r=n.console;r&&r.error&&(1==arguments.length?r.error(t):r.error(t,e))}},2012:function(t,e){var r=function(){this.head=null,this.tail=null};r.prototype={add:function(t){var e={item:t,next:null};this.head?this.tail.next=e:this.head=e,this.tail=e},get:function(){var t=this.head;if(t)return this.head=t.next,this.tail===t&&(this.tail=null),t.item}},t.exports=r},2013:function(t,e){t.exports="object"==typeof window&&"object"!=typeof Deno},2014:function(t,e,r){"use strict";var n=r(1907),i=r(1905),a=r(1916),s=r(1926),o=r(1955),u=r(1977);n({target:"Promise",stat:!0,forced:r(1979)},{all:function(t){var e=this,r=s.f(e),n=r.resolve,h=r.reject,c=o((function(){var r=a(e.resolve),s=[],o=0,c=1;u(t,(function(t){var a=o++,u=!1;c++,i(r,e,t).then((function(t){u||(u=!0,s[a]=t,--c||n(s))}),h)})),--c||n(s)}));return c.error&&h(c.value),r.promise}})},2015:function(t,e,r){var n=r(1904),i=r(1927),a=n("iterator"),s=Array.prototype;t.exports=function(t){return void 0!==t&&(i.Array===t||s[a]===t)}},2016:function(t,e,r){var n=r(1905),i=r(1916),a=r(1906),s=r(1932),o=r(1978),u=TypeError;t.exports=function(t,e){var r=arguments.length<2?o(t):e;if(i(r))return a(n(r,t));throw u(s(t)+" is not iterable")}},2017:function(t,e,r){var n=r(1905),i=r(1906),a=r(1919);t.exports=function(t,e,r){var s,o;i(t);try{if(!(s=a(t,"return"))){if("throw"===e)throw r;return r}s=n(s,t)}catch(t){o=!0,s=t}if("throw"===e)throw r;if(o)throw s;return i(s),r}},2018:function(t,e,r){var n=r(1904)("iterator"),i=!1;try{var a=0,s={next:function(){return{done:!!a++}},return:function(){i=!0}};s[n]=function(){return this},Array.from(s,(function(){throw 2}))}catch(t){}t.exports=function(t,e){if(!e&&!i)return!1;var r=!1;try{var a={};a[n]=function(){return{next:function(){return{done:r=!0}}}},t(a)}catch(t){}return r}},2019:function(t,e,r){"use strict";var n=r(1907),i=r(1911),a=r(1925).CONSTRUCTOR,s=r(1924),o=r(1915),u=r(1901),h=r(1917),c=s&&s.prototype;if(n({target:"Promise",proto:!0,forced:a,real:!0},{catch:function(t){return this.then(void 0,t)}}),!i&&u(s)){var l=o("Promise").prototype.catch;c.catch!==l&&h(c,"catch",l,{unsafe:!0})}},2020:function(t,e,r){"use strict";var n=r(1907),i=r(1905),a=r(1916),s=r(1926),o=r(1955),u=r(1977);n({target:"Promise",stat:!0,forced:r(1979)},{race:function(t){var e=this,r=s.f(e),n=r.reject,h=o((function(){var s=a(e.resolve);u(t,(function(t){i(s,e,t).then(r.resolve,n)}))}));return h.error&&n(h.value),r.promise}})},2021:function(t,e,r){"use strict";var n=r(1907),i=r(1905),a=r(1926);n({target:"Promise",stat:!0,forced:r(1925).CONSTRUCTOR},{reject:function(t){var e=a.f(this);return i(e.reject,void 0,t),e.promise}})},2022:function(t,e,r){"use strict";var n=r(1907),i=r(1915),a=r(1911),s=r(1924),o=r(1925).CONSTRUCTOR,u=r(2023),h=i("Promise"),c=a&&!o;n({target:"Promise",stat:!0,forced:a||o},{resolve:function(t){return u(c&&this===h?s:this,t)}})},2023:function(t,e,r){var n=r(1906),i=r(1914),a=r(1926);t.exports=function(t,e){if(n(t),i(e)&&e.constructor===t)return e;var r=a.f(t);return(0,r.resolve)(e),r.promise}},2024:function(t,e){function r(t,e,r,n,i,a,s){try{var o=t[a](s),u=o.value}catch(t){return void r(t)}o.done?e(u):Promise.resolve(u).then(n,i)}t.exports=function(t){return function(){var e=this,n=arguments;return new Promise((function(i,a){var s=t.apply(e,n);function o(t){r(s,i,a,o,u,"next",t)}function u(t){r(s,i,a,o,u,"throw",t)}o(void 0)}))}},t.exports.__esModule=!0,t.exports.default=t.exports},2025:function(t,e,r){"use strict";var n=r(1905),i=r(1956),a=r(1906),s=r(1920),o=r(1912),u=r(1910),h=r(1919),c=r(1957),l=r(1958);i("match",(function(t,e,r){return[function(e){var r=u(this),i=null==e?void 0:h(e,t);return i?n(i,e,r):new RegExp(e)[t](o(r))},function(t){var n=a(this),i=o(t),u=r(e,n,i);if(u.done)return u.value;if(!n.global)return l(n,i);var h=n.unicode;n.lastIndex=0;for(var f,g=[],p=0;null!==(f=l(n,i));){var d=o(f[0]);g[p]=d,""===d&&(n.lastIndex=c(i,s(n.lastIndex),h)),p++}return 0===p?null:g}]}))},2026:function(t,e,r){"use strict";var n=r(1907),i=r(1941);n({target:"RegExp",proto:!0,forced:/./.exec!==i},{exec:i})},2027:function(t,e,r){var n=r(1909),i=r(1967),a=r(1913),s=r(1906),o=r(1922),u=r(2028);e.f=n&&!i?Object.defineProperties:function(t,e){s(t);for(var r,n=o(e),i=u(e),h=i.length,c=0;h>c;)a.f(t,r=i[c++],n[r]);return t}},2028:function(t,e,r){var n=r(1968),i=r(1950);t.exports=Object.keys||function(t){return n(t,i)}},2029:function(t,e,r){var n=r(1903),i=r(1902).RegExp;t.exports=n((function(){var t=i(".","s");return!(t.dotAll&&t.exec("\n")&&"s"===t.flags)}))},2030:function(t,e,r){var n=r(1903),i=r(1902).RegExp;t.exports=n((function(){var t=i("(?<a>b)","g");return"b"!==t.exec("b").groups.a||"bc"!=="b".replace(t,"$<a>c")}))},2031:function(t,e,r){var n=r(1900),i=r(1938),a=r(1912),s=r(1910),o=n("".charAt),u=n("".charCodeAt),h=n("".slice),c=function(t){return function(e,r){var n,c,l=a(s(e)),f=i(r),g=l.length;return f<0||f>=g?t?"":void 0:(n=u(l,f))<55296||n>56319||f+1===g||(c=u(l,f+1))<56320||c>57343?t?o(l,f):n:t?h(l,f,f+2):c-56320+(n-55296<<10)+65536}};t.exports={codeAt:c(!1),charAt:c(!0)}},2032:function(t,e,r){"use strict";var n=r(1953),i=r(1905),a=r(1900),s=r(1956),o=r(1903),u=r(1906),h=r(1901),c=r(1938),l=r(1920),f=r(1912),g=r(1910),p=r(1957),d=r(1919),v=r(2033),y=r(1958),m=r(1904)("replace"),x=Math.max,b=Math.min,S=a([].concat),w=a([].push),T=a("".indexOf),A=a("".slice),O="$0"==="a".replace(/./,"$0"),P=!!/./[m]&&""===/./[m]("a","$0");s("replace",(function(t,e,r){var a=P?"$":"$0";return[function(t,r){var n=g(this),a=null==t?void 0:d(t,m);return a?i(a,t,n,r):i(e,f(n),t,r)},function(t,i){var s=u(this),o=f(t);if("string"==typeof i&&-1===T(i,a)&&-1===T(i,"$<")){var g=r(e,s,o,i);if(g.done)return g.value}var d=h(i);d||(i=f(i));var m=s.global;if(m){var O=s.unicode;s.lastIndex=0}for(var P=[];;){var C=y(s,o);if(null===C)break;if(w(P,C),!m)break;""===f(C[0])&&(s.lastIndex=p(o,l(s.lastIndex),O))}for(var E,M="",N=0,R=0;R<P.length;R++){for(var _=f((C=P[R])[0]),V=x(b(c(C.index),o.length),0),I=[],k=1;k<C.length;k++)w(I,void 0===(E=C[k])?E:String(E));var L=C.groups;if(d){var D=S([_],I,V,o);void 0!==L&&w(D,L);var B=f(n(i,void 0,D))}else B=v(_,o,V,I,L,i);V>=N&&(M+=A(o,N,V)+B,N=V+_.length)}return M+A(o,N)}]}),!!o((function(){var t=/./;return t.exec=function(){var t=[];return t.groups={a:"7"},t},"7"!=="".replace(t,"$<a>")}))||!O||P)},2033:function(t,e,r){var n=r(1900),i=r(1933),a=Math.floor,s=n("".charAt),o=n("".replace),u=n("".slice),h=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,c=/\$([$&'`]|\d{1,2})/g;t.exports=function(t,e,r,n,l,f){var g=r+t.length,p=n.length,d=c;return void 0!==l&&(l=i(l),d=h),o(f,d,(function(i,o){var h;switch(s(o,0)){case"$":return"$";case"&":return t;case"`":return u(e,0,r);case"'":return u(e,g);case"<":h=l[u(o,1,-1)];break;default:var c=+o;if(0===c)return i;if(c>p){var f=a(c/10);return 0===f?i:f<=p?void 0===n[f-1]?s(o,1):n[f-1]+s(o,1):i}h=n[c-1]}return void 0===h?"":h}))}},2034:function(t,e,r){"use strict";var n,i=r(1907),a=r(1900),s=r(1921).f,o=r(1920),u=r(1912),h=r(1959),c=r(1910),l=r(1960),f=r(1911),g=a("".startsWith),p=a("".slice),d=Math.min,v=l("startsWith");i({target:"String",proto:!0,forced:!!(f||v||(n=s(String.prototype,"startsWith"),!n||n.writable))&&!v},{startsWith:function(t){var e=u(c(this));h(t);var r=o(d(arguments.length>1?arguments[1]:void 0,e.length)),n=u(t);return g?g(e,n,r):p(e,r,r+n.length)===n}})},2035:function(t,e,r){var n=r(1904),i=r(1942),a=r(1913).f,s=n("unscopables"),o=Array.prototype;null==o[s]&&a(o,s,{configurable:!0,value:i(null)}),t.exports=function(t){o[s][t]=!0}},2036:function(t,e,r){"use strict";var n=r(1907),i=r(1905),a=r(1911),s=r(1935),o=r(1901),u=r(2037),h=r(1985),c=r(1972),l=r(1951),f=r(1923),g=r(1917),p=r(1904),d=r(1927),v=r(1984),y=s.PROPER,m=s.CONFIGURABLE,x=v.IteratorPrototype,b=v.BUGGY_SAFARI_ITERATORS,S=p("iterator"),w=function(){return this};t.exports=function(t,e,r,s,p,v,T){u(r,e,s);var A,O,P,C=function(t){if(t===p&&_)return _;if(!b&&t in N)return N[t];switch(t){case"keys":case"values":case"entries":return function(){return new r(this,t)}}return function(){return new r(this)}},E=e+" Iterator",M=!1,N=t.prototype,R=N[S]||N["@@iterator"]||p&&N[p],_=!b&&R||C(p),V="Array"==e&&N.entries||R;if(V&&(A=h(V.call(new t)))!==Object.prototype&&A.next&&(a||h(A)===x||(c?c(A,x):o(A[S])||g(A,S,w)),l(A,E,!0,!0),a&&(d[E]=w)),y&&"values"==p&&R&&"values"!==R.name&&(!a&&m?f(N,"name","values"):(M=!0,_=function(){return i(R,this)})),p)if(O={values:C("values"),keys:v?_:C("keys"),entries:C("entries")},T)for(P in O)(b||M||!(P in N))&&g(N,P,O[P]);else n({target:e,proto:!0,forced:b||M},O);return a&&!T||N[S]===_||g(N,S,_,{name:p}),d[e]=_,O}},2037:function(t,e,r){"use strict";var n=r(1984).IteratorPrototype,i=r(1942),a=r(1929),s=r(1951),o=r(1927),u=function(){return this};t.exports=function(t,e,r,h){var c=e+" Iterator";return t.prototype=i(n,{next:a(+!h,r)}),s(t,c,!1,!0),o[c]=u,t}},2038:function(t,e,r){var n=r(1903);t.exports=!n((function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype}))},2039:function(t,e,r){var n=r(1902),i=r(2040),a=r(2041),s=r(1983),o=r(1923),u=r(1904),h=u("iterator"),c=u("toStringTag"),l=s.values,f=function(t,e){if(t){if(t[h]!==l)try{o(t,h,l)}catch(e){t[h]=l}if(t[c]||o(t,c,e),i[e])for(var r in s)if(t[r]!==s[r])try{o(t,r,s[r])}catch(e){t[r]=s[r]}}};for(var g in i)f(n[g]&&n[g].prototype,g);f(a,"DOMTokenList")},2040:function(t,e){t.exports={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}},2041:function(t,e,r){var n=r(1934)("span").classList,i=n&&n.constructor&&n.constructor.prototype;t.exports=i===Object.prototype?void 0:i},2042:function(t,e){t.exports=function(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t},t.exports.__esModule=!0,t.exports.default=t.exports},2043:function(t,e,r){"use strict";var n=r(1907),i=r(2044).left,a=r(1986),s=r(1944),o=r(1940);n({target:"Array",proto:!0,forced:!a("reduce")||!o&&s>79&&s<83},{reduce:function(t){var e=arguments.length;return i(this,t,e,e>1?arguments[1]:void 0)}})},2044:function(t,e,r){var n=r(1916),i=r(1933),a=r(1961),s=r(1939),o=TypeError,u=function(t){return function(e,r,u,h){n(r);var c=i(e),l=a(c),f=s(c),g=t?f-1:0,p=t?-1:1;if(u<2)for(;;){if(g in l){h=l[g],g+=p;break}if(g+=p,t?g<0:f<=g)throw o("Reduce of empty array with no initial value")}for(;t?g>=0:f>g;g+=p)g in l&&(h=r(h,l[g],g,c));return h}};t.exports={left:u(!1),right:u(!0)}},2045:function(t,e,r){"use strict";var n,i=r(1907),a=r(1900),s=r(1921).f,o=r(1920),u=r(1912),h=r(1959),c=r(1910),l=r(1960),f=r(1911),g=a("".endsWith),p=a("".slice),d=Math.min,v=l("endsWith");i({target:"String",proto:!0,forced:!!(f||v||(n=s(String.prototype,"endsWith"),!n||n.writable))&&!v},{endsWith:function(t){var e=u(c(this));h(t);var r=arguments.length>1?arguments[1]:void 0,n=e.length,i=void 0===r?n:d(o(r),n),a=u(t);return g?g(e,a,i):p(e,i-a.length,i)===a}})},2046:function(t,e,r){"use strict";var n=r(1953),i=r(1905),a=r(1900),s=r(1956),o=r(1982),u=r(1906),h=r(1910),c=r(1973),l=r(1957),f=r(1920),g=r(1912),p=r(1919),d=r(2047),v=r(1958),y=r(1941),m=r(1981),x=r(1903),b=m.UNSUPPORTED_Y,S=Math.min,w=[].push,T=a(/./.exec),A=a(w),O=a("".slice);s("split",(function(t,e,r){var a;return a="c"=="abbc".split(/(b)*/)[1]||4!="test".split(/(?:)/,-1).length||2!="ab".split(/(?:ab)*/).length||4!=".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length?function(t,r){var a=g(h(this)),s=void 0===r?4294967295:r>>>0;if(0===s)return[];if(void 0===t)return[a];if(!o(t))return i(e,a,t,s);for(var u,c,l,f=[],p=(t.ignoreCase?"i":"")+(t.multiline?"m":"")+(t.unicode?"u":"")+(t.sticky?"y":""),v=0,m=new RegExp(t.source,p+"g");(u=i(y,m,a))&&!((c=m.lastIndex)>v&&(A(f,O(a,v,u.index)),u.length>1&&u.index<a.length&&n(w,f,d(u,1)),l=u[0].length,v=c,f.length>=s));)m.lastIndex===u.index&&m.lastIndex++;return v===a.length?!l&&T(m,"")||A(f,""):A(f,O(a,v)),f.length>s?d(f,0,s):f}:"0".split(void 0,0).length?function(t,r){return void 0===t&&0===r?[]:i(e,this,t,r)}:e,[function(e,r){var n=h(this),s=null==e?void 0:p(e,t);return s?i(s,e,n,r):i(a,g(n),e,r)},function(t,n){var i=u(this),s=g(t),o=r(a,i,s,n,a!==e);if(o.done)return o.value;var h=c(i,RegExp),p=i.unicode,d=(i.ignoreCase?"i":"")+(i.multiline?"m":"")+(i.unicode?"u":"")+(b?"g":"y"),y=new h(b?"^(?:"+i.source+")":i,d),m=void 0===n?4294967295:n>>>0;if(0===m)return[];if(0===s.length)return null===v(y,s)?[s]:[];for(var x=0,w=0,T=[];w<s.length;){y.lastIndex=b?0:w;var P,C=v(y,b?O(s,w):s);if(null===C||(P=S(f(y.lastIndex+(b?w:0)),s.length))===x)w=l(s,w,p);else{if(A(T,O(s,x,w)),T.length===m)return T;for(var E=1;E<=C.length-1;E++)if(A(T,C[E]),T.length===m)return T;w=x=P}}return A(T,O(s,x)),T}]}),!!x((function(){var t=/(?:)/,e=t.exec;t.exec=function(){return e.apply(this,arguments)};var r="ab".split(t);return 2!==r.length||"a"!==r[0]||"b"!==r[1]})),b)},2047:function(t,e,r){var n=r(1970),i=r(1939),a=r(2048),s=Array,o=Math.max;t.exports=function(t,e,r){for(var u=i(t),h=n(e,u),c=n(void 0===r?u:r,u),l=s(o(c-h,0)),f=0;h<c;h++,f++)a(l,f,t[h]);return l.length=f,l}},2048:function(t,e,r){"use strict";var n=r(1943),i=r(1913),a=r(1929);t.exports=function(t,e,r){var s=n(e);s in t?i.f(t,s,a(0,r)):t[s]=r}},2049:function(t,e,r){"use strict";var n=r(1907),i=r(2050).trim;n({target:"String",proto:!0,forced:r(2051)("trim")},{trim:function(){return i(this)}})},2050:function(t,e,r){var n=r(1900),i=r(1910),a=r(1912),s=r(1987),o=n("".replace),u="["+s+"]",h=RegExp("^"+u+u+"*"),c=RegExp(u+u+"*$"),l=function(t){return function(e){var r=a(i(e));return 1&t&&(r=o(r,h,"")),2&t&&(r=o(r,c,"")),r}};t.exports={start:l(1),end:l(2),trim:l(3)}},2051:function(t,e,r){var n=r(1935).PROPER,i=r(1903),a=r(1987);t.exports=function(t){return i((function(){return!!a[t]()||"​᠎"!=="​᠎"[t]()||n&&a[t].name!==t}))}},2052:function(t,e){t.exports=function(t){this.ok=!1,this.alpha=1,"#"==t.charAt(0)&&(t=t.substr(1,6)),t=(t=t.replace(/ /g,"")).toLowerCase();var e={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"00ffff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000000",blanchedalmond:"ffebcd",blue:"0000ff",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"00ffff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dodgerblue:"1e90ff",feldspar:"d19275",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"ff00ff",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgrey:"d3d3d3",lightgreen:"90ee90",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslateblue:"8470ff",lightslategray:"778899",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"00ff00",limegreen:"32cd32",linen:"faf0e6",magenta:"ff00ff",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370d8",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"d87093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"663399",red:"ff0000",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",violetred:"d02090",wheat:"f5deb3",white:"ffffff",whitesmoke:"f5f5f5",yellow:"ffff00",yellowgreen:"9acd32"};t=e[t]||t;for(var r=[{re:/^rgba\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3}),\s*((?:\d?\.)?\d)\)$/,example:["rgba(123, 234, 45, 0.8)","rgba(255,234,245,1.0)"],process:function(t){return[parseInt(t[1]),parseInt(t[2]),parseInt(t[3]),parseFloat(t[4])]}},{re:/^rgb\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)$/,example:["rgb(123, 234, 45)","rgb(255,234,245)"],process:function(t){return[parseInt(t[1]),parseInt(t[2]),parseInt(t[3])]}},{re:/^([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,example:["#00ff00","336699"],process:function(t){return[parseInt(t[1],16),parseInt(t[2],16),parseInt(t[3],16)]}},{re:/^([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,example:["#fb0","f0f"],process:function(t){return[parseInt(t[1]+t[1],16),parseInt(t[2]+t[2],16),parseInt(t[3]+t[3],16)]}}],n=0;n<r.length;n++){var i=r[n].re,a=r[n].process,s=i.exec(t);if(s){var o=a(s);this.r=o[0],this.g=o[1],this.b=o[2],o.length>3&&(this.alpha=o[3]),this.ok=!0}}this.r=this.r<0||isNaN(this.r)?0:this.r>255?255:this.r,this.g=this.g<0||isNaN(this.g)?0:this.g>255?255:this.g,this.b=this.b<0||isNaN(this.b)?0:this.b>255?255:this.b,this.alpha=this.alpha<0?0:this.alpha>1||isNaN(this.alpha)?1:this.alpha,this.toRGB=function(){return"rgb("+this.r+", "+this.g+", "+this.b+")"},this.toRGBA=function(){return"rgba("+this.r+", "+this.g+", "+this.b+", "+this.alpha+")"},this.toHex=function(){var t=this.r.toString(16),e=this.g.toString(16),r=this.b.toString(16);return 1==t.length&&(t="0"+t),1==e.length&&(e="0"+e),1==r.length&&(r="0"+r),"#"+t+e+r},this.getHelpXML=function(){for(var t=new Array,n=0;n<r.length;n++)for(var i=r[n].example,a=0;a<i.length;a++)t[t.length]=i[a];for(var s in e)t[t.length]=s;var o=document.createElement("ul");o.setAttribute("id","rgbcolor-examples");for(n=0;n<t.length;n++)try{var u=document.createElement("li"),h=new RGBColor(t[n]),c=document.createElement("div");c.style.cssText="margin: 3px; border: 1px solid black; background:"+h.toHex()+"; color:"+h.toHex(),c.appendChild(document.createTextNode("test"));var l=document.createTextNode(" "+t[n]+" -> "+h.toRGB()+" -> "+h.toHex());u.appendChild(c),u.appendChild(l),o.appendChild(u)}catch(t){}return o}}},2053:function(t,e,r){"use strict";var n=r(1907),i=r(1900),a=r(1969).indexOf,s=r(1986),o=i([].indexOf),u=!!o&&1/o([1],1,-0)<0,h=s("indexOf");n({target:"Array",proto:!0,forced:u||!h},{indexOf:function(t){var e=arguments.length>1?arguments[1]:void 0;return u?o(this,t,e)||0:a(this,t,e)}})},2054:function(t,e,r){"use strict";var n=r(1907),i=r(1900),a=r(1959),s=r(1910),o=r(1912),u=r(1960),h=i("".indexOf);n({target:"String",proto:!0,forced:!u("includes")},{includes:function(t){return!!~h(o(s(this)),o(a(t)),arguments.length>1?arguments[1]:void 0)}})},2055:function(t,e,r){"use strict";var n=r(1907),i=r(1900),a=r(2056),s=i([].reverse),o=[1,2];n({target:"Array",proto:!0,forced:String(o)===String(o.reverse())},{reverse:function(){return a(this)&&(this.length=this.length),s(this)}})},2056:function(t,e,r){var n=r(1918);t.exports=Array.isArray||function(t){return"Array"==n(t)}},2057:function(t,e,r){"use strict";r.r(e),r.d(e,"COMMAND_ARG_COUNTS",(function(){return T})),r.d(e,"SVGPathData",(function(){return w})),r.d(e,"SVGPathDataParser",(function(){return S})),r.d(e,"SVGPathDataTransformer",(function(){return l})),r.d(e,"encodeSVGPath",(function(){return a}));
/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */
var n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)};function i(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}function a(t){var e="";Array.isArray(t)||(t=[t]);for(var r=0;r<t.length;r++){var n=t[r];if(n.type===w.CLOSE_PATH)e+="z";else if(n.type===w.HORIZ_LINE_TO)e+=(n.relative?"h":"H")+n.x;else if(n.type===w.VERT_LINE_TO)e+=(n.relative?"v":"V")+n.y;else if(n.type===w.MOVE_TO)e+=(n.relative?"m":"M")+n.x+" "+n.y;else if(n.type===w.LINE_TO)e+=(n.relative?"l":"L")+n.x+" "+n.y;else if(n.type===w.CURVE_TO)e+=(n.relative?"c":"C")+n.x1+" "+n.y1+" "+n.x2+" "+n.y2+" "+n.x+" "+n.y;else if(n.type===w.SMOOTH_CURVE_TO)e+=(n.relative?"s":"S")+n.x2+" "+n.y2+" "+n.x+" "+n.y;else if(n.type===w.QUAD_TO)e+=(n.relative?"q":"Q")+n.x1+" "+n.y1+" "+n.x+" "+n.y;else if(n.type===w.SMOOTH_QUAD_TO)e+=(n.relative?"t":"T")+n.x+" "+n.y;else{if(n.type!==w.ARC)throw new Error('Unexpected command type "'+n.type+'" at index '+r+".");e+=(n.relative?"a":"A")+n.rX+" "+n.rY+" "+n.xRot+" "+ +n.lArcFlag+" "+ +n.sweepFlag+" "+n.x+" "+n.y}}return e}function s(t,e){var r=t[0],n=t[1];return[r*Math.cos(e)-n*Math.sin(e),r*Math.sin(e)+n*Math.cos(e)]}function o(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];for(var r=0;r<t.length;r++)if("number"!=typeof t[r])throw new Error("assertNumbers arguments["+r+"] is not a number. "+typeof t[r]+" == typeof "+t[r]);return!0}var u=Math.PI;function h(t,e,r){t.lArcFlag=0===t.lArcFlag?0:1,t.sweepFlag=0===t.sweepFlag?0:1;var n=t.rX,i=t.rY,a=t.x,o=t.y;n=Math.abs(t.rX),i=Math.abs(t.rY);var h=s([(e-a)/2,(r-o)/2],-t.xRot/180*u),c=h[0],l=h[1],f=Math.pow(c,2)/Math.pow(n,2)+Math.pow(l,2)/Math.pow(i,2);1<f&&(n*=Math.sqrt(f),i*=Math.sqrt(f)),t.rX=n,t.rY=i;var g=Math.pow(n,2)*Math.pow(l,2)+Math.pow(i,2)*Math.pow(c,2),p=(t.lArcFlag!==t.sweepFlag?1:-1)*Math.sqrt(Math.max(0,(Math.pow(n,2)*Math.pow(i,2)-g)/g)),d=n*l/i*p,v=-i*c/n*p,y=s([d,v],t.xRot/180*u);t.cX=y[0]+(e+a)/2,t.cY=y[1]+(r+o)/2,t.phi1=Math.atan2((l-v)/i,(c-d)/n),t.phi2=Math.atan2((-l-v)/i,(-c-d)/n),0===t.sweepFlag&&t.phi2>t.phi1&&(t.phi2-=2*u),1===t.sweepFlag&&t.phi2<t.phi1&&(t.phi2+=2*u),t.phi1*=180/u,t.phi2*=180/u}function c(t,e,r){o(t,e,r);var n=t*t+e*e-r*r;if(0>n)return[];if(0===n)return[[t*r/(t*t+e*e),e*r/(t*t+e*e)]];var i=Math.sqrt(n);return[[(t*r+e*i)/(t*t+e*e),(e*r-t*i)/(t*t+e*e)],[(t*r-e*i)/(t*t+e*e),(e*r+t*i)/(t*t+e*e)]]}var l,f=Math.PI/180;function g(t,e,r){return(1-r)*t+r*e}function p(t,e,r,n){return t+Math.cos(n/180*u)*e+Math.sin(n/180*u)*r}function d(t,e,r,n){var i=1e-6,a=e-t,s=r-e,o=3*a+3*(n-r)-6*s,u=6*(s-a),h=3*a;return Math.abs(o)<i?[-h/u]:function(t,e,r){void 0===r&&(r=1e-6);var n=t*t/4-e;if(n<-r)return[];if(n<=r)return[-t/2];var i=Math.sqrt(n);return[-t/2-i,-t/2+i]}(u/o,h/o,i)}function v(t,e,r,n,i){var a=1-i;return t*(a*a*a)+e*(3*a*a*i)+r*(3*a*i*i)+n*(i*i*i)}!function(t){function e(){return i((function(t,e,r){return t.relative&&(void 0!==t.x1&&(t.x1+=e),void 0!==t.y1&&(t.y1+=r),void 0!==t.x2&&(t.x2+=e),void 0!==t.y2&&(t.y2+=r),void 0!==t.x&&(t.x+=e),void 0!==t.y&&(t.y+=r),t.relative=!1),t}))}function r(){var t=NaN,e=NaN,r=NaN,n=NaN;return i((function(i,a,s){return i.type&w.SMOOTH_CURVE_TO&&(i.type=w.CURVE_TO,t=isNaN(t)?a:t,e=isNaN(e)?s:e,i.x1=i.relative?a-t:2*a-t,i.y1=i.relative?s-e:2*s-e),i.type&w.CURVE_TO?(t=i.relative?a+i.x2:i.x2,e=i.relative?s+i.y2:i.y2):(t=NaN,e=NaN),i.type&w.SMOOTH_QUAD_TO&&(i.type=w.QUAD_TO,r=isNaN(r)?a:r,n=isNaN(n)?s:n,i.x1=i.relative?a-r:2*a-r,i.y1=i.relative?s-n:2*s-n),i.type&w.QUAD_TO?(r=i.relative?a+i.x1:i.x1,n=i.relative?s+i.y1:i.y1):(r=NaN,n=NaN),i}))}function n(){var t=NaN,e=NaN;return i((function(r,n,i){if(r.type&w.SMOOTH_QUAD_TO&&(r.type=w.QUAD_TO,t=isNaN(t)?n:t,e=isNaN(e)?i:e,r.x1=r.relative?n-t:2*n-t,r.y1=r.relative?i-e:2*i-e),r.type&w.QUAD_TO){t=r.relative?n+r.x1:r.x1,e=r.relative?i+r.y1:r.y1;var a=r.x1,s=r.y1;r.type=w.CURVE_TO,r.x1=((r.relative?0:n)+2*a)/3,r.y1=((r.relative?0:i)+2*s)/3,r.x2=(r.x+2*a)/3,r.y2=(r.y+2*s)/3}else t=NaN,e=NaN;return r}))}function i(t){var e=0,r=0,n=NaN,i=NaN;return function(a){if(isNaN(n)&&!(a.type&w.MOVE_TO))throw new Error("path must start with moveto");var s=t(a,e,r,n,i);return a.type&w.CLOSE_PATH&&(e=n,r=i),void 0!==a.x&&(e=a.relative?e+a.x:a.x),void 0!==a.y&&(r=a.relative?r+a.y:a.y),a.type&w.MOVE_TO&&(n=e,i=r),s}}function a(t,e,r,n,a,s){return o(t,e,r,n,a,s),i((function(i,o,u,h){var c=i.x1,l=i.x2,f=i.relative&&!isNaN(h),g=void 0!==i.x?i.x:f?0:o,p=void 0!==i.y?i.y:f?0:u;function d(t){return t*t}i.type&w.HORIZ_LINE_TO&&0!==e&&(i.type=w.LINE_TO,i.y=i.relative?0:u),i.type&w.VERT_LINE_TO&&0!==r&&(i.type=w.LINE_TO,i.x=i.relative?0:o),void 0!==i.x&&(i.x=i.x*t+p*r+(f?0:a)),void 0!==i.y&&(i.y=g*e+i.y*n+(f?0:s)),void 0!==i.x1&&(i.x1=i.x1*t+i.y1*r+(f?0:a)),void 0!==i.y1&&(i.y1=c*e+i.y1*n+(f?0:s)),void 0!==i.x2&&(i.x2=i.x2*t+i.y2*r+(f?0:a)),void 0!==i.y2&&(i.y2=l*e+i.y2*n+(f?0:s));var v=t*n-e*r;if(void 0!==i.xRot&&(1!==t||0!==e||0!==r||1!==n))if(0===v)delete i.rX,delete i.rY,delete i.xRot,delete i.lArcFlag,delete i.sweepFlag,i.type=w.LINE_TO;else{var y=i.xRot*Math.PI/180,m=Math.sin(y),x=Math.cos(y),b=1/d(i.rX),S=1/d(i.rY),T=d(x)*b+d(m)*S,A=2*m*x*(b-S),O=d(m)*b+d(x)*S,P=T*n*n-A*e*n+O*e*e,C=A*(t*n+e*r)-2*(T*r*n+O*t*e),E=T*r*r-A*t*r+O*t*t,M=(Math.atan2(C,P-E)+Math.PI)%Math.PI/2,N=Math.sin(M),R=Math.cos(M);i.rX=Math.abs(v)/Math.sqrt(P*d(R)+C*N*R+E*d(N)),i.rY=Math.abs(v)/Math.sqrt(P*d(N)-C*N*R+E*d(R)),i.xRot=180*M/Math.PI}return void 0!==i.sweepFlag&&0>v&&(i.sweepFlag=+!i.sweepFlag),i}))}t.ROUND=function(t){function e(e){return Math.round(e*t)/t}return void 0===t&&(t=1e13),o(t),function(t){return void 0!==t.x1&&(t.x1=e(t.x1)),void 0!==t.y1&&(t.y1=e(t.y1)),void 0!==t.x2&&(t.x2=e(t.x2)),void 0!==t.y2&&(t.y2=e(t.y2)),void 0!==t.x&&(t.x=e(t.x)),void 0!==t.y&&(t.y=e(t.y)),void 0!==t.rX&&(t.rX=e(t.rX)),void 0!==t.rY&&(t.rY=e(t.rY)),t}},t.TO_ABS=e,t.TO_REL=function(){return i((function(t,e,r){return t.relative||(void 0!==t.x1&&(t.x1-=e),void 0!==t.y1&&(t.y1-=r),void 0!==t.x2&&(t.x2-=e),void 0!==t.y2&&(t.y2-=r),void 0!==t.x&&(t.x-=e),void 0!==t.y&&(t.y-=r),t.relative=!0),t}))},t.NORMALIZE_HVZ=function(t,e,r){return void 0===t&&(t=!0),void 0===e&&(e=!0),void 0===r&&(r=!0),i((function(n,i,a,s,o){if(isNaN(s)&&!(n.type&w.MOVE_TO))throw new Error("path must start with moveto");return e&&n.type&w.HORIZ_LINE_TO&&(n.type=w.LINE_TO,n.y=n.relative?0:a),r&&n.type&w.VERT_LINE_TO&&(n.type=w.LINE_TO,n.x=n.relative?0:i),t&&n.type&w.CLOSE_PATH&&(n.type=w.LINE_TO,n.x=n.relative?s-i:s,n.y=n.relative?o-a:o),n.type&w.ARC&&(0===n.rX||0===n.rY)&&(n.type=w.LINE_TO,delete n.rX,delete n.rY,delete n.xRot,delete n.lArcFlag,delete n.sweepFlag),n}))},t.NORMALIZE_ST=r,t.QT_TO_C=n,t.INFO=i,t.SANITIZE=function(t){void 0===t&&(t=0),o(t);var e=NaN,r=NaN,n=NaN,a=NaN;return i((function(i,s,o,u,h){var c=Math.abs,l=!1,f=0,g=0;if(i.type&w.SMOOTH_CURVE_TO&&(f=isNaN(e)?0:s-e,g=isNaN(r)?0:o-r),i.type&(w.CURVE_TO|w.SMOOTH_CURVE_TO)?(e=i.relative?s+i.x2:i.x2,r=i.relative?o+i.y2:i.y2):(e=NaN,r=NaN),i.type&w.SMOOTH_QUAD_TO?(n=isNaN(n)?s:2*s-n,a=isNaN(a)?o:2*o-a):i.type&w.QUAD_TO?(n=i.relative?s+i.x1:i.x1,a=i.relative?o+i.y1:i.y2):(n=NaN,a=NaN),i.type&w.LINE_COMMANDS||i.type&w.ARC&&(0===i.rX||0===i.rY||!i.lArcFlag)||i.type&w.CURVE_TO||i.type&w.SMOOTH_CURVE_TO||i.type&w.QUAD_TO||i.type&w.SMOOTH_QUAD_TO){var p=void 0===i.x?0:i.relative?i.x:i.x-s,d=void 0===i.y?0:i.relative?i.y:i.y-o;f=isNaN(n)?void 0===i.x1?f:i.relative?i.x:i.x1-s:n-s,g=isNaN(a)?void 0===i.y1?g:i.relative?i.y:i.y1-o:a-o;var v=void 0===i.x2?0:i.relative?i.x:i.x2-s,y=void 0===i.y2?0:i.relative?i.y:i.y2-o;c(p)<=t&&c(d)<=t&&c(f)<=t&&c(g)<=t&&c(v)<=t&&c(y)<=t&&(l=!0)}return i.type&w.CLOSE_PATH&&c(s-u)<=t&&c(o-h)<=t&&(l=!0),l?[]:i}))},t.MATRIX=a,t.ROTATE=function(t,e,r){void 0===e&&(e=0),void 0===r&&(r=0),o(t,e,r);var n=Math.sin(t),i=Math.cos(t);return a(i,n,-n,i,e-e*i+r*n,r-e*n-r*i)},t.TRANSLATE=function(t,e){return void 0===e&&(e=0),o(t,e),a(1,0,0,1,t,e)},t.SCALE=function(t,e){return void 0===e&&(e=t),o(t,e),a(t,0,0,e,0,0)},t.SKEW_X=function(t){return o(t),a(1,0,Math.atan(t),1,0,0)},t.SKEW_Y=function(t){return o(t),a(1,Math.atan(t),0,1,0,0)},t.X_AXIS_SYMMETRY=function(t){return void 0===t&&(t=0),o(t),a(-1,0,0,1,t,0)},t.Y_AXIS_SYMMETRY=function(t){return void 0===t&&(t=0),o(t),a(1,0,0,-1,0,t)},t.A_TO_C=function(){return i((function(t,e,r){return w.ARC===t.type?function(t,e,r){var n,i,a,o;t.cX||h(t,e,r);for(var u=Math.min(t.phi1,t.phi2),c=Math.max(t.phi1,t.phi2)-u,l=Math.ceil(c/90),p=new Array(l),d=e,v=r,y=0;y<l;y++){var m=g(t.phi1,t.phi2,y/l),x=g(t.phi1,t.phi2,(y+1)/l),b=x-m,S=4/3*Math.tan(b*f/4),T=[Math.cos(m*f)-S*Math.sin(m*f),Math.sin(m*f)+S*Math.cos(m*f)],A=T[0],O=T[1],P=[Math.cos(x*f),Math.sin(x*f)],C=P[0],E=P[1],M=[C+S*Math.sin(x*f),E-S*Math.cos(x*f)],N=M[0],R=M[1];p[y]={relative:t.relative,type:w.CURVE_TO};var _=function(e,r){var n=s([e*t.rX,r*t.rY],t.xRot),i=n[0],a=n[1];return[t.cX+i,t.cY+a]};n=_(A,O),p[y].x1=n[0],p[y].y1=n[1],i=_(N,R),p[y].x2=i[0],p[y].y2=i[1],a=_(C,E),p[y].x=a[0],p[y].y=a[1],t.relative&&(p[y].x1-=d,p[y].y1-=v,p[y].x2-=d,p[y].y2-=v,p[y].x-=d,p[y].y-=v),d=(o=[p[y].x,p[y].y])[0],v=o[1]}return p}(t,t.relative?0:e,t.relative?0:r):t}))},t.ANNOTATE_ARCS=function(){return i((function(t,e,r){return t.relative&&(e=0,r=0),w.ARC===t.type&&h(t,e,r),t}))},t.CLONE=function(){return function(t){var e={};for(var r in t)e[r]=t[r];return e}},t.CALCULATE_BOUNDS=function(){var t=e(),a=n(),s=r(),o=i((function(e,r,n){var i=s(a(t(function(t){var e={};for(var r in t)e[r]=t[r];return e}(e))));function u(t){t>o.maxX&&(o.maxX=t),t<o.minX&&(o.minX=t)}function l(t){t>o.maxY&&(o.maxY=t),t<o.minY&&(o.minY=t)}if(i.type&w.DRAWING_COMMANDS&&(u(r),l(n)),i.type&w.HORIZ_LINE_TO&&u(i.x),i.type&w.VERT_LINE_TO&&l(i.y),i.type&w.LINE_TO&&(u(i.x),l(i.y)),i.type&w.CURVE_TO){u(i.x),l(i.y);for(var f=0,g=d(r,i.x1,i.x2,i.x);f<g.length;f++)0<(V=g[f])&&1>V&&u(v(r,i.x1,i.x2,i.x,V));for(var y=0,m=d(n,i.y1,i.y2,i.y);y<m.length;y++)0<(V=m[y])&&1>V&&l(v(n,i.y1,i.y2,i.y,V))}if(i.type&w.ARC){u(i.x),l(i.y),h(i,r,n);for(var x=i.xRot/180*Math.PI,b=Math.cos(x)*i.rX,S=Math.sin(x)*i.rX,T=-Math.sin(x)*i.rY,A=Math.cos(x)*i.rY,O=i.phi1<i.phi2?[i.phi1,i.phi2]:-180>i.phi2?[i.phi2+360,i.phi1+360]:[i.phi2,i.phi1],P=O[0],C=O[1],E=function(t){var e=t[0],r=t[1],n=180*Math.atan2(r,e)/Math.PI;return n<P?n+360:n},M=0,N=c(T,-b,0).map(E);M<N.length;M++)(V=N[M])>P&&V<C&&u(p(i.cX,b,T,V));for(var R=0,_=c(A,-S,0).map(E);R<_.length;R++){var V;(V=_[R])>P&&V<C&&l(p(i.cY,S,A,V))}}return e}));return o.minX=1/0,o.maxX=-1/0,o.minY=1/0,o.maxY=-1/0,o}}(l||(l={}));var y,m=function(){function t(){}return t.prototype.round=function(t){return this.transform(l.ROUND(t))},t.prototype.toAbs=function(){return this.transform(l.TO_ABS())},t.prototype.toRel=function(){return this.transform(l.TO_REL())},t.prototype.normalizeHVZ=function(t,e,r){return this.transform(l.NORMALIZE_HVZ(t,e,r))},t.prototype.normalizeST=function(){return this.transform(l.NORMALIZE_ST())},t.prototype.qtToC=function(){return this.transform(l.QT_TO_C())},t.prototype.aToC=function(){return this.transform(l.A_TO_C())},t.prototype.sanitize=function(t){return this.transform(l.SANITIZE(t))},t.prototype.translate=function(t,e){return this.transform(l.TRANSLATE(t,e))},t.prototype.scale=function(t,e){return this.transform(l.SCALE(t,e))},t.prototype.rotate=function(t,e,r){return this.transform(l.ROTATE(t,e,r))},t.prototype.matrix=function(t,e,r,n,i,a){return this.transform(l.MATRIX(t,e,r,n,i,a))},t.prototype.skewX=function(t){return this.transform(l.SKEW_X(t))},t.prototype.skewY=function(t){return this.transform(l.SKEW_Y(t))},t.prototype.xSymmetry=function(t){return this.transform(l.X_AXIS_SYMMETRY(t))},t.prototype.ySymmetry=function(t){return this.transform(l.Y_AXIS_SYMMETRY(t))},t.prototype.annotateArcs=function(){return this.transform(l.ANNOTATE_ARCS())},t}(),x=function(t){return" "===t||"\t"===t||"\r"===t||"\n"===t},b=function(t){return"0".charCodeAt(0)<=t.charCodeAt(0)&&t.charCodeAt(0)<="9".charCodeAt(0)},S=function(t){function e(){var e=t.call(this)||this;return e.curNumber="",e.curCommandType=-1,e.curCommandRelative=!1,e.canParseCommandOrComma=!0,e.curNumberHasExp=!1,e.curNumberHasExpDigits=!1,e.curNumberHasDecimal=!1,e.curArgs=[],e}return i(e,t),e.prototype.finish=function(t){if(void 0===t&&(t=[]),this.parse(" ",t),0!==this.curArgs.length||!this.canParseCommandOrComma)throw new SyntaxError("Unterminated command at the path end.");return t},e.prototype.parse=function(t,e){var r=this;void 0===e&&(e=[]);for(var n=function(t){e.push(t),r.curArgs.length=0,r.canParseCommandOrComma=!0},i=0;i<t.length;i++){var a=t[i],s=!(this.curCommandType!==w.ARC||3!==this.curArgs.length&&4!==this.curArgs.length||1!==this.curNumber.length||"0"!==this.curNumber&&"1"!==this.curNumber),o=b(a)&&("0"===this.curNumber&&"0"===a||s);if(!b(a)||o)if("e"!==a&&"E"!==a)if("-"!==a&&"+"!==a||!this.curNumberHasExp||this.curNumberHasExpDigits)if("."!==a||this.curNumberHasExp||this.curNumberHasDecimal||s){if(this.curNumber&&-1!==this.curCommandType){var u=Number(this.curNumber);if(isNaN(u))throw new SyntaxError("Invalid number ending at "+i);if(this.curCommandType===w.ARC)if(0===this.curArgs.length||1===this.curArgs.length){if(0>u)throw new SyntaxError('Expected positive number, got "'+u+'" at index "'+i+'"')}else if((3===this.curArgs.length||4===this.curArgs.length)&&"0"!==this.curNumber&&"1"!==this.curNumber)throw new SyntaxError('Expected a flag, got "'+this.curNumber+'" at index "'+i+'"');this.curArgs.push(u),this.curArgs.length===T[this.curCommandType]&&(w.HORIZ_LINE_TO===this.curCommandType?n({type:w.HORIZ_LINE_TO,relative:this.curCommandRelative,x:u}):w.VERT_LINE_TO===this.curCommandType?n({type:w.VERT_LINE_TO,relative:this.curCommandRelative,y:u}):this.curCommandType===w.MOVE_TO||this.curCommandType===w.LINE_TO||this.curCommandType===w.SMOOTH_QUAD_TO?(n({type:this.curCommandType,relative:this.curCommandRelative,x:this.curArgs[0],y:this.curArgs[1]}),w.MOVE_TO===this.curCommandType&&(this.curCommandType=w.LINE_TO)):this.curCommandType===w.CURVE_TO?n({type:w.CURVE_TO,relative:this.curCommandRelative,x1:this.curArgs[0],y1:this.curArgs[1],x2:this.curArgs[2],y2:this.curArgs[3],x:this.curArgs[4],y:this.curArgs[5]}):this.curCommandType===w.SMOOTH_CURVE_TO?n({type:w.SMOOTH_CURVE_TO,relative:this.curCommandRelative,x2:this.curArgs[0],y2:this.curArgs[1],x:this.curArgs[2],y:this.curArgs[3]}):this.curCommandType===w.QUAD_TO?n({type:w.QUAD_TO,relative:this.curCommandRelative,x1:this.curArgs[0],y1:this.curArgs[1],x:this.curArgs[2],y:this.curArgs[3]}):this.curCommandType===w.ARC&&n({type:w.ARC,relative:this.curCommandRelative,rX:this.curArgs[0],rY:this.curArgs[1],xRot:this.curArgs[2],lArcFlag:this.curArgs[3],sweepFlag:this.curArgs[4],x:this.curArgs[5],y:this.curArgs[6]})),this.curNumber="",this.curNumberHasExpDigits=!1,this.curNumberHasExp=!1,this.curNumberHasDecimal=!1,this.canParseCommandOrComma=!0}if(!x(a))if(","===a&&this.canParseCommandOrComma)this.canParseCommandOrComma=!1;else if("+"!==a&&"-"!==a&&"."!==a)if(o)this.curNumber=a,this.curNumberHasDecimal=!1;else{if(0!==this.curArgs.length)throw new SyntaxError("Unterminated command at index "+i+".");if(!this.canParseCommandOrComma)throw new SyntaxError('Unexpected character "'+a+'" at index '+i+". Command cannot follow comma");if(this.canParseCommandOrComma=!1,"z"!==a&&"Z"!==a)if("h"===a||"H"===a)this.curCommandType=w.HORIZ_LINE_TO,this.curCommandRelative="h"===a;else if("v"===a||"V"===a)this.curCommandType=w.VERT_LINE_TO,this.curCommandRelative="v"===a;else if("m"===a||"M"===a)this.curCommandType=w.MOVE_TO,this.curCommandRelative="m"===a;else if("l"===a||"L"===a)this.curCommandType=w.LINE_TO,this.curCommandRelative="l"===a;else if("c"===a||"C"===a)this.curCommandType=w.CURVE_TO,this.curCommandRelative="c"===a;else if("s"===a||"S"===a)this.curCommandType=w.SMOOTH_CURVE_TO,this.curCommandRelative="s"===a;else if("q"===a||"Q"===a)this.curCommandType=w.QUAD_TO,this.curCommandRelative="q"===a;else if("t"===a||"T"===a)this.curCommandType=w.SMOOTH_QUAD_TO,this.curCommandRelative="t"===a;else{if("a"!==a&&"A"!==a)throw new SyntaxError('Unexpected character "'+a+'" at index '+i+".");this.curCommandType=w.ARC,this.curCommandRelative="a"===a}else e.push({type:w.CLOSE_PATH}),this.canParseCommandOrComma=!0,this.curCommandType=-1}else this.curNumber=a,this.curNumberHasDecimal="."===a}else this.curNumber+=a,this.curNumberHasDecimal=!0;else this.curNumber+=a;else this.curNumber+=a,this.curNumberHasExp=!0;else this.curNumber+=a,this.curNumberHasExpDigits=this.curNumberHasExp}return e},e.prototype.transform=function(t){return Object.create(this,{parse:{value:function(e,r){void 0===r&&(r=[]);for(var n=0,i=Object.getPrototypeOf(this).parse.call(this,e);n<i.length;n++){var a=i[n],s=t(a);Array.isArray(s)?r.push.apply(r,s):r.push(s)}return r}}})},e}(m),w=function(t){function e(r){var n=t.call(this)||this;return n.commands="string"==typeof r?e.parse(r):r,n}return i(e,t),e.prototype.encode=function(){return e.encode(this.commands)},e.prototype.getBounds=function(){var t=l.CALCULATE_BOUNDS();return this.transform(t),t},e.prototype.transform=function(t){for(var e=[],r=0,n=this.commands;r<n.length;r++){var i=t(n[r]);Array.isArray(i)?e.push.apply(e,i):e.push(i)}return this.commands=e,this},e.encode=function(t){return a(t)},e.parse=function(t){var e=new S,r=[];return e.parse(t,r),e.finish(r),r},e.CLOSE_PATH=1,e.MOVE_TO=2,e.HORIZ_LINE_TO=4,e.VERT_LINE_TO=8,e.LINE_TO=16,e.CURVE_TO=32,e.SMOOTH_CURVE_TO=64,e.QUAD_TO=128,e.SMOOTH_QUAD_TO=256,e.ARC=512,e.LINE_COMMANDS=e.LINE_TO|e.HORIZ_LINE_TO|e.VERT_LINE_TO,e.DRAWING_COMMANDS=e.HORIZ_LINE_TO|e.VERT_LINE_TO|e.LINE_TO|e.CURVE_TO|e.SMOOTH_CURVE_TO|e.QUAD_TO|e.SMOOTH_QUAD_TO|e.ARC,e}(m),T=((y={})[w.MOVE_TO]=2,y[w.LINE_TO]=2,y[w.HORIZ_LINE_TO]=1,y[w.VERT_LINE_TO]=1,y[w.CLOSE_PATH]=0,y[w.QUAD_TO]=4,y[w.SMOOTH_QUAD_TO]=2,y[w.CURVE_TO]=6,y[w.SMOOTH_CURVE_TO]=4,y[w.ARC]=7,y)},2058:function(t,e,r){"use strict";var n=r(1935).PROPER,i=r(1917),a=r(1906),s=r(1912),o=r(1903),u=r(2059),h=RegExp.prototype.toString,c=o((function(){return"/a/b"!=h.call({source:"a",flags:"b"})})),l=n&&"toString"!=h.name;(c||l)&&i(RegExp.prototype,"toString",(function(){var t=a(this);return"/"+s(t.source)+"/"+s(u(t))}),{unsafe:!0})},2059:function(t,e,r){var n=r(1905),i=r(1908),a=r(1930),s=r(1980),o=RegExp.prototype;t.exports=function(t){var e=t.flags;return void 0!==e||"flags"in o||i(t,"flags")||!a(o,t)?e:n(s,t)}},2060:function(t,e,r){"use strict";function n(t){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}r.r(e),r.d(e,"BlurStack",(function(){return f})),r.d(e,"canvasRGB",(function(){return c})),r.d(e,"canvasRGBA",(function(){return u})),r.d(e,"image",(function(){return s})),r.d(e,"imageDataRGB",(function(){return l})),r.d(e,"imageDataRGBA",(function(){return h}));var i=[512,512,456,512,328,456,335,512,405,328,271,456,388,335,292,512,454,405,364,328,298,271,496,456,420,388,360,335,312,292,273,512,482,454,428,405,383,364,345,328,312,298,284,271,259,496,475,456,437,420,404,388,374,360,347,335,323,312,302,292,282,273,265,512,497,482,468,454,441,428,417,405,394,383,373,364,354,345,337,328,320,312,305,298,291,284,278,271,265,259,507,496,485,475,465,456,446,437,428,420,412,404,396,388,381,374,367,360,354,347,341,335,329,323,318,312,307,302,297,292,287,282,278,273,269,265,261,512,505,497,489,482,475,468,461,454,447,441,435,428,422,417,411,405,399,394,389,383,378,373,368,364,359,354,350,345,341,337,332,328,324,320,316,312,309,305,301,298,294,291,287,284,281,278,274,271,268,265,262,259,257,507,501,496,491,485,480,475,470,465,460,456,451,446,442,437,433,428,424,420,416,412,408,404,400,396,392,388,385,381,377,374,370,367,363,360,357,354,350,347,344,341,338,335,332,329,326,323,320,318,315,312,310,307,304,302,299,297,294,292,289,287,285,282,280,278,275,273,271,269,267,265,263,261,259],a=[9,11,12,13,13,14,14,15,15,15,15,16,16,16,16,17,17,17,17,17,17,17,18,18,18,18,18,18,18,18,18,19,19,19,19,19,19,19,19,19,19,19,19,19,19,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24];function s(t,e,r,n,i,a){if("string"==typeof t&&(t=document.getElementById(t)),t&&"naturalWidth"in t){var s=i?"offset":"natural",o=t[s+"Width"],h=t[s+"Height"];if("string"==typeof e&&(e=document.getElementById(e)),e&&"getContext"in e){a||(e.style.width=o+"px",e.style.height=h+"px"),e.width=o,e.height=h;var l=e.getContext("2d");l.clearRect(0,0,o,h),l.drawImage(t,0,0,t.naturalWidth,t.naturalHeight,0,0,o,h),isNaN(r)||r<1||(n?u(e,0,0,o,h,r):c(e,0,0,o,h,r))}}}function o(t,e,r,i,a){if("string"==typeof t&&(t=document.getElementById(t)),!t||"object"!==n(t)||!("getContext"in t))throw new TypeError("Expecting canvas with `getContext` method in processCanvasRGB(A) calls!");var s=t.getContext("2d");try{return s.getImageData(e,r,i,a)}catch(t){throw new Error("unable to access image data: "+t)}}function u(t,e,r,n,i,a){if(!(isNaN(a)||a<1)){a|=0;var s=o(t,e,r,n,i);s=h(s,e,r,n,i,a),t.getContext("2d").putImageData(s,e,r)}}function h(t,e,r,n,s,o){for(var u,h=t.data,c=2*o+1,l=n-1,g=s-1,p=o+1,d=p*(p+1)/2,v=new f,y=v,m=1;m<c;m++)y=y.next=new f,m===p&&(u=y);y.next=v;for(var x=null,b=null,S=0,w=0,T=i[o],A=a[o],O=0;O<s;O++){y=v;for(var P=h[w],C=h[w+1],E=h[w+2],M=h[w+3],N=0;N<p;N++)y.r=P,y.g=C,y.b=E,y.a=M,y=y.next;for(var R=0,_=0,V=0,I=0,k=p*P,L=p*C,D=p*E,B=p*M,j=d*P,F=d*C,z=d*E,U=d*M,H=1;H<p;H++){var X=w+((l<H?l:H)<<2),Y=h[X],G=h[X+1],W=h[X+2],q=h[X+3],Q=p-H;j+=(y.r=Y)*Q,F+=(y.g=G)*Q,z+=(y.b=W)*Q,U+=(y.a=q)*Q,R+=Y,_+=G,V+=W,I+=q,y=y.next}x=v,b=u;for(var $=0;$<n;$++){var Z=U*T>>A;if(h[w+3]=Z,0!==Z){var K=255/Z;h[w]=(j*T>>A)*K,h[w+1]=(F*T>>A)*K,h[w+2]=(z*T>>A)*K}else h[w]=h[w+1]=h[w+2]=0;j-=k,F-=L,z-=D,U-=B,k-=x.r,L-=x.g,D-=x.b,B-=x.a;var J=$+o+1;J=S+(J<l?J:l)<<2,j+=R+=x.r=h[J],F+=_+=x.g=h[J+1],z+=V+=x.b=h[J+2],U+=I+=x.a=h[J+3],x=x.next;var tt=b,et=tt.r,rt=tt.g,nt=tt.b,it=tt.a;k+=et,L+=rt,D+=nt,B+=it,R-=et,_-=rt,V-=nt,I-=it,b=b.next,w+=4}S+=n}for(var at=0;at<n;at++){var st=h[w=at<<2],ot=h[w+1],ut=h[w+2],ht=h[w+3],ct=p*st,lt=p*ot,ft=p*ut,gt=p*ht,pt=d*st,dt=d*ot,vt=d*ut,yt=d*ht;y=v;for(var mt=0;mt<p;mt++)y.r=st,y.g=ot,y.b=ut,y.a=ht,y=y.next;for(var xt=n,bt=0,St=0,wt=0,Tt=0,At=1;At<=o;At++){w=xt+at<<2;var Ot=p-At;pt+=(y.r=st=h[w])*Ot,dt+=(y.g=ot=h[w+1])*Ot,vt+=(y.b=ut=h[w+2])*Ot,yt+=(y.a=ht=h[w+3])*Ot,Tt+=st,bt+=ot,St+=ut,wt+=ht,y=y.next,At<g&&(xt+=n)}w=at,x=v,b=u;for(var Pt=0;Pt<s;Pt++){var Ct=w<<2;h[Ct+3]=ht=yt*T>>A,ht>0?(ht=255/ht,h[Ct]=(pt*T>>A)*ht,h[Ct+1]=(dt*T>>A)*ht,h[Ct+2]=(vt*T>>A)*ht):h[Ct]=h[Ct+1]=h[Ct+2]=0,pt-=ct,dt-=lt,vt-=ft,yt-=gt,ct-=x.r,lt-=x.g,ft-=x.b,gt-=x.a,Ct=at+((Ct=Pt+p)<g?Ct:g)*n<<2,pt+=Tt+=x.r=h[Ct],dt+=bt+=x.g=h[Ct+1],vt+=St+=x.b=h[Ct+2],yt+=wt+=x.a=h[Ct+3],x=x.next,ct+=st=b.r,lt+=ot=b.g,ft+=ut=b.b,gt+=ht=b.a,Tt-=st,bt-=ot,St-=ut,wt-=ht,b=b.next,w+=n}}return t}function c(t,e,r,n,i,a){if(!(isNaN(a)||a<1)){a|=0;var s=o(t,e,r,n,i);s=l(s,e,r,n,i,a),t.getContext("2d").putImageData(s,e,r)}}function l(t,e,r,n,s,o){for(var u,h=t.data,c=2*o+1,l=n-1,g=s-1,p=o+1,d=p*(p+1)/2,v=new f,y=v,m=1;m<c;m++)y=y.next=new f,m===p&&(u=y);y.next=v;for(var x,b,S=null,w=null,T=i[o],A=a[o],O=0,P=0,C=0;C<s;C++){var E=h[P],M=h[P+1],N=h[P+2],R=p*E,_=p*M,V=p*N,I=d*E,k=d*M,L=d*N;y=v;for(var D=0;D<p;D++)y.r=E,y.g=M,y.b=N,y=y.next;for(var B=0,j=0,F=0,z=1;z<p;z++)x=P+((l<z?l:z)<<2),I+=(y.r=E=h[x])*(b=p-z),k+=(y.g=M=h[x+1])*b,L+=(y.b=N=h[x+2])*b,B+=E,j+=M,F+=N,y=y.next;S=v,w=u;for(var U=0;U<n;U++)h[P]=I*T>>A,h[P+1]=k*T>>A,h[P+2]=L*T>>A,I-=R,k-=_,L-=V,R-=S.r,_-=S.g,V-=S.b,x=O+((x=U+o+1)<l?x:l)<<2,I+=B+=S.r=h[x],k+=j+=S.g=h[x+1],L+=F+=S.b=h[x+2],S=S.next,R+=E=w.r,_+=M=w.g,V+=N=w.b,B-=E,j-=M,F-=N,w=w.next,P+=4;O+=n}for(var H=0;H<n;H++){var X=h[P=H<<2],Y=h[P+1],G=h[P+2],W=p*X,q=p*Y,Q=p*G,$=d*X,Z=d*Y,K=d*G;y=v;for(var J=0;J<p;J++)y.r=X,y.g=Y,y.b=G,y=y.next;for(var tt=0,et=0,rt=0,nt=1,it=n;nt<=o;nt++)P=it+H<<2,$+=(y.r=X=h[P])*(b=p-nt),Z+=(y.g=Y=h[P+1])*b,K+=(y.b=G=h[P+2])*b,tt+=X,et+=Y,rt+=G,y=y.next,nt<g&&(it+=n);P=H,S=v,w=u;for(var at=0;at<s;at++)h[x=P<<2]=$*T>>A,h[x+1]=Z*T>>A,h[x+2]=K*T>>A,$-=W,Z-=q,K-=Q,W-=S.r,q-=S.g,Q-=S.b,x=H+((x=at+p)<g?x:g)*n<<2,$+=tt+=S.r=h[x],Z+=et+=S.g=h[x+1],K+=rt+=S.b=h[x+2],S=S.next,W+=X=w.r,q+=Y=w.g,Q+=G=w.b,tt-=X,et-=Y,rt-=G,w=w.next,P+=n}return t}var f=function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.r=0,this.g=0,this.b=0,this.a=0,this.next=null}},313:function(t,e){var r,n,i=t.exports={};function a(){throw new Error("setTimeout has not been defined")}function s(){throw new Error("clearTimeout has not been defined")}function o(t){if(r===setTimeout)return setTimeout(t,0);if((r===a||!r)&&setTimeout)return r=setTimeout,setTimeout(t,0);try{return r(t,0)}catch(e){try{return r.call(null,t,0)}catch(e){return r.call(this,t,0)}}}!function(){try{r="function"==typeof setTimeout?setTimeout:a}catch(t){r=a}try{n="function"==typeof clearTimeout?clearTimeout:s}catch(t){n=s}}();var u,h=[],c=!1,l=-1;function f(){c&&u&&(c=!1,u.length?h=u.concat(h):l=-1,h.length&&g())}function g(){if(!c){var t=o(f);c=!0;for(var e=h.length;e;){for(u=h,h=[];++l<e;)u&&u[l].run();l=-1,e=h.length}u=null,c=!1,function(t){if(n===clearTimeout)return clearTimeout(t);if((n===s||!n)&&clearTimeout)return n=clearTimeout,clearTimeout(t);try{n(t)}catch(e){try{return n.call(null,t)}catch(e){return n.call(this,t)}}}(t)}}function p(t,e){this.fun=t,this.array=e}function d(){}i.nextTick=function(t){var e=new Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)e[r-1]=arguments[r];h.push(new p(t,e)),1!==h.length||c||o(g)},p.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=d,i.addListener=d,i.once=d,i.off=d,i.removeListener=d,i.removeAllListeners=d,i.emit=d,i.prependListener=d,i.prependOnceListener=d,i.listeners=function(t){return[]},i.binding=function(t){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(t){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},67:function(t,e,r){(function(e){for(var n=r(1424),i="undefined"==typeof window?e:window,a=["moz","webkit"],s="AnimationFrame",o=i["request"+s],u=i["cancel"+s]||i["cancelRequest"+s],h=0;!o&&h<a.length;h++)o=i[a[h]+"Request"+s],u=i[a[h]+"Cancel"+s]||i[a[h]+"CancelRequest"+s];if(!o||!u){var c=0,l=0,f=[];o=function(t){if(0===f.length){var e=n(),r=Math.max(0,1e3/60-(e-c));c=r+e,setTimeout((function(){var t=f.slice(0);f.length=0;for(var e=0;e<t.length;e++)if(!t[e].cancelled)try{t[e].callback(c)}catch(t){setTimeout((function(){throw t}),0)}}),Math.round(r))}return f.push({handle:++l,callback:t,cancelled:!1}),l},u=function(t){for(var e=0;e<f.length;e++)f[e].handle===t&&(f[e].cancelled=!0)}}t.exports=function(t){return o.call(i,t)},t.exports.cancel=function(){u.apply(i,arguments)},t.exports.polyfill=function(t){t||(t=i),t.requestAnimationFrame=o,t.cancelAnimationFrame=u}}).call(this,r(135))}}]);