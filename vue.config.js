const path = require('path');
const CompressionPlugin = require('compression-webpack-plugin');
const UglifyJsPlugin = require('uglifyjs-webpack-plugin');
const { CleanWebpackPlugin } = require('clean-webpack-plugin');
const webpack = require('webpack');
// const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin
const fs = require('fs');
const proMode = process.env.NODE_ENV == 'production';

function resolve (dir) {
  return path.join(__dirname, dir);
}
const files = fs.readdirSync(path.resolve(__dirname, './public/dll'));
let dllReferencePluginList = [];
files.forEach(file => {
  if (/.*\.json/.test(file)) {
    dllReferencePluginList.push(new webpack.DllReferencePlugin({
      context: process.cwd(),

      manifest: path.resolve(__dirname, './public/dll', file)
    }));
  }
});
module.exports = {
  productionSourceMap: false,
  publicPath: proMode ? './' : '/',
  configureWebpack: {
    externals: {
      'vue': 'Vue',
      'AMap': 'AMap',
      'AMapUI': 'AMapUI',
      '$': 'jquery',
      'axios': 'axios',
      'vue-router': 'VueRouter',
      vuex: 'Vuex',
      'OSS': 'ali-oss'
    },
    performance: proMode ? { maxEntrypointSize: 10000000, maxAssetSize: 3000000 } : {},
    output: proMode ? {
      filename: `[name].[contenthash].js`,
      chunkFilename: `[name].[contenthash].js`
    } : {
      filename: `[name].[hash].js`,
      chunkFilename: `[name].[hash].js`
    },
    plugins: (proMode ? [
      // new BundleAnalyzerPlugin(),
      // Ignore all locale files of moment.js
      new webpack.IgnorePlugin(/^\.\/locale$/, /moment$/),
      new CleanWebpackPlugin(),
      new webpack.HashedModuleIdsPlugin(),
      // ����compression-webpack-pluginѹ��
      new CompressionPlugin({
        algorithm: 'gzip',
        test: /\.(js|css|less)$/,
        threshold: 10240,
        minRatio: 0.8
      }),
      new webpack.optimize.LimitChunkCountPlugin({
        maxChunks: 20,
        minChunkSize: 30000
      }),
      new webpack.optimize.SplitChunksPlugin({
        name: ['manifest', 'vendor', 'runtime', 'fail', 'libs', 'utils', 'assets', 'operations', 'components', 'ant', 'dataCenter'],
        chunks: 'initial',
        minChunks: 10
      })
      // new webpack.optimize.AggressiveSplittingPlugin({
      //     minSize: 30000, // 字节，分割点。默认：30720
      //     maxSize: 50000, // 字节，每个文件最大字节。
      //     chunkOverhead: 0, // 默认：0
      //     entryChunkMultiplicator: 1
      // })
      // ,
    ] : []).concat(dllReferencePluginList),
    optimization: proMode ? {
      minimizer: [
        new UglifyJsPlugin({
          uglifyOptions: {
            warnings: false,
            // ɾ��console debugger
            compress: {
              drop_console: true, // console
              drop_debugger: true,
              collapse_vars: true,
              reduce_vars: true,
              pure_funcs: ['console.log', 'console.info', 'console.warn'] // �Ƴ�console
            },
            // ɾ��ע��
            output: {
              comments: false
            }
          },
          parallel: true,
          cache: true,
          extractComments: false
        })
      ],
      runtimeChunk: {
        name: 'manifest'
      }
      // splitChunks: {
      //   maxInitialRequests: 30,
      //   minSize: 30000,
      //   maxSize: 5 * 30000,
      //   cacheGroups: {
      //     vendors: {
      //       test: /[\\/]node_modules[\\/]/,
      //       priority: -10
      //     },
      //     default: {
      //       minChunks: 2,
      //       priority: -20,
      //       reuseExistingChunk: true
      //     },
      //     src: {
      //       chunks: 'all',
      //       test: /[\\/]src/,
      //       name: 'src',
      //       priority: 20,
      //       enforce: true,
      //       reuseExistingChunk: true
      //     },
      //     // utils: {
      //     //     chunks: 'initial',
      //     //     test: /[\\/]src[\\/]utils[\\/]/,
      //     //     name: 'utils',
      //     //     priority: 18,
      //     //     enforce: true,
      //     //     reuseExistingChunk: true
      //     // },
      //     // assets: {
      //     //     chunks: 'all',
      //     //     test: /[\\/]src[\\/]assets[\\/]/,
      //     //     name: 'assets',
      //     //     priority: 17,
      //     //     enforce: true,
      //     //     reuseExistingChunk: true
      //     // },
      //     // components: {
      //     //     chunks: 'all',
      //     //     test: /[\\/]src[\\/]components[\\/]/,
      //     //     name: 'components',
      //     //     priority: 16,
      //     //     enforce: true,
      //     //     reuseExistingChunk: true
      //     // },
      //     // manaGy: {
      //     //     chunks: 'all',
      //     //     test: /[\\/]src[\\/]views[\\/]mana_gy[\\/]/,
      //     //     name: 'manaGy',
      //     //     priority: 14,
      //     //     enforce: true,
      //     //     reuseExistingChunk: true
      //     // },
      //     // isolarerp: {
      //     //     chunks: 'all',
      //     //     test: /[\\/]src[\\/]views[\\/]isolarerp[\\/]/,
      //     //     name: 'isolarerp',
      //     //     priority: 13,
      //     //     enforce: true,
      //     //     reuseExistingChunk: true
      //     // },
      //     // dataCenter: {
      //     //     chunks: 'all',
      //     //     test: /[\\/]src[\\/]views[\\/]dataCenter[\\/]/,
      //     //     name: 'dataCenter',
      //     //     priority: 12,
      //     //     enforce: true,
      //     //     reuseExistingChunk: true
      //     // },
      //     // operations: {
      //     //     chunks: 'all',
      //     //     test: /[\\/]src[\\/]views[\\/]operations[\\/]/,
      //     //     name: 'operations',
      //     //     priority: 11,
      //     //     enforce: true,
      //     //     reuseExistingChunk: true
      //     // },
      //     commons: {
      //       name: 'commons',
      //       chunks: 'all',
      //       reuseExistingChunk: true
      //     }
      //   }
      // }
    } : {}
  },
  chainWebpack: (config) => {
    config.resolve.alias
      .set('@$', resolve('src'))
      .set('@api', resolve('src/api'))
      .set('@assets', resolve('src/assets'))
      .set('@comp', resolve('src/components'))
      .set('@views', resolve('src/views'))
      .set('@layout', resolve('src/layout'));
    // csshash
    if (proMode) {
      config.plugins.delete('prefetch');
      config.plugins.delete('preload');
    }
    // ����vxe-table�����es6���룬���IE11��������
    config.module
      .rule('vxe')
      .test(/\.js$/)
      .include
      .add(resolve('node_modules/vxe-table'))
      .add(resolve('node_modules/vxe-table-plugin-antd'))
      .end()
      .use()
      .loader('babel-loader').options({
        cacheDirectory: true
      })
      .end();
    config.module
      .rule('svg')
      .exclude.add(resolve('src/assets/icons'))
      .end();
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/assets/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })
      .end();
  },

  css: {
    loaderOptions: {
      less: {
        modifyVars: {
          /* less �������ǣ������Զ��� ant design ���� */
          'primary-color': '#FF8F33',
          'link-color': '#FF8F33',
          'border-radius-base': '4px'
        },
        javascriptEnabled: true
      }
    },
    extract: proMode ? {
      ignoreOrder: true
    } : false
  },
  devServer: {
    port: 3000,
    proxy: {
      '/solareye-boot': {
        target: 'https://api-dev.isolareye.com',
        ws: false,
        changeOrigin: true
      }
    }
  }
  // lintOnSave: undefined
};
