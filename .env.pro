NODE_ENV=production
VUE_APP_ENV=pro

VUE_APP_API_BASE_URL=http://172.16.100.100:30080/solareye-system
VUE_APP_CAS_BASE_URL=http://cas.example.org:8443/cas
/* VUE_APP_API_ERP_URL=http://192.168.158.126:8080 */
VUE_APP_API_ERP_URL=http://172.16.100.100:30080/isolar-erp
VUE_APP_API_MSG_URL=http://172.16.100.100:30080/solareye-msg
VUE_APP_API_CODE_URL=https://model.isolareye.com/app/app.png
VUE_APP_API_SEC_URL=http://172.16.100.100:30080/solareyetwo
VUE_APP_API_BI_URL=http://172.16.100.100:30080/isolarerpbi
# 跳转的web地址
VUE_APP_REPORT_WEB_BASE_URL=http://172.16.100.100:30080/data-report 
VUE_APP_AIGC_URL=https://aigc.isolareye.com/#/index
VUE_APP_AIGC_API_URL=http://172.16.100.100:30080/solareye-aigc
VUE_APP_Health_BASE_URL=http://172.16.100.100:30080/isolar-health
VUE_APP_BI_BASE_URL=http://172.16.100.100:30080/data-report
VUE_APP_DING_BASE_URL=http://172.16.100.100:30080/dingding

VUE_APP_API_SHOP_URL = http://172.16.100.100:30080/solareye-shop
VUE_APP_TANGO_BASE_URL=http://172.16.100.100:30080/solareyecare
VUE_APP_API_SHOP_URL = http://172.16.100.100:30080/solareye-shop
VUE_APP_OSS_FILE_URL=http://172.16.100.100:30080/solareyemc/oss/v1
VUE_APP_DM_BASE_URL=http://172.16.100.100:30080/solareyedm
VUE_APP_RDP_URL=http://172.16.100.100:30080/RDP-SERVER
VUE_APP_IOT_BASE_URL=http://172.16.100.100:30080/solareyeiot
# 报表跳转的web地址
VUE_APP_REPORT_WEB_BASE_URL=http://172.16.100.100:30080/data-report 
# 微应用列表必须VUE_APP_SUB_开头,solarCare为子应用的项目名称,也是子应用的路由父路径
VUE_APP_SUB_solarCare = '/solarCare/'
